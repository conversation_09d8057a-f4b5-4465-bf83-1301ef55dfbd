<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8fff8c47-8c5d-4e48-ae24-1294eb9ccb76" name="Changes" comment="v2.7 费用项目导入和下载模板逻辑修改">
      <change beforePath="$PROJECT_DIR$/som-consumer-rent/som-consumer-opt-rent/src/main/java/com/seewin/som/rent/service/impl/RentChargingStandardStoreServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/som-consumer-rent/som-consumer-opt-rent/src/main/java/com/seewin/som/rent/service/impl/RentChargingStandardStoreServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/som-consumer-rent/som-consumer-opt-rent/src/main/java/com/seewin/som/rent/vo/resp/RentChargingStandardStoreExcelTemplateItem.java" beforeDir="false" afterPath="$PROJECT_DIR$/som-consumer-rent/som-consumer-opt-rent/src/main/java/com/seewin/som/rent/vo/resp/RentChargingStandardStoreExcelTemplateItem.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/seewin-route" value="uat" />
        <entry key="$PROJECT_DIR$/som-commerce" value="master" />
        <entry key="$PROJECT_DIR$/som-consumer-commerce" value="prod" />
        <entry key="$PROJECT_DIR$/som-consumer-iot" value="master" />
        <entry key="$PROJECT_DIR$/som-consumer-rent" value="dev" />
        <entry key="$PROJECT_DIR$/som-consumer-report" value="master" />
        <entry key="$PROJECT_DIR$/som-consumer-space" value="master" />
        <entry key="$PROJECT_DIR$/som-executor" value="master" />
        <entry key="$PROJECT_DIR$/som-iot" value="master" />
        <entry key="$PROJECT_DIR$/som-rent" value="prod" />
        <entry key="$PROJECT_DIR$/som-report" value="prod" />
        <entry key="$PROJECT_DIR$/som-space" value="master" />
      </map>
    </option>
    <option name="RECENT_COMMON_BRANCH" value="uat" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/som-consumer-rent" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../jdk-17.0.10/lib/src.zip!/java.base/java/lang/Number.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="E:\JavaSpace\peizhi\apache-maven-3.5.4\apache-maven-3.5.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="explicitlyDisabledProfiles" value="nexus-profile" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2w1tYzbVzq42VOfUtTvRKPBr72y" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Notification.DisplayName-DoNotAsk-vcs.post.commit.checks.failed&quot;: &quot;Commit contains problems&quot;,
    &quot;Notification.DoNotAsk-vcs.post.commit.checks.failed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.GateWayApp.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SomCommerceMgtApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SomConsumerCusCommerceApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.SomConsumerEipCommerceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SomConsumerOptCommerceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SomConsumerOptRentApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SomRentMgtApplication.executor&quot;: &quot;Run&quot;,
    &quot;StatusDashboardGroupingRule&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/OpenWebUI&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Global Libraries&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.254023&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\project\som-consumer-rent\som-consumer-opt-rent\src\main\test\com\seewin\som" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.SomCommerceMgtApplication">
    <configuration name="CommerceContractTest.test2" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="som-consumer-opt-commerce" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.seewin.som.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.seewin.som" />
      <option name="MAIN_CLASS_NAME" value="com.seewin.som.CommerceContractTest" />
      <option name="METHOD_NAME" value="test2" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RentFeeActualTest.test1" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="som-consumer-opt-rent" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.seewin.som.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.seewin.som" />
      <option name="MAIN_CLASS_NAME" value="com.seewin.som.RentFeeActualTest" />
      <option name="METHOD_NAME" value="test1" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Test.test1" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="som-consumer-opt-rent" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.seewin.som.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.seewin.som" />
      <option name="MAIN_CLASS_NAME" value="com.seewin.som.Test" />
      <option name="METHOD_NAME" value="test1" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ConsumerOptReportApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-opt-report" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.ConsumerOptReportApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ConsumerOptSpaceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-opt-space" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.ConsumerOptSpaceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GateWayApp" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="seewin-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.GateWayApp" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobExecutorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-job-executor" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xxl.job.executor.JobExecutorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomCommerceMgtApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-commerce-mgt" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomCommerceMgtApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerCusCommerceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-cus-commerce" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerCusCommerceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerCusIotApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-cus-iot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerCusIotApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerCusRentApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-cus-rent" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerCusRentApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerCusReportApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-cus-report" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.SomConsumerCusReportApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerCusSpaceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-cus-space" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerCusSpaceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerDataIotApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-data-iot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerDataIotApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerEipCommerceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-eip-commerce" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerEipCommerceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerEipSpaceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-eip-space" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerEipSpaceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerEvtCommerceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-evt-commerce" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerEvtCommerceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerEvtRentApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-evt-rent" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.rent.SomConsumerEvtRentApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerOpenReportApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-open-report" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.SomConsumerOpenReportApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerOptCommerceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="som-consumer-opt-commerce" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerOptCommerceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.seewin.som.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerOptIotApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-opt-iot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerOptIotApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomConsumerOptRentApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-consumer-opt-rent" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomConsumerOptRentApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomIotMgtApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-iot-mgt" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomIotMgtApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomRentMgtApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-rent-mgt" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomRentMgtApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SomReportMgtApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-report-mgt" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.som.SomReportMgtApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SpaceMgtApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="som-space-mgt" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seewin.SpaceMgtApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.CommerceContractTest.test2" />
      <item itemvalue="JUnit.RentFeeActualTest.test1" />
      <item itemvalue="JUnit.Test.test1" />
      <item itemvalue="Spring Boot.SomConsumerEvtRentApplication" />
      <item itemvalue="Spring Boot.ConsumerOptReportApplication" />
      <item itemvalue="Spring Boot.ConsumerOptSpaceApplication" />
      <item itemvalue="Spring Boot.GateWayApp" />
      <item itemvalue="Spring Boot.JobExecutorApplication" />
      <item itemvalue="Spring Boot.SomCommerceMgtApplication" />
      <item itemvalue="Spring Boot.SomConsumerCusCommerceApplication" />
      <item itemvalue="Spring Boot.SomConsumerCusIotApplication" />
      <item itemvalue="Spring Boot.SomConsumerCusRentApplication" />
      <item itemvalue="Spring Boot.SomConsumerCusReportApplication" />
      <item itemvalue="Spring Boot.SomConsumerCusSpaceApplication" />
      <item itemvalue="Spring Boot.SomConsumerDataIotApplication" />
      <item itemvalue="Spring Boot.SomConsumerEipCommerceApplication" />
      <item itemvalue="Spring Boot.SomConsumerEipSpaceApplication" />
      <item itemvalue="Spring Boot.SomConsumerEvtCommerceApplication" />
      <item itemvalue="Spring Boot.SomConsumerOpenReportApplication" />
      <item itemvalue="Spring Boot.SomConsumerOptIotApplication" />
      <item itemvalue="Spring Boot.SomConsumerOptRentApplication" />
      <item itemvalue="Spring Boot.SomIotMgtApplication" />
      <item itemvalue="Spring Boot.SomRentMgtApplication" />
      <item itemvalue="Spring Boot.SomReportMgtApplication" />
      <item itemvalue="Spring Boot.SpaceMgtApplication" />
      <item itemvalue="Spring Boot.SomConsumerOptCommerceApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.SomConsumerOptCommerceApplication" />
        <item itemvalue="JUnit.Test.test1" />
        <item itemvalue="JUnit.RentFeeActualTest.test1" />
        <item itemvalue="JUnit.CommerceContractTest.test2" />
        <item itemvalue="Spring Boot.SomConsumerOptCommerceApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8fff8c47-8c5d-4e48-ae24-1294eb9ccb76" name="Changes" comment="" />
      <created>1745220960523</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745220960523</updated>
      <workItem from="1745220962543" duration="2326000" />
      <workItem from="1745475524396" duration="4648000" />
      <workItem from="1745545379565" duration="18000" />
      <workItem from="1745915508289" duration="3584000" />
      <workItem from="1746596693819" duration="4124000" />
      <workItem from="1746607598339" duration="7247000" />
      <workItem from="1746694383345" duration="7961000" />
      <workItem from="1746759887154" duration="283000" />
      <workItem from="1746760185873" duration="189000" />
      <workItem from="1746770042225" duration="308000" />
      <workItem from="1746770504423" duration="791000" />
      <workItem from="1747040557792" duration="3395000" />
      <workItem from="1747206850206" duration="742000" />
      <workItem from="1747379256928" duration="3958000" />
      <workItem from="1747733571668" duration="54006000" />
      <workItem from="1747961673452" duration="36828000" />
      <workItem from="1748249911037" duration="514000" />
      <workItem from="1748250448380" duration="529000" />
      <workItem from="1748251056056" duration="1436000" />
      <workItem from="1748315231840" duration="24316000" />
      <workItem from="1748496791304" duration="37000" />
      <workItem from="1748496854154" duration="6208000" />
      <workItem from="1748568976709" duration="6639000" />
      <workItem from="1749118332113" duration="4792000" />
      <workItem from="1749199626401" duration="309000" />
      <workItem from="1749435611107" duration="4499000" />
      <workItem from="1749440572951" duration="1667000" />
      <workItem from="1749447327958" duration="507000" />
      <workItem from="1749447854143" duration="6841000" />
      <workItem from="1749455176655" duration="5011000" />
      <workItem from="1749463425031" duration="3249000" />
      <workItem from="1749538901883" duration="784000" />
      <workItem from="1749610846093" duration="1264000" />
      <workItem from="1749626126171" duration="596000" />
      <workItem from="1749709598476" duration="1574000" />
      <workItem from="1749776709944" duration="21608000" />
      <workItem from="1750058446931" duration="2521000" />
      <workItem from="1750061772641" duration="2186000" />
      <workItem from="1750149671426" duration="1032000" />
      <workItem from="1750318115985" duration="1258000" />
      <workItem from="1750387161367" duration="9583000" />
      <workItem from="1750406414444" duration="1635000" />
      <workItem from="1750657467524" duration="1017000" />
      <workItem from="1750661620697" duration="2513000" />
      <workItem from="1750750378881" duration="1464000" />
      <workItem from="1750831104843" duration="1896000" />
      <workItem from="1750838402154" duration="687000" />
      <workItem from="1750840980644" duration="2279000" />
      <workItem from="1750916367821" duration="313000" />
      <workItem from="1751418171559" duration="732000" />
      <workItem from="1751425752941" duration="60000" />
      <workItem from="1751442025831" duration="1207000" />
      <workItem from="1751451296417" duration="20146000" />
      <workItem from="1751534261742" duration="8864000" />
      <workItem from="1751601320917" duration="13011000" />
      <workItem from="1751620203811" duration="2928000" />
      <workItem from="1751623550219" duration="634000" />
      <workItem from="1751850717815" duration="1684000" />
      <workItem from="1751853033525" duration="373000" />
      <workItem from="1751853634715" duration="83000" />
      <workItem from="1751853737817" duration="112000" />
      <workItem from="1751853890070" duration="564000" />
      <workItem from="1751854615734" duration="935000" />
      <workItem from="1751855582648" duration="4809000" />
      <workItem from="1751860759804" duration="10013000" />
      <workItem from="1751940481566" duration="18943000" />
      <workItem from="1752022791067" duration="7964000" />
      <workItem from="1752050224906" duration="2417000" />
    </task>
    <task id="LOCAL-00001" summary="v2.3 功能开发">
      <option name="closed" value="true" />
      <created>1746698334533</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1746698334533</updated>
    </task>
    <task id="LOCAL-00002" summary="v2.4 提交代码1">
      <option name="closed" value="true" />
      <created>1747968291163</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747968291163</updated>
    </task>
    <task id="LOCAL-00003" summary="v2.4 提交代码2">
      <option name="closed" value="true" />
      <created>1747968334918</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747968334918</updated>
    </task>
    <task id="LOCAL-00004" summary="v2.4 提交代码2">
      <option name="closed" value="true" />
      <created>1747970002216</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1747970002216</updated>
    </task>
    <task id="LOCAL-00005" summary="v2.4 提交代码3">
      <option name="closed" value="true" />
      <created>1747971919762</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1747971919762</updated>
    </task>
    <task id="LOCAL-00006" summary="v2.4 提交代码">
      <option name="closed" value="true" />
      <created>1747989707598</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1747989707598</updated>
    </task>
    <task id="LOCAL-00007" summary="v2.4 提交代码">
      <option name="closed" value="true" />
      <created>1747990265093</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1747990265093</updated>
    </task>
    <task id="LOCAL-00008" summary="v2.4 提交代码">
      <option name="closed" value="true" />
      <created>1748317155161</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1748317155161</updated>
    </task>
    <task id="LOCAL-00009" summary="v2.4 提交代码">
      <option name="closed" value="true" />
      <created>1748317159371</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1748317159371</updated>
    </task>
    <task id="LOCAL-00010" summary="v2.4 提交代码">
      <option name="closed" value="true" />
      <created>1748317518082</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1748317518082</updated>
    </task>
    <task id="LOCAL-00011" summary="v2.4 提交代码">
      <option name="closed" value="true" />
      <created>1748317969859</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1748317969859</updated>
    </task>
    <task id="LOCAL-00012" summary="v2.4 修复bug">
      <option name="closed" value="true" />
      <created>1748396737657</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1748396737657</updated>
    </task>
    <task id="LOCAL-00013" summary="v2.4 修复bug">
      <option name="closed" value="true" />
      <created>1748498261300</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1748498261300</updated>
    </task>
    <task id="LOCAL-00014" summary="v2.4 修复bug">
      <option name="closed" value="true" />
      <created>1748570388042</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1748570388042</updated>
    </task>
    <task id="LOCAL-00015" summary="v2.5 新增项目地址字段">
      <option name="closed" value="true" />
      <created>1749439904931</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1749439904931</updated>
    </task>
    <task id="LOCAL-00016" summary="v2.5 新增项目地址字段">
      <option name="closed" value="true" />
      <created>1749457927382</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1749457927382</updated>
    </task>
    <task id="LOCAL-00017" summary="v2.5 bugfix">
      <option name="closed" value="true" />
      <created>1749785634281</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1749785634281</updated>
    </task>
    <task id="LOCAL-00018" summary="v2.5 bugfix">
      <option name="closed" value="true" />
      <created>1749787020250</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1749787020250</updated>
    </task>
    <task id="LOCAL-00019" summary="v2.5 bugfix">
      <option name="closed" value="true" />
      <created>1749796749948</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1749796749948</updated>
    </task>
    <task id="LOCAL-00020" summary="v2.5 bugfix">
      <option name="closed" value="true" />
      <created>1749800318470</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1749800318470</updated>
    </task>
    <task id="LOCAL-00021" summary="v2.6 带看台账和导出">
      <option name="closed" value="true" />
      <created>1750402078631</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1750402078631</updated>
    </task>
    <task id="LOCAL-00022" summary="v2.7 下载导入模板">
      <option name="closed" value="true" />
      <created>1751536410253</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1751536410253</updated>
    </task>
    <task id="LOCAL-00023" summary="v2.7 费用项目导入">
      <option name="closed" value="true" />
      <created>1751620279362</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1751620279363</updated>
    </task>
    <task id="LOCAL-00024" summary="v2.7 费用项目导入">
      <option name="closed" value="true" />
      <created>1751621934230</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1751621934230</updated>
    </task>
    <task id="LOCAL-00025" summary="v2.7 费用项目导入">
      <option name="closed" value="true" />
      <created>1751854215299</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1751854215299</updated>
    </task>
    <task id="LOCAL-00026" summary="v2.7 费用项目导入-日志">
      <option name="closed" value="true" />
      <created>1751854897853</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1751854897853</updated>
    </task>
    <task id="LOCAL-00027" summary="v2.7 费用项目导入-日志">
      <option name="closed" value="true" />
      <created>1751856056819</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1751856056819</updated>
    </task>
    <task id="LOCAL-00028" summary="v2.7 费用项目导入和下载模板修改">
      <option name="closed" value="true" />
      <created>1751868643307</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1751868643307</updated>
    </task>
    <task id="LOCAL-00029" summary="v2.7 费用项目导入和下载模板修改">
      <option name="closed" value="true" />
      <created>1751873638643</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1751873638643</updated>
    </task>
    <task id="LOCAL-00030" summary="v2.7 费用项目导入和下载模板逻辑修改">
      <option name="closed" value="true" />
      <created>1751957173198</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1751957173198</updated>
    </task>
    <task id="LOCAL-00031" summary="v2.7 费用项目导入和下载模板逻辑修改">
      <option name="closed" value="true" />
      <created>1751963697378</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1751963697378</updated>
    </task>
    <task id="LOCAL-00032" summary="v2.7 费用项目导入和下载模板逻辑修改">
      <option name="closed" value="true" />
      <created>1752023142691</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1752023142691</updated>
    </task>
    <option name="localTasksCounter" value="33" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:org.apache.dubbo:dubbo" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.grpc:grpc-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/uat" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="陈俊" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="v2.3 功能开发" />
    <MESSAGE value="v2.4 提交代码1" />
    <MESSAGE value="v2.4 提交代码2" />
    <MESSAGE value="v2.4 提交代码3" />
    <MESSAGE value="v2.4 提交代码" />
    <MESSAGE value="v2.4 修复bug" />
    <MESSAGE value="v2.5 新增项目地址字段" />
    <MESSAGE value="v2.5 bugfix" />
    <MESSAGE value="v2.6 带看台账和导出" />
    <MESSAGE value="v2.7 下载导入模板" />
    <MESSAGE value="v2.7 费用项目导入" />
    <MESSAGE value="v2.7 费用项目导入-日志" />
    <MESSAGE value="v2.7 费用项目导入和下载模板修改" />
    <MESSAGE value="v2.7 费用项目导入和下载模板逻辑修改" />
    <option name="LAST_COMMIT_MESSAGE" value="v2.7 费用项目导入和下载模板逻辑修改" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/som-consumer-commerce/som-consumer-cus-commerce/src/main/java/com/seewin/som/commerce/controller/CommerceOpenWebController.java</url>
          <line>61</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>