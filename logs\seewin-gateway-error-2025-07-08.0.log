{"appName":"seewin-gateway","time":"2025-07-08 14:34:03","level":"ERROR","class":"com.seewin.gateway.config.GlobalExceptionHandler","method":"handle","line":"46","message":"网关异常全局处理，异常信息：503 SERVICE_UNAVAILABLE \"Unable to find instance for som-consumer-adm-system\"","statck_trace":""}
{"appName":"seewin-gateway","time":"2025-07-08 14:35:47","level":"ERROR","class":"com.seewin.gateway.filter.OptFilter","method":"filter","line":"198","message":"凭证过期","statck_trace":"io.jsonwebtoken.ExpiredJwtException: JWT expired at 2025-07-08T01:13:00Z. Current time: 2025-07-08T06:35:47Z, a difference of 19367865 milliseconds.  Allowed clock skew: 0 milliseconds.\r\n\tat io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:427)\r\n\tat io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529)\r\n\tat io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589)\r\n\tat io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173)\r\n\tat com.seewin.redis.util.JwtUtil.parserToken(JwtUtil.java:42)\r\n\tat com.seewin.redis.handler.TokenHandler.parseToken(TokenHandler.java:67)\r\n\tat com.seewin.gateway.filter.OptFilter.filter(OptFilter.java:145)\r\n\tat com.seewin.gateway.factory.AuthFilterGatewayFilterFactory.lambda$apply$0(AuthFilterGatewayFilterFactory.java:91)\r\n\tat org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)\r\n\tat org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:117)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)\r\n\tat reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:258)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:863)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)\r\n\tat reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:258)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:863)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:136)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.request(MonoFilterWhen.java:182)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:100)\r\n\tat reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:206)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:98)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:44)\r\n\tat reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:335)\r\n\tat reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:127)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerComplete(FluxConcatMapNoPrefetch.java:274)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onComplete(FluxConcatMap.java:887)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.innerResult(MonoFilterWhen.java:216)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onNext(MonoFilterWhen.java:332)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onNext(MonoFilterWhen.java:290)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onSubscribe(MonoFilterWhen.java:313)\r\n\tat reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:193)\r\n\tat reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:146)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.request(MonoFilterWhen.java:182)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:338)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:338)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)\r\n\tat reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)\r\n\tat reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)\r\n\tat reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1169)\r\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:710)\r\n\tat reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:481)\r\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:652)\r\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:238)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\r\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\r\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\r\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\r\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\r\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\n"}
