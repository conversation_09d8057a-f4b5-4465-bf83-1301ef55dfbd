package com.seewin.som.commerce.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 广告位合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
@Setter
public class CommerceAdvertContractEditDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同类型（0-广告位、1-多经点位）
     */
    private Integer advertContractType;

    /**
     * 签约类型：1 新签，2 续签
     */
    private Integer contractType;

    /**
     * 广告多经ID
     */
    private Long advertId;

    /**
     * 广告多经编号
     */
    private String advertName;

    /**
     * 多经点位类型
     */
    private Integer advertType;

    /**
     * 场地使用面积（m2）
     */
    private BigDecimal advertArea;

    /**
     * 甲方
     */
    private String lessor;

    /**
     * 甲方法定代表人
     */
    private String lessorRepresentative;

    /**
     * 甲方地址
     */
    private String lessorAddress;

    /**
     * 第三方
     */
    private String thirdParty;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 身份证地址/公司营业执照地址
     */
    private String supplierCertificateAddress;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人方式（号码）
     */
    private String contactPhon;

    /**
     * 身份证号码/统一信用代码
     */
    private String supplierCertificateCode;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 签约品牌名称
     */
    private String brandName;

    /**
     * 签约品牌业态字典code
     */
    private Long brandCommercialTypeCode;

    /**
     * 签约品牌业态名称
     */
    private String brandCommercialTypeName;

    /**
     * 签约品牌一级品类
     */
    private Long brandCategoryId;

    /**
     * 签约品牌一级品类名称
     */
    private String brandCategoryName;

    /**
     * 签约主体：1公司，2个人
     */
    private Integer signEntity;

    /**
     * 场地用途
     */
    private String siteUse;

    /**
     * 租赁起始时间
     */
    private LocalDate rentStartDate;

    /**
     * 租赁结束时间
     */
    private LocalDate rentEndDate;

    /**
     * 租赁类型：1 纯租、2-纯扣
     */
    private Integer rentType;

    /**
     * 运营管理费收取方式(0-月,1-季度,2-半年,3-年,4-定价)
     */
    private Integer operatePricePayType;

    /**
     * 扣点百分率
     */
    private BigDecimal monthFeePercent;

    /**
     * 运营管理费单价（小写）（元）
     */
    private BigDecimal monthOperatePriceContract;

    /**
     * 运营管理费总额（小写）（元）
     */
    private BigDecimal monthOperateFeeContract;

    /**
     * 费用支付方式(0-按月支付,1-一次性支付,2-其他支付方式)
     */
    private Integer feePricePayType;

    /**
     * 一次性支付日期
     */
    private LocalDate firstPayDate;

    /**
     * 其他支付方式说明
     */
    private String otherRemark;

    /**
     * 电费收取方式 广告位-(0-运营管理费已包含使用期限内广告位电费，乙方无需另行支付,1-乙方按照目前商业用电的收费标准向甲方交纳电费,2-该场地无电表) 多经点-（0-乙方自行安装独立水、电表并承担相应费用，水费、电费、基础设施服务费等公共事业收费标准按照甲方收费政策执行。1-场地无水电表。2-乙方缴纳的营运管理费已包含水电费，无需另行缴纳。）
     */
    private Integer electricityPayType;

    /**
     * 电费标准（元/月）
     */
    private BigDecimal electricityPrice;

    /**
     * 首次电费支付日期
     */
    private LocalDate electricityPayDate;

    /**
     * 首次电费周期开始日期
     */
    private LocalDate electricityStartDate;

    /**
     * 首次电费周期结束日期
     */
    private LocalDate electricityEndDate;

    /**
     * 首次水电费（元）
     */
    private BigDecimal electricityFee;

    /**
     * 保证金收取方式 广告位-(0-合同签订之日起三日内由乙方一次性支付给甲方。,1-乙方在上期《广告位使用协议》（或《场地租赁合同》）签订时已缴纳保证金)多经点位-（0-乙方应当于签订本协议当日内一次性缴清本协议约定的营运管理费保证金、公共事业保证。1-乙方在上期《临促协议》签订时已缴纳营运管理费保证金、公共事业保证金，自动转为本期《临促协议》的营运管理费保证金）
     */
    private String bailPayType;

    /**
     * 运营管理费保证金（元）
     */
    private BigDecimal operateManageBailFeeContract;

    /**
     * 公共事业保证金（元）
     */
    private BigDecimal commonBailFeeContract;

    /**
     * 上期已缴纳运营管理费保证金（元）
     */
    private BigDecimal operateManageBailFeeLast;

    /**
     * 上期已缴纳公共事业保证金（元）
     */
    private BigDecimal commonBailFeeLast;

    /**
     * 一次性支付运营管理费总费用（元）
     */
    private BigDecimal firstPayOperateFee;

    /**
     * 审批状态: 2 审批中，3 已通过，4 已驳回
     */
    private Integer approveStatus;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 审批时间
     */
    private LocalDate approveDate;

    /**
     * 合同归档时间
     */
    private LocalDate archiveDate;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 项目地址
     */
    private String faddressdetail;

    /**
     * 保证金总额
     */
    private BigDecimal securityDepositTotalAmount;

    /**
     * 第三方公司
     */
    private String thirdPartyCompany;
}
