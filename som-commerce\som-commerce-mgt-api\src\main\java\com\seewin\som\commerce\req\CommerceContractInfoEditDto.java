package com.seewin.som.commerce.req;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招商合同信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Getter
@Setter
public class CommerceContractInfoEditDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 签约品牌名称
     */
    private String brandName;

    /**
     * 签约品牌业态字典code
     */
    private Long brandCommercialTypeCode;

    /**
     * 签约品牌业态名称
     */
    private String brandCommercialTypeName;

    /**
     * 签约品牌一级品类
     */
    private Long brandCategoryId;

    /**
     * 签约品牌一级品类名称
     */
    private String brandCategoryName;

    /**
     * 签约品牌经营范围
     */
    private String businessScope;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 身份证号码/统一信用代码
     */
    private String supplierCertificateCode;

    /**
     * 身份证地址/公司营业执照地址
     */
    private String supplierCertificateAddress;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人方式（号码）
     */
    private String contactPhon;

    /**
     * 供应商质量事件
     */
    private Integer supplierQuality;

    /**
     * 供应商安全事故
     */
    private Integer supplierSecurity;

    /**
     * 供应商违约事件
     */
    private Integer supplierViolate;

    /**
     * 签约主体：1公司，2个人
     */
    private Integer signEntity;

    /**
     * 签约类型：1 新签，2 续签
     */
    private Integer contractType;

    /**
     * 经营模式：1 品牌直营，2 品牌代理，3 品牌加盟，4 个体经营
     */
    private Integer businessType;

    /**
     * 是否重新装修：0 否 ，1 是
     */
    private Integer decoration;

    /**
     * 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
     */
    private Integer rentType;

    /**
     * 租赁起始时间
     */
    private LocalDate rentStartDate;

    /**
     * 租赁结束时间
     */
    private LocalDate rentEndDate;

    /**
     * 商户交付日期
     */
    private LocalDate deliveryDate;

    /**
     * 实际交付日期
     */
    private LocalDate actDeliveryDate;

    /**
     * 商户计租日期
     */
    private LocalDate rentFeeDate;

    /**
     * 商户开业日期
     */
    private LocalDate openDate;

    /**
     * 实际开业日期
     */
    private LocalDate actOpenDate;

    /**
     * 计费截止日期
     */
    private LocalDate actRetriveDate;

     /**
     * 商铺类型：0：正铺；1：临促
     */
    private Integer shopType;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 甲方
     */
    private String lessor;
    /**
     * 甲方法定代表人
     */
    private String lessorRepresentative;
    /**
     * 甲方地址
     */
    private String lessorAddress;
    /**
     * 第三方
     */
    private String thirdParty;
    /**
     * 月运营管理费递增方式,0-比例递增，1-金额递增  2 不递增
     */
    private Integer operateIncreaseMode;
    /**
     * 月物业管理费递增方式,0-比例递增 1-金额递增  2-不递增
     */
    private Integer manageIncreaseMode;

    /**
     * API接口费首期款支付时间
     */
    private LocalDateTime apiInitialPaymentTime;

    /**
     * API接口费期数
     */
    private Integer apiFeeInstallments;

    /**
     * API接口费每期款项支付时间
     */
    private String apiInstallmentPaymentTime;

    /**
     * API接口费尾期款支付时间
     */
    private LocalDateTime apiFinalPaymentTime;

    /**
     * 产品合作开始时间
     */
    private LocalDateTime productCooperationStartTime;

    /**
     * 产品合作结束时间
     */
    private LocalDateTime productCooperationEndTime;
}
