package com.seewin.som.commerce.resp;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招商合同表-商铺
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Getter
@Setter
public class CommerceContractGetVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 工单编号
     */
    private String orderCode;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 审批状态: 2 审批中，3 已通过，4 已驳回
     */
    private Integer approveStatus;

    /**
     * 招商传签申请日期
     */
    private LocalDate applyDate;

    /**
     * 审批时间
     */
    private LocalDate approveDate;

    /**
     * 合同归档时间
     */
    private LocalDate archiveDate;

    /**
     * 驳回意见
     */
    private String approveRemark;

    /**
     * 招商传签人账号
     */
    private String executeUser;

    /**
     * 招商传签人姓名
     */
    private String executeUserName;

    /**
     * 招商传签人所属组织编码
     */
    private String executeUserFid;

    /**
     * 招商传签人所属组织名称
     */
    private String executeUserFname;

    /**
     * 空间完整编码
     */
    private String fcode;

    /**
     * 空间完整名称
     */
    private String fname;

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 房间名称
     */
    private String name;

    /**
     * 门店ID
     */
    private String roomShopId;

    /**
     * 商铺实用面积
     */
    private Double actualArea;

    /**
     * 商铺记租面积
     */
    private Double rentArea;

    /**
     * 铺位状态：1空置，2在营，3已签约（字典值）
     */
    private Integer roomStatus;

    /**
     * 空置开始时间
     */
    private LocalDate emptyStartDate;

    /**
     * (上次)合同到期时间
     */
    private LocalDate lastContractDate;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 签约品牌名称
     */
    private String brandName;

    /**
     * 适配业态字典id
     */
    private Long commercialTypeCode;

    /**
     * 适配业态名称
     */
    private String commercialTypeName;

    /**
     * 所属一级品类
     */
    private Long categoryId;

    /**
     * 所属一级品类名称
     */
    private String categoryName;

    /**
     * 电量
     */
    private Integer electricity;

    /**
     * 给水
     */
    private Integer waterSupply;

    /**
     * 排水
     */
    private Integer drainage;

    /**
     * 排油烟量
     */
    private Integer exhaustFumes;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private CommerceContractInfoGetVo contractInfo;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 数据来源 0 手动新增 1 初始化
     */
    private Integer dataType;

    /**
     * 用户输入
     */
    private String userInput;
    /**
     * 项目地址
     */
    private String faddressdetail;

    /**
     * 合同模板类型, 0-普通模板 1-科技模板 2-小程序模板
     */
    private Integer templateType;

}
