package com.seewin.som.commerce.resp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招商合同打印表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@Setter
public class CommerceContractPrintGetVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同模板ID
     */
    private Long contractTemplateId;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 模板类型（0-word，1-excel）
     */
    private Integer templateType;

    /**
     * 合同属性（0 合同，1附件协议）
     */
    private Integer contractAttr;

    /**
     * 合同类型（0 租赁合同）
     */
    private String contractType;

    /**
     * 合同类型名称
     */
    private String contractTypeName;

    /**
     * 合同属性
     */
    private String contractAttrName;
    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 单据号
     */
    private String invoiceNo;

    /**
     * 合同登记(备案)号
     */
    private String registerNo;

    /**
     * 甲方(出租方)
     */
    private String lessor;

    /**
     * 甲方(出租方)法定代表人
     */
    private String lessorRepresentative;

    /**
     * 甲方(出租方)住所
     */
    private String lessorAddress;

    /**
     * 乙方(承租方)
     */
    private String renter;

    /**
     * 乙方(承租方)法定代表人
     */
    private String renterRepresentative;

    /**
     * 乙方(承租方)住所
     */
    private String renterAddress;

    /**
     * 商铺所在城市
     */
    private String roomCity;

    /**
     * 商铺所在地址
     */
    private String roomAddress;

    /**
     * 铺位号
     */
    private String roomName;

    /**
     * 商铺建筑面积
     */
    private Double roomBuildArea;

    /**
     * 租期(月份)
     */
    private Integer rentPeriod;

    /**
     * 租赁起始时间
     */
    private LocalDate rentStartDate;

    /**
     * 租赁截止时间
     */
    private LocalDate rentEndDate;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 经营品牌业态字典code
     */
    private Long brandCommercialTypeCode;

    /**
     * 经营品牌业态名称
     */
    private String brandCommercialTypeName;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 月租金单价
     */
    private BigDecimal monthPriceContract;

    /**
     * 月租金总额（元）
     */
    private BigDecimal monthFeeContract;

    /**
     * 租金递增起始年
     */
    private BigDecimal incrementalStartContract;

    /**
     * 租金递增率(%)
     */
    private String incrementalRateContract;
    /**
     * 递增率集合
     */
    private String incrementalRateList;

    /**
     * 租赁保证金
     */
    private BigDecimal rentBailFeeContract;

    /**
     * 公共事业保证金
     */
    private BigDecimal commonBailFeeContract;

    /**
     * 甲方账户名称(核算组织名称)
     */
    private String accountingOrgName;

    /**
     * 甲方银行名称
     */
    private String bankAccountName;

    /**
     * 甲方银行账号编码
     */
    private String bankAccountCode;

    /**
     * 商户交付日期
     */
    private LocalDate deliveryDate;

    /**
     * 装修免租期(天数)
     */
    private Integer feeFreeContract;

    /**
     * 商户开业日期
     */
    private LocalDate openDate;

    /**
     * 装修押金(元)
     */
    private BigDecimal decorationBailFeeContract;

    /**
     * POS机设备总价款(元)
     */
    private BigDecimal posFee;

    /**
     * 通知信息-甲方(出租方)
     */
    private String lessorNotify;

    /**
     * 通知信息-甲方(出租方)地址
     */
    private String lessorNotifyAddress;

    /**
     * 通知信息-乙方(承租方)
     */
    private String renterNotify;

    /**
     * 通知信息-乙方(承租方)电话
     */
    private String renterNotifyPhone;

    /**
     * 通知信息-乙方(承租方)地址
     */
    private String renterNotifyAddress;

    /**
     * 通知信息-乙方(承租方)邮箱
     */
    private String renterNotifyEmail;

    /**
     * 通知信息-乙方(承租方)联系人
     */
    private String renterNotifyContactName;

    /**
     * 通知信息-乙方(承租方)联系方式
     */
    private String renterNotifyContactPhone;

    /**
     * 标准明细
     */
    private String standardDetail;

    /**
     * 甲方银行账号
     */
    private String bankAccount;

    /**
     * 第三方
     */
    private String thirdparty;

    /**
     * 丙方
     */
    private String interparty;

    /**
     * 首期预付租管费
     */
    private BigDecimal prepRentFee;

    /**
     * 楼层名称
     */
    private String floorName;

    /**
     * 功能
     */
    private String functionName;

    /**
     * 甲方(出租方)邮箱
     */
    private String lessorEmail;

    /**
     * 甲方(出租方)联系人
     */
    private String lessorContactName;

    /**
     * 甲方(出租方)联系方式
     */
    private String lessorContactPhone;

    /**
     * 乙方(承租方)身份证号
     */
    private String renterCardNumber;

    /**
     * 月运营管理费单价
     */
    private BigDecimal monthOperatePriceContract;

    /**
     * 月运营管理费总额
     */
    private BigDecimal monthOperateFeeContract;

    /**
     * 月物业管理费单价
     */
    private BigDecimal monthManagePriceContract;

    /**
     * 月物业管理费总额（元）
     */
    private BigDecimal monthManageFeeContract;

    /**
     * 综合管理费
     */
    private BigDecimal compositeManageFeeContract;

    /**
     * 运营运维使用费预付天数
     */
    private Integer prepPayDays;

    /**
     * 预付运营平台软硬件使用费
     */
    private BigDecimal prepOperateFee;

    /**
     * 预付运维平台软硬件使用费
     */
    private BigDecimal prepMaintainFee;

    /**
     * 物业管理费保证金
     */
    private BigDecimal propertyManageBailFeeContract;

    /**
     * 运营管理费保证金
     */
    private BigDecimal operateManageBailFeeContract;

    /**
     * 其他费用单价
     */
    private BigDecimal otherPriceContract;

    /**
     * 其他服务费用（元）
     */
    private BigDecimal otherFeeContract;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 租赁类型
     */
    private Integer rentTypeInt;
    /**
     * 租金递增方式,0-固定比例，1-固定金额
     */
    private Integer rentIncreaseMode;

    private LocalDate payStopDate;
    /**
     * 装修期运营管理费（元）
     */
    private BigDecimal decorationOperateFee;
    /**
     * 装修期物业管理费（元）
     */
    private BigDecimal decorationPropertyFee;
    /*
     * 广告位多经点位编号
     */
    private String advertName;

    /*
     * 面积长字符
     */
    private String advertAreaStr;

    /*
     * 场地使用面积（m2）
     */
    private BigDecimal advertArea;

    /*
     * 场地用途
     */
    private String siteUse;

    /*
     * 运营管理费收取方式(0-月,1-季度,2-半年,3-年,4-定价)
     */
    private Integer operatePricePayType;

    /*
     * 运营管理费单价（大写）（圆）
     */
    private String operatePriceContractUpper;

    /*
     * 运营管理费总额（大写）（圆）
     */
    private String operateFeeContractUpper;

    /*
     * 费用支付方式(0-按月支付,1-一次性支付,2-其他支付方式)
     */
    private Integer feePricePayType;

    /*
     * 其他支付方式说明
     */
    private String otherRemark;

    /*
     * 电费收取方式 广告位-(0-运营管理费已包含使用期限内广告位电费，乙方无需另行支付,1-乙方按照目前商业用电的收费标准向甲方交纳电费,2-该场地无电表) 多经点-（0-乙方自行安装独立水、电表并承担相应费用，水费、电费、基础设施服务费等公共事业收费标准按照甲方收费政策执行。1-场地无水电表。2-乙方缴纳的营运管理费已包含水电费，无需另行缴纳。）
     */
    private Integer electricityPayType;

    /*
     * 电费标准（元/月）
     */
    private BigDecimal electricityPrice;

    /*
     * 首次电费支付日期
     */
    private LocalDate electricityPayDate;

    /*
     * 首次电费周期开始日期
     */
    private LocalDate electricityStartDate;

    /*
     * 首次电费周期结束日期
     */
    private LocalDate electricityEndDate;

    /*
     * 首次水电费（元）
     */
    private BigDecimal electricityFee;

    /*
     * 运营管理费保证金月数（个）
     */
    private Integer bailMonth;

    /*
     * 保证金收取方式 广告位-(0-合同签订之日起三日内由乙方一次性支付给甲方。,1-乙方在上期《广告位使用协议》（或《场地租赁合同》）签订时已缴纳保证金)多经点位-（0-乙方应当于签订本协议当日内一次性缴清本协议约定的营运管理费保证金、公共事业保证。1-乙方在上期《临促协议》签订时已缴纳营运管理费保证金、公共事业保证金，自动转为本期《临促协议》的营运管理费保证金）
     */
    private String bailPayType;

    /*
     * 上期已缴纳运营管理费保证金（元）
     */
    private BigDecimal operateManageBailFeeLast;

    /*
     * 租期（天）
     */
    private Integer rentPeriodDay;

    /*
     * 租赁类型：1 纯租、2-纯扣
     */
    private String rentType;

    /*
     * 一次性支付日期
     */
    private LocalDate firstPayDate;

    /*
     * 上期已缴纳公共事业保证金（元）
     */
    private BigDecimal commonBailFeeLast;

    /*
     * 运营管理费保证金、公共事业保证金总额（元）
     */
    private BigDecimal bailTotalFee;

    /*
     * 身份证号码/统一信用代码
     */
    private String supplierCertificateCode;

    /*
     * 合同签署方式  0- 单方签署 1-平台自身与个人用户签署  2-平台自身与企业用户签署
     */
    private Integer signType;


    /*
     * 甲方签章关键词
     */
    private String lessorKeyword;


    /*
     * 乙方签章关键词
     */
    private String renterKeyword;


    /*
     * 签署流程ID
     */
    private String signFlowId;


    /*
     * 签署状态  0-未签署  1-签署中  2-已签署  3-拒签
     */
    private Integer signStatus;


    /*
     * 已签署文件的下载链接
     */
    private String signedDownloadUrl;


    /*
     * 用章公司
     */
    private Integer signCompanyCode;

    /*
     * 印章类别
     */
    private Integer sealTypeCode;


    /*
     * 是否乙方盖章   后改成了单方签署形式  0 甲方签署  1 乙方签署（个人）  2 乙方签署（企业）
     */
    private Integer isRenterSeal;

    /*
     * 是否乙方先盖章
     */
    private Integer isRenterSealFirst;

    /*
     * 是否盖骑缝章
     */
    private Integer isCrossPageSeal;


    /*
     * 乙方企业名称
     */
    private String renterCompanyName;


    /*
     * 乙方企业统一社会信用代码
     */
    private String renterCompanyCode;


    /*
     * 供应商名称
     */
    private String supplierName;


    /*
     * 属于同一份合同的文件共有的唯一ID
     */
    private String sameContractId;

    /*
     * 签署文件Id
     */
    private String fileId;
    /*
     * 项目地址
     */
    private String faddressdetail;

    /**
     * 保证金总额
     */
    private BigDecimal securityDepositTotalAmount;

    /**
     * 第三方公司
     */
    private String thirdPartyCompany;

    /**
     * API接口费首期款支付时间
     */
    private LocalDate apiInitialPaymentTime;

    /**
     * API接口费期数
     */
    private Integer apiFeeInstallments;

    /**
     * API接口费每期款项支付时间
     */
    private String apiInstallmentPaymentTime;

    /**
     * API接口费尾期款支付时间
     */
    private LocalDate apiFinalPaymentTime;

    /**

     * 产品合作起始时间
     */
    private LocalDate productCooperationStartTime;

    /**
     * 产品合作截止时间
     */
    private LocalDate productCooperationEndTime;

    /**
     * API接口费
     */
    private BigDecimal apiFee;

    /**
     * API接口费总款项
     */
    private BigDecimal apiFeeTotalAmount;

    /**
     * API接口费首期款
     */
    private BigDecimal apiInitialPaymentAmount;

    /**
     * API接口费每期款项
     */
    private BigDecimal apiInstallmentAmount;

    /**
     * API接口费尾期款
     */
    private BigDecimal apiFinalPaymentAmount;

    /**
     * 产品保证金
     */
    private BigDecimal productSecurityDeposit;

}
