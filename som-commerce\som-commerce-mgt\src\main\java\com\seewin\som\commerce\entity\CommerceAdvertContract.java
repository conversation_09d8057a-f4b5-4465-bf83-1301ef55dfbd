package com.seewin.som.commerce.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 广告位合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("som_commerce_advert_contract")
public class CommerceAdvertContract implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 合同编号
     */
    @TableField("contract_code")
    private String contractCode;

    /**
     * 合同类型（0-广告位、1-多经点位）
     */
    @TableField("advert_contract_type")
    private Integer advertContractType;

    /**
     * 签约类型：1 新签，2 续签
     */
    @TableField("contract_type")
    private Integer contractType;

    /**
     * 广告多经ID
     */
    @TableField("advert_id")
    private Long advertId;

    /**
     * 广告多经编号
     */
    @TableField("advert_name")
    private String advertName;

    /**
     * 多经点位类型
     */
    @TableField("advert_type")
    private Integer advertType;

    /**
     * 场地使用面积（m2）
     */
    @TableField("advert_area")
    private BigDecimal advertArea;

    /**
     * 甲方
     */
    @TableField("lessor")
    private String lessor;

    /**
     * 甲方法定代表人
     */
    @TableField("lessor_representative")
    private String lessorRepresentative;

    /**
     * 甲方地址
     */
    @TableField("lessor_address")
    private String lessorAddress;

    /**
     * 第三方
     */
    @TableField("third_party")
    private String thirdParty;

    /**
     * 供应商ID
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 身份证地址/公司营业执照地址
     */
    @TableField("supplier_certificate_address")
    private String supplierCertificateAddress;

    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人方式（号码）
     */
    @TableField("contact_phon")
    private String contactPhon;

    /**
     * 身份证号码/统一信用代码
     */
    @TableField("supplier_certificate_code")
    private String supplierCertificateCode;

    /**
     * 品牌ID
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 签约品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 签约品牌业态字典code
     */
    @TableField("brand_commercial_type_code")
    private Long brandCommercialTypeCode;

    /**
     * 签约品牌业态名称
     */
    @TableField("brand_commercial_type_name")
    private String brandCommercialTypeName;

    /**
     * 签约品牌一级品类
     */
    @TableField("brand_category_id")
    private Long brandCategoryId;

    /**
     * 签约品牌一级品类名称
     */
    @TableField("brand_category_name")
    private String brandCategoryName;

    /**
     * 签约主体：1公司，2个人
     */
    @TableField("sign_entity")
    private Integer signEntity;

    /**
     * 场地用途
     */
    @TableField("site_use")
    private String siteUse;

    /**
     * 租赁起始时间
     */
    @TableField("rent_start_date")
    private LocalDate rentStartDate;

    /**
     * 租赁结束时间
     */
    @TableField("rent_end_date")
    private LocalDate rentEndDate;

    /**
     * 租赁类型：1 纯租、2-纯扣
     */
    @TableField("rent_type")
    private Integer rentType;

    /**
     * 运营管理费收取方式(0-月,1-季度,2-半年,3-年,4-定价)
     */
    @TableField("operate_price_pay_type")
    private Integer operatePricePayType;

    /**
     * 扣点百分率
     */
    @TableField("month_fee_percent")
    private BigDecimal monthFeePercent;

    /**
     * 运营管理费单价（小写）（元）
     */
    @TableField("month_operate_price_contract")
    private BigDecimal monthOperatePriceContract;

    /**
     * 运营管理费总额（小写）（元）
     */
    @TableField("month_operate_fee_contract")
    private BigDecimal monthOperateFeeContract;

    /**
     * 费用支付方式(0-按月支付,1-一次性支付,2-其他支付方式)
     */
    @TableField("fee_price_pay_type")
    private Integer feePricePayType;

    /**
     * 一次性支付日期
     */
    @TableField("first_pay_date")
    private LocalDate firstPayDate;

    /**
     * 其他支付方式说明
     */
    @TableField("other_remark")
    private String otherRemark;

    /**
     * 电费收取方式 广告位-(0-运营管理费已包含使用期限内广告位电费，乙方无需另行支付,1-乙方按照目前商业用电的收费标准向甲方交纳电费,2-该场地无电表) 多经点-（0-乙方自行安装独立水、电表并承担相应费用，水费、电费、基础设施服务费等公共事业收费标准按照甲方收费政策执行。1-场地无水电表。2-乙方缴纳的营运管理费已包含水电费，无需另行缴纳。）
     */
    @TableField("electricity_pay_type")
    private Integer electricityPayType;

    /**
     * 电费标准（元/月）
     */
    @TableField("electricity_price")
    private BigDecimal electricityPrice;

    /**
     * 首次电费支付日期
     */
    @TableField("electricity_pay_date")
    private LocalDate electricityPayDate;

    /**
     * 首次电费周期开始日期
     */
    @TableField("electricity_start_date")
    private LocalDate electricityStartDate;

    /**
     * 首次电费周期结束日期
     */
    @TableField("electricity_end_date")
    private LocalDate electricityEndDate;

    /**
     * 首次水电费（元）
     */
    @TableField("electricity_fee")
    private BigDecimal electricityFee;

    /**
     * 保证金收取方式 广告位-(0-合同签订之日起三日内由乙方一次性支付给甲方。,1-乙方在上期《广告位使用协议》（或《场地租赁合同》）签订时已缴纳保证金)多经点位-（0-乙方应当于签订本协议当日内一次性缴清本协议约定的营运管理费保证金、公共事业保证。1-乙方在上期《临促协议》签订时已缴纳营运管理费保证金、公共事业保证金，自动转为本期《临促协议》的营运管理费保证金）
     */
    @TableField("bail_pay_type")
    private String bailPayType;

    /**
     * 运营管理费保证金（元）
     */
    @TableField("operate_manage_bail_fee_contract")
    private BigDecimal operateManageBailFeeContract;

    /**
     * 公共事业保证金（元）
     */
    @TableField("common_bail_fee_contract")
    private BigDecimal commonBailFeeContract;

    /**
     * 上期已缴纳运营管理费保证金（元）
     */
    @TableField("operate_manage_bail_fee_last")
    private BigDecimal operateManageBailFeeLast;

    /**
     * 上期已缴纳公共事业保证金（元）
     */
    @TableField("common_bail_fee_last")
    private BigDecimal commonBailFeeLast;

    /**
     * 一次性支付运营管理费总费用（元）
     */
    @TableField("first_pay_operate_fee")
    private BigDecimal firstPayOperateFee;

    /**
     * 审批状态: 2 审批中，3 已通过，4 已驳回
     */
    @TableField("approve_status")
    private Integer approveStatus;

    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;

    /**
     * 审批时间
     */
    @TableField("approve_date")
    private LocalDate approveDate;

    /**
     * 合同归档时间
     */
    @TableField("archive_date")
    private LocalDate archiveDate;

    /**
     * 项目地址
     */
    @TableField("f_address_detail")
    private String faddressdetail;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 第三方公司
     */
    @TableField("third_party_company")
    private String thirdPartyCompany;

    /**
     * 保证金总额
     */
    @TableField("security_deposit_total_amount")
    private BigDecimal securityDepositTotalAmount;

    /**
     * 合同模板类型, 0-普通模板 1-科技模板
     */
    @TableField("template_type")
    private Integer templateType;
}
