package com.seewin.som.commerce.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 招商合同表-商铺
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@TableName("som_commerce_contract")
public class CommerceContract implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 工单编号
     */
    @TableField("order_code")
    private String orderCode;

    /**
     * 子工单ID
     */
    @TableField("sub_order_id")
    private Long subOrderId;

    /**
     * 合同编号
     */
    @TableField("contract_code")
    private String contractCode;

    /**
     * 审批状态: 2 审批中，3 已通过，4 已驳回
     */
    @TableField("approve_status")
    private Integer approveStatus;

    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;

    /**
     * 招商传签申请日期
     */
    @TableField("apply_date")
    private LocalDate applyDate;

    /**
     * 审批时间
     */
    @TableField("approve_date")
    private LocalDate approveDate;

    /**
     * 合同归档时间
     */
    @TableField("archive_date")
    private LocalDate archiveDate;

    /**
     * 驳回意见
     */
    @TableField("approve_remark")
    private String approveRemark;

    /**
     * 招商传签人账号
     */
    @TableField("execute_user")
    private String executeUser;

    /**
     * 招商传签人姓名
     */
    @TableField("execute_user_name")
    private String executeUserName;

    /**
     * 招商传签人所属组织编码
     */
    @TableField("execute_user_fid")
    private String executeUserFid;

    /**
     * 招商传签人所属组织名称
     */
    @TableField("execute_user_fname")
    private String executeUserFname;

    /**
     * 空间完整编码
     */
    @TableField("fcode")
    private String fcode;

    /**
     * 空间完整名称
     */
    @TableField("fname")
    private String fname;

    /**
     * 房间ID
     */
    @TableField("room_id")
    private Long roomId;

    /**
     * 房间名称
     */
    @TableField("name")
    private String name;

    /**
     * 门店ID
     */
    @TableField("room_shop_id")
    private String roomShopId;

    /**
     * 商铺实用面积
     */
    @TableField("actual_area")
    private Double actualArea;

    /**
     * 商铺记租面积
     */
    @TableField("rent_area")
    private Double rentArea;

    /**
     * 铺位状态：1空置，2在营，3已签约（字典值）
     */
    @TableField("room_status")
    private Integer roomStatus;

    /**
     * 空置开始时间
     */
    @TableField("empty_start_date")
    private LocalDate emptyStartDate;

    /**
     * (上次)合同到期时间
     */
    @TableField("last_contract_date")
    private LocalDate lastContractDate;

    /**
     * 品牌ID
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 签约品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 适配业态字典id
     */
    @TableField("commercial_type_code")
    private Long commercialTypeCode;

    /**
     * 适配业态名称
     */
    @TableField("commercial_type_name")
    private String commercialTypeName;

    /**
     * 所属一级品类
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 所属一级品类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 电量
     */
    @TableField("electricity")
    private Integer electricity;

    /**
     * 给水
     */
    @TableField("water_supply")
    private Integer waterSupply;

    /**
     * 排水
     */
    @TableField("drainage")
    private Integer drainage;

    /**
     * 排油烟量
     */
    @TableField("exhaust_fumes")
    private Integer exhaustFumes;

    /**
     * 数据来源 0 手动新增 1 初始化
     */
    @TableField("data_type")
    private Integer dataType;

    @TableField("user_input")
    private String userInput;

    @TableField("f_address_detail")
    private String faddressdetail;
    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 合同模板类型, 0-普通模板 1-科技模板 2-小程序模板
     */
    @TableField("template_type")
    private Integer templateType;

}
