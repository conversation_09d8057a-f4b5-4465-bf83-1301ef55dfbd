package com.seewin.som.commerce.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 招商合同信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@TableName("som_commerce_contract_info")
public class CommerceContractInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 合同编号
     */
    @TableField("contract_code")
    private String contractCode;

    /**
     * 品牌ID
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 签约品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 签约品牌业态字典code
     */
    @TableField("brand_commercial_type_code")
    private Long brandCommercialTypeCode;

    /**
     * 签约品牌业态名称
     */
    @TableField("brand_commercial_type_name")
    private String brandCommercialTypeName;

    /**
     * 签约品牌一级品类
     */
    @TableField("brand_category_id")
    private Long brandCategoryId;

    /**
     * 签约品牌一级品类名称
     */
    @TableField("brand_category_name")
    private String brandCategoryName;

    /**
     * 签约品牌经营范围
     */
    @TableField("business_scope")
    private String businessScope;

    /**
     * 供应商ID
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 身份证号码/统一信用代码
     */
    @TableField("supplier_certificate_code")
    private String supplierCertificateCode;

    /**
     * 身份证地址/公司营业执照地址
     */
    @TableField("supplier_certificate_address")
    private String supplierCertificateAddress;

    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人方式（号码）
     */
    @TableField("contact_phon")
    private String contactPhon;

    /**
     * 供应商质量事件
     */
    @TableField("supplier_quality")
    private Integer supplierQuality;

    /**
     * 供应商安全事故
     */
    @TableField("supplier_security")
    private Integer supplierSecurity;

    /**
     * 供应商违约事件
     */
    @TableField("supplier_violate")
    private Integer supplierViolate;

    /**
     * 签约主体：1公司，2个人
     */
    @TableField("sign_entity")
    private Integer signEntity;

    /**
     * 签约类型：1 新签，2 续签
     */
    @TableField("contract_type")
    private Integer contractType;

    /**
     * 经营模式：1 品牌直营，2 品牌代理，3 品牌加盟，4 个体经营
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 是否重新装修：0 否 ，1 是
     */
    @TableField("decoration")
    private Integer decoration;

    /**
     * 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
     */
    @TableField("rent_type")
    private Integer rentType;

    /**
     * 租赁起始时间
     */
    @TableField("rent_start_date")
    private LocalDate rentStartDate;

    /**
     * 租赁结束时间
     */
    @TableField("rent_end_date")
    private LocalDate rentEndDate;

    /**
     * 商户交付日期
     */
    @TableField("delivery_date")
    private LocalDate deliveryDate;

    /**
     * 实际交付日期
     */
    @TableField("act_delivery_date")
    private LocalDate actDeliveryDate;

    /**
     * 商户计租日期
     */
    @TableField("rent_fee_date")
    private LocalDate rentFeeDate;

    /**
     * 商户开业日期
     */
    @TableField("open_date")
    private LocalDate openDate;

    /**
     * 实际开业日期
     */
    @TableField("act_open_date")
    private LocalDate actOpenDate;

    /**
     * 计费截止日期
     */
    @TableField("act_retrive_date")
    private LocalDate actRetriveDate;

    /**
     * 扣点方式，0-固定比例，1-阶梯变化，2-年份变化，只有当租赁类型为纯扣、保底扣（租金）、保底扣（租金+管理费）时才有该字段
     */
    @TableField("points_mode")
    private Integer pointsMode;


    /**
     * 结算日期，扣点方式只有当租赁类型为纯扣、保底扣（租金）、保底扣（租金+管理费）时才有该字段
     */
    @TableField("settlement_date")
    private Integer settlementDate;

    /**
     * 租金递增方式,0-固定比例，1-固定金额,递增方式为【固定比例】时，租赁信息中的字段为【租金年递增率（%），递增方式为【固定金额】时，租赁信息中的字段为【租金年递增（元）】
     */
    @TableField("rent_increase_mode")
    private Integer rentIncreaseMode;

    /**
     * 商铺类型：0：正铺；1：临促
     */
    @TableField("shop_type")
    private Integer shopType;



    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;
    /**
     * 甲方
     */
    @TableField( "lessor")
    private String lessor;
    /**
     *甲方法定代表人
     */
    @TableField( "lessor_representative")
    private String lessorRepresentative;
    /**
     * 甲方地址
     */
    @TableField( "lessor_address")
    private String lessorAddress;
    /**
     * 第三方
     */
    @TableField( "third_party")
    private String thirdParty;
    /**
     * 月运营管理费递增方式,0-比例递增，1-金额递增  2 不递增
     */
    @TableField( "operate_increase_mode")
    private Integer operateIncreaseMode;
    /**
     * 月物业管理费递增方式,0-比例递增 1-金额递增  2-不递增
     */
    @TableField( "manage_increase_mode")
    private Integer manageIncreaseMode;

    /**
     * API接口费首期款支付时间
     */
    @TableField("api_initial_payment_time")
    private LocalDateTime apiInitialPaymentTime;

    /**
     * API接口费期数
     */
    @TableField("api_fee_installments")
    private Integer apiFeeInstallments;

    /**
     * API接口费每期款项支付时间
     */
    @TableField("api_installment_payment_time")
    private String apiInstallmentPaymentTime;

    /**
     * API接口费尾期款支付时间
     */
    @TableField("api_final_payment_time")
    private LocalDateTime apiFinalPaymentTime;

    /**
     * 产品合作开始时间
     */
    @TableField("product_cooperation_start_time")
    private LocalDateTime productCooperationStartTime;

    /**
     * 产品合作结束时间
     */
    @TableField("product_cooperation_end_time")
    private LocalDateTime productCooperationEndTime;
}
