package com.seewin.som.commerce.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 招商合同打印表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@TableName("som_commerce_contract_print")
public class CommerceContractPrint implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 合同编号
     */
    @TableField("contract_code")
    private String contractCode;

    /**
     * 合同模板ID
     */
    @TableField("contract_template_id")
    private Long contractTemplateId;
    /**
     * 模板名称
     */
    @TableField("template_name")
    private String templateName;
    /**
     * 模板类型（0-word，1-excel）
     */
    @TableField("template_type")
    private Integer templateType;

    /**
     * 合同属性（0 合同，1附件协议）
     */
    @TableField("contract_attr")
    private Integer contractAttr;

    /**
     * 合同类型（0 租赁合同）
     */
    @TableField("contract_type")
    private String contractType;

    /**
     * 合同类型名称
     */
    @TableField("contract_type_name")
    private String contractTypeName;

    /**
     * 合同属性
     */
    @TableField("contract_attr_name")
    private String contractAttrName;
    /**
     * 合同号
     */
    @TableField("contract_no")
    private String contractNo;

    /**
     * 单据号
     */
    @TableField("invoice_no")
    private String invoiceNo;

    /**
     * 合同登记(备案)号
     */
    @TableField("register_no")
    private String registerNo;

    /**
     * 甲方(出租方)
     */
    @TableField("lessor")
    private String lessor;

    /**
     * 甲方(出租方)法定代表人
     */
    @TableField("lessor_representative")
    private String lessorRepresentative;

    /**
     * 甲方(出租方)住所
     */
    @TableField("lessor_address")
    private String lessorAddress;

    /**
     * 乙方(承租方)
     */
    @TableField("renter")
    private String renter;

    /**
     * 乙方(承租方)法定代表人
     */
    @TableField("renter_representative")
    private String renterRepresentative;

    /**
     * 乙方(承租方)住所
     */
    @TableField("renter_address")
    private String renterAddress;

    /**
     * 商铺所在城市
     */
    @TableField("room_city")
    private String roomCity;

    /**
     * 商铺所在地址
     */
    @TableField("room_address")
    private String roomAddress;

    /**
     * 铺位号
     */
    @TableField("room_name")
    private String roomName;

    /**
     * 商铺建筑面积
     */
    @TableField("room_build_area")
    private Double roomBuildArea;

    /**
     * 租期(月份)
     */
    @TableField("rent_period")
    private Integer rentPeriod;

    /**
     * 租赁起始时间
     */
    @TableField("rent_start_date")
    private LocalDate rentStartDate;

    /**
     * 租赁截止时间
     */
    @TableField("rent_end_date")
    private LocalDate rentEndDate;

    /**
     * 品牌ID
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 经营品牌业态字典code
     */
    @TableField("brand_commercial_type_code")
    private Long brandCommercialTypeCode;

    /**
     * 经营品牌业态名称
     */
    @TableField("brand_commercial_type_name")
    private String brandCommercialTypeName;

    /**
     * 经营范围
     */
    @TableField("business_scope")
    private String businessScope;

    /**
     * 月租金单价
     */
    @TableField("month_price_contract")
    private BigDecimal monthPriceContract;

    /**
     * 月租金总额（元）
     */
    @TableField("month_fee_contract")
    private BigDecimal monthFeeContract;

    /**
     * 租金递增起始年
     */
    @TableField("incremental_start_contract")
    private BigDecimal incrementalStartContract;

    /**
     * 租金递增率(%)
     */
    @TableField("incremental_rate_contract")
    private String incrementalRateContract;

    /**
     * 租赁保证金
     */
    @TableField("rent_bail_fee_contract")
    private BigDecimal rentBailFeeContract;

    /**
     * 公共事业保证金
     */
    @TableField("common_bail_fee_contract")
    private BigDecimal commonBailFeeContract;

    /**
     * 甲方账户名称(核算组织名称)
     */
    @TableField("accounting_org_name")
    private String accountingOrgName;

    /**
     * 甲方银行名称
     */
    @TableField("bank_account_name")
    private String bankAccountName;

    /**
     * 甲方银行账号编码
     */
    @TableField("bank_account_code")
    private String bankAccountCode;

    /**
     * 商户交付日期
     */
    @TableField("delivery_date")
    private LocalDate deliveryDate;

    /**
     * 装修免租期(天数)
     */
    @TableField("fee_free_contract")
    private Integer feeFreeContract;

    /**
     * 商户开业日期
     */
    @TableField("open_date")
    private LocalDate openDate;

    /**
     * 装修押金(元)
     */
    @TableField("decoration_bail_fee_contract")
    private BigDecimal decorationBailFeeContract;

    /**
     * POS机设备总价款(元)
     */
    @TableField("pos_fee")
    private BigDecimal posFee;

    /**
     * 通知信息-甲方(出租方)
     */
    @TableField("lessor_notify")
    private String lessorNotify;

    /**
     * 通知信息-甲方(出租方)地址
     */
    @TableField("lessor_notify_address")
    private String lessorNotifyAddress;

    /**
     * 通知信息-乙方(承租方)
     */
    @TableField("renter_notify")
    private String renterNotify;

    /**
     * 通知信息-乙方(承租方)电话
     */
    @TableField("renter_notify_phone")
    private String renterNotifyPhone;

    /**
     * 通知信息-乙方(承租方)地址
     */
    @TableField("renter_notify_address")
    private String renterNotifyAddress;

    /**
     * 通知信息-乙方(承租方)邮箱
     */
    @TableField("renter_notify_email")
    private String renterNotifyEmail;

    /**
     * 通知信息-乙方(承租方)联系人
     */
    @TableField("renter_notify_contact_name")
    private String renterNotifyContactName;

    /**
     * 通知信息-乙方(承租方)联系方式
     */
    @TableField("renter_notify_contact_phone")
    private String renterNotifyContactPhone;

    /**
     * 标准明细
     */
    @TableField("standard_detail")
    private String standardDetail;


    /**
     * 甲方银行账号
     */
    @TableField("bank_account")
    private String bankAccount;

    /**
     * 第三方
     */
    @TableField("thirdparty")
    private String thirdparty;

    /**
     * 丙方
     */
    @TableField("interparty")
    private String interparty;

    /**
     * 首期预付租管费
     */
    @TableField("prep_rent_fee")
    private BigDecimal prepRentFee;

    /**
     * 楼层名称
     */
    @TableField("floor_name")
    private String floorName;

    /**
     * 功能
     */
    @TableField("function_name")
    private String functionName;

    /**
     * 甲方(出租方)邮箱
     */
    @TableField("lessor_email")
    private String lessorEmail;

    /**
     * 甲方(出租方)联系人
     */
    @TableField("lessor_contact_name")
    private String lessorContactName;

    /**
     * 甲方(出租方)联系方式
     */
    @TableField("lessor_contact_phone")
    private String lessorContactPhone;

    /**
     * 乙方(承租方)身份证号
     */
    @TableField("renter_card_number")
    private String renterCardNumber;

    /**
     * 月物业管理费单价
     */
    @TableField("month_operate_price_contract")
    private BigDecimal monthOperatePriceContract;

    /**
     * 月物业管理费总额（元）
     */
    @TableField("month_operate_fee_contract")
    private BigDecimal monthOperateFeeContract;

    /**
     * 月运营管理费单价
     */
    @TableField("month_manage_price_contract")
    private BigDecimal monthManagePriceContract;

    /**
     * 月运营管理费总额（元）
     */
    @TableField("month_manage_fee_contract")
    private BigDecimal monthManageFeeContract;

    /**
     * 综合管理费
     */
    @TableField("composite_manage_fee_contract")
    private BigDecimal compositeManageFeeContract;

    /**
     * 运营运维使用费预付天数
     */
    @TableField("prep_pay_days")
    private Integer prepPayDays;

    /**
     * 预付运营平台软硬件使用费
     */
    @TableField("prep_operate_fee")
    private BigDecimal prepOperateFee;

    /**
     * 预付运维平台软硬件使用费
     */
    @TableField("prep_maintain_fee")
    private BigDecimal prepMaintainFee;

    /**
     * 物业管理费保证金
     */
    @TableField("property_manage_bail_fee_contract")
    private BigDecimal propertyManageBailFeeContract;

    /**
     * 运营管理费保证金
     */
    @TableField("operate_manage_bail_fee_contract")
    private BigDecimal operateManageBailFeeContract;

    /**
     * 其他费用单价
     */
    @TableField("other_price_contract")
    private BigDecimal otherPriceContract;

    /**
     * 其他服务费用（元）
     */
    @TableField("other_fee_contract")
    private BigDecimal otherFeeContract;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 管理费支付停止日期
     */
    @TableField("pay_stop_date")
    private LocalDate payStopDate;

    /*
     * 广告位多经点位编号
     */
    @TableField("advert_name")
    private String advertName;

    /*
     * 面积长字符
     */
    @TableField("advert_area_str")
    private String advertAreaStr;

    /*
     * 场地使用面积（m2）
     */
    @TableField("advert_area")
    private BigDecimal advertArea;

    /*
     * 场地用途
     */
    @TableField("site_use")
    private String siteUse;

    /*
     * 运营管理费收取方式(0-月,1-季度,2-半年,3-年,4-定价)
     */
    @TableField("operate_price_pay_type")
    private Integer operatePricePayType;

    /*
     * 运营管理费单价（大写）（圆）
     */
    @TableField("operate_price_contract_upper")
    private String operatePriceContractUpper;

    /*
     * 运营管理费总额（大写）（圆）
     */
    @TableField("operate_fee_contract_upper")
    private String operateFeeContractUpper;

    /*
     * 费用支付方式(0-按月支付,1-一次性支付,2-其他支付方式)
     */
    @TableField("fee_price_pay_type")
    private Integer feePricePayType;

    /*
     * 其他支付方式说明
     */
    @TableField("other_remark")
    private String otherRemark;

    /*
     * 电费收取方式 广告位-(0-运营管理费已包含使用期限内广告位电费，乙方无需另行支付,1-乙方按照目前商业用电的收费标准向甲方交纳电费,2-该场地无电表) 多经点-（0-乙方自行安装独立水、电表并承担相应费用，水费、电费、基础设施服务费等公共事业收费标准按照甲方收费政策执行。1-场地无水电表。2-乙方缴纳的营运管理费已包含水电费，无需另行缴纳。）
     */
    @TableField("electricity_pay_type")
    private Integer electricityPayType;

    /*
     * 电费标准（元/月）
     */
    @TableField("electricity_price")
    private BigDecimal electricityPrice;

    /*
     * 首次电费支付日期
     */
    @TableField("electricity_pay_date")
    private LocalDate electricityPayDate;

    /*
     * 首次电费周期开始日期
     */
    @TableField("electricity_start_date")
    private LocalDate electricityStartDate;

    /*
     * 首次电费周期结束日期
     */
    @TableField("electricity_end_date")
    private LocalDate electricityEndDate;

    /*
     * 首次水电费（元）
     */
    @TableField("electricity_fee")
    private BigDecimal electricityFee;

    /*
     * 运营管理费保证金月数（个）
     */
    @TableField("bail_month")
    private Integer bailMonth;

    /*
     * 保证金收取方式 广告位-(0-合同签订之日起三日内由乙方一次性支付给甲方。,1-乙方在上期《广告位使用协议》（或《场地租赁合同》）签订时已缴纳保证金)多经点位-（0-乙方应当于签订本协议当日内一次性缴清本协议约定的营运管理费保证金、公共事业保证。1-乙方在上期《临促协议》签订时已缴纳营运管理费保证金、公共事业保证金，自动转为本期《临促协议》的营运管理费保证金）
     */
    @TableField("bail_pay_type")
    private String bailPayType;

    /*
     * 上期已缴纳运营管理费保证金（元）
     */
    @TableField("operate_manage_bail_fee_last")
    private BigDecimal operateManageBailFeeLast;

    /*
     * 租期（天）
     */
    @TableField("rent_period_day")
    private Integer rentPeriodDay;

    /*
     * 租赁类型：1 纯租、2-纯扣
     */
    @TableField("rent_type")
    private String rentType;

    /*
     * 一次性支付日期
     */
    @TableField("first_pay_date")
    private LocalDate firstPayDate;

    /*
     * 上期已缴纳公共事业保证金（元）
     */
    @TableField("common_bail_fee_last")
    private BigDecimal commonBailFeeLast;

    /*
     * 运营管理费保证金、公共事业保证金总额（元）
     */
    @TableField("bail_total_fee")
    private BigDecimal bailTotalFee;

    /*
     * 身份证号码/统一信用代码
     */
    @TableField("supplier_certificate_code")
    private String supplierCertificateCode;

    /*
     * 合同签署方式  0-平台自身与个人用户签署 1-平台自身与企业用户签署 2-企业用户与个人用户签署
     */
    @TableField("sign_type")
    private Integer signType;

    /*
     * 甲方签章关键词
     */
    @TableField("lessor_keyword")
    private String lessorKeyword;


    /*
     * 乙方签章关键词
     */
    @TableField("renter_keyword")
    private String renterKeyword;


    /*
     * 签署流程ID
     */
    @TableField("sign_flow_id")
    private String signFlowId;


    /*
     * 签署状态
     */
    @TableField("sign_status")
    private Integer signStatus;


    /*
     * 已签署文件的下载链接
     */
    @TableField("signed_download_url")
    private String signedDownloadUrl;



    /*
     * 用章公司
     */
    @TableField("sign_company_code")
    private Integer signCompanyCode;

    /*
     * 印章类别
     */
    @TableField("seal_type_code")
    private Integer sealTypeCode;



    /*
     * 是否乙方盖章
     */
    @TableField("is_renter_seal")
    private Integer isRenterSeal;

    /*
     * 是否乙方先盖章
     */
    @TableField("is_renter_seal_first")
    private Integer isRenterSealFirst;

    /*
     * 是否盖骑缝章
     */
    @TableField("is_cross_page_seal")
    private Integer isCrossPageSeal;


    /*
     * 乙方企业名称
     */
    @TableField("renter_company_name")
    private String renterCompanyName;


    /*
     * 乙方企业统一社会信用代码
     */
    @TableField("renter_company_code")
    private String renterCompanyCode;

    /*
     * 属于同一份合同的文件共有的唯一ID
     */
    @TableField("same_contract_id")
    private String sameContractId;


    /*
     * 签署文件Id
     */
    @TableField("file_id")
    private String fileId;

    /*
     * 项目地址
     */
    @TableField("f_address_detail")
    private String faddressdetail;

    /**
     * 保证金总额
     */
    @TableField("security_deposit_total_amount")
    private BigDecimal securityDepositTotalAmount;

    /**
     * 第三方公司
     */
    @TableField("third_party_company")
    private String thirdPartyCompany;

    /**
     * API接口费首期款支付时间
     */
    @TableField("api_initial_payment_time")
    private LocalDate apiInitialPaymentTime;

    /**
     * API接口费期数
     */
    @TableField("api_fee_installments")
    private Integer apiFeeInstallments;

    /**
     * API接口费每期款项支付时间
     */
    @TableField("api_installment_payment_time")
    private String apiInstallmentPaymentTime;

    /**
     * API接口费尾期款支付时间
     */
    @TableField("api_final_payment_time")
    private LocalDate apiFinalPaymentTime;

    /**

     * 产品合作起始时间
     */
    @TableField("product_cooperation_start_time")
    private LocalDate productCooperationStartTime;

    /**
     * 产品合作截止时间
     */
    @TableField("product_cooperation_end_time")
    private LocalDate productCooperationEndTime;

    /**
     * API接口费
     */
    @TableField("api_fee")
    private BigDecimal apiFee;

    /**
     * API接口费总款项
     */
    @TableField("api_fee_total_amount")
    private BigDecimal apiFeeTotalAmount;

    /**
     * API接口费首期款
     */
    @TableField("api_initial_payment_amount")
    private BigDecimal apiInitialPaymentAmount;

    /**
     * API接口费每期款项
     */
    @TableField("api_installment_amount")
    private BigDecimal apiInstallmentAmount;

    /**
     * API接口费尾期款
     */
    @TableField("api_final_payment_amount")
    private BigDecimal apiFinalPaymentAmount;

    /**
     * 产品保证金
     */
    @TableField("product_security_deposit")
    private BigDecimal productSecurityDeposit;

}
