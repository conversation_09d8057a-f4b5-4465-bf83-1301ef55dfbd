package com.seewin.som.commerce.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <p>
 * 招商合同租赁信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("som_commerce_contract_rent")
public class CommerceContractRent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 合同编号
     */
    @TableField("contract_code")
    private String contractCode;

    /**
     * 签约年限（年）
     */
    @TableField("year_limit")
    private Integer yearLimit;

    /**
     * 签约年限（年）
     */
    @TableField("year_limit_contract")
    private String yearLimitContract;

    /**
     * 签约年限（年）是否符合：0 否，1 是
     */
    @TableField("year_limit_match")
    private Integer yearLimitMatch;

    /**
     * 月租金单价（元/㎡）
     */
    @TableField("month_price")
    private BigDecimal monthPrice;

    /**
     * 月租金单价
     */
    @TableField("month_price_contract")
    private BigDecimal monthPriceContract;

    /**
     * 月租金总额（元）
     */
    @TableField("month_fee_contract")
    private BigDecimal monthFeeContract;

    /**
     * 租金递增集合
     */
    @TableField("incremental_rate_list")
    private String incrementalRateList;

    /**
     * 扣点百分率集合
     */
    @TableField("month_fee_percent_list")
    private String monthFeePercentList;

    /**
     * 扣点百分率
     */
    @TableField("month_fee_percent")
    private Double monthFeePercent;

    /**
     * 月物业管理费单价（元/㎡）
     */
    @TableField("month_manage_price")
    private BigDecimal monthManagePrice;

    /**
     * 月物业管理费单价
     */
    @TableField("month_manage_price_contract")
    private BigDecimal monthManagePriceContract;

    /**
     * 月物业管理费单价是否符合：0 否，1 是
     */
    @TableField("month_manage_price_match")
    private Integer monthManagePriceMatch;

    /**
     * 月物业管理费总额（元）
     */
    @TableField("month_manage_fee_contract")
    private BigDecimal monthManageFeeContract;

    /**
     * 月运营管理费单价（元/㎡）
     */
    @TableField("month_operate_price")
    private BigDecimal monthOperatePrice;

    /**
     * 月运营管理费单价
     */
    @TableField("month_operate_price_contract")
    private BigDecimal monthOperatePriceContract;

    /**
     * 月运营管理费单价是否符合：0 否，1 是
     */
    @TableField("month_operate_price_match")
    private Integer monthOperatePriceMatch;

    /**
     * 月运营管理费总额（元）
     */
    @TableField("month_operate_fee_contract")
    private BigDecimal monthOperateFeeContract;

    /**
     * 其他费用单价（元/㎡）
     */
    @TableField("other_price")
    private BigDecimal otherPrice;

    /**
     * 其他费用单价
     */
    @TableField("other_price_contract")
    private BigDecimal otherPriceContract;

    /**
     * 其他费用单价是否符合：0 否，1 是
     */
    @TableField("other_price_match")
    private Integer otherPriceMatch;

    /**
     * 其他费用总额（元）
     */
    @TableField("other_fee_contract")
    private BigDecimal otherFeeContract;

    /**
     * 租金年递增率（%）
     */
    @TableField("incremental_rate")
    private BigDecimal incrementalRate;

    /**
     * 租金递增起年
     */
    @TableField("incremental_start_contract")
    private BigDecimal incrementalStartContract;

    /**
     * 租金递增间隔
     */
    @TableField("incremental_interval_contract")
    private BigDecimal incrementalIntervalContract;

    /**
     * 租金年递增率（%）
     */
    @TableField("incremental_rate_contract")
    private BigDecimal incrementalRateContract;

    /**
     * 租金年递增率是否符合：0 否，1 是
     */
    @TableField("incremental_rate_match")
    private Integer incrementalRateMatch;

    /**
     * 装修免租期（天）
     */
    @TableField("fee_free")
    private Integer feeFree;

    /**
     * 装修免租期
     */
    @TableField("fee_free_contract")
    private Integer feeFreeContract;

    /**
     * 装修免租期是否符合：0 否，1 是
     */
    @TableField("fee_free_match")
    private Integer feeFreeMatch;

    /**
     * 综合管理费（元）
     */
    @TableField("composite_manage_fee")
    private BigDecimal compositeManageFee;

    /**
     * 综合管理费
     */
    @TableField("composite_manage_fee_contract")
    private BigDecimal compositeManageFeeContract;

    /**
     * 综合管理费是否符合：0 否，1 是
     */
    @TableField("composite_manage_fee_match")
    private Integer compositeManageFeeMatch;

    /**
     * 租赁保证金（元）
     */
    @TableField("rent_bail_fee")
    private BigDecimal rentBailFee;

    /**
     * 租赁保证金
     */
    @TableField("rent_bail_fee_contract")
    private BigDecimal rentBailFeeContract;

    /**
     * 租赁保证金是否符合：0 否，1 是
     */
    @TableField("rent_bail_fee_match")
    private Integer rentBailFeeMatch;

    /**
     * 物业管理费保证金（元）
     */
    @TableField("property_manage_bail_fee")
    private BigDecimal propertyManageBailFee;

    /**
     * 物业管理费保证金
     */
    @TableField("property_manage_bail_fee_contract")
    private BigDecimal propertyManageBailFeeContract;

    /**
     * 物业管理费保证金是否符合：0 否，1 是
     */
    @TableField("property_manage_bail_fee_match")
    private Integer propertyManageBailFeeMatch;

    /**
     * 运营管理费保证金（元）
     */
    @TableField("operate_manage_bail_fee")
    private BigDecimal operateManageBailFee;

    /**
     * 运营管理费保证金
     */
    @TableField("operate_manage_bail_fee_contract")
    private BigDecimal operateManageBailFeeContract;

    /**
     * 运营管理费保证金是否符合：0 否，1 是
     */
    @TableField("operate_manage_bail_fee_match")
    private Integer operateManageBailFeeMatch;

    /**
     * 公共事业保证金（元）
     */
    @TableField("common_bail_fee")
    private BigDecimal commonBailFee;

    /**
     * 公共事业保证金
     */
    @TableField("common_bail_fee_contract")
    private BigDecimal commonBailFeeContract;

    /**
     * 公共事业保证金是否符合：0 否，1 是
     */
    @TableField("common_bail_fee_match")
    private Integer commonBailFeeMatch;

    /**
     * 装修押金（元）
     */
    @TableField("decoration_bail_fee")
    private BigDecimal decorationBailFee;

    /**
     * 装修押金
     */
    @TableField("decoration_bail_fee_contract")
    private BigDecimal decorationBailFeeContract;

    /**
     * 装修押金是否符合：0 否，1 是
     */
    @TableField("decoration_bail_fee_match")
    private Integer decorationBailFeeMatch;

    /**
     * POS机租金（元）
     */
    @TableField("pos_rent_fee")
    private BigDecimal posRentFee;

    /**
     * POS机押金（元）
     */
    @TableField("pos_bail_fee")
    private BigDecimal posBailFee;

    /**
     * 装修期物业管理费（元）
     */
    @TableField("decoration_property_fee")
    private BigDecimal decorationPropertyFee;

    /**
     * 装修期运营管理费（元）
     */
    @TableField("decoration_operate_fee")
    private BigDecimal decorationOperateFee;

    /**
     * 垃圾清运费（元）
     */
    @TableField("clean_fee")
    private BigDecimal cleanFee;

    /**
     * 月保底销售额
     */
    @TableField("guarant_sale_contract")
    private BigDecimal guarantSaleContract;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 月运营管理费递增集合
     */
    @TableField("operate_incremental_rate_list")
    private String operateIncrementalRateList;

    /**
     * 月物业管理费递增集合
     */
    @TableField("manage_incremental_rate_list")
    private String manageIncrementalRateList;

    /**
     * 扣点百分率 是否符合：0 否，1 是
     */
    @TableField("month_fee_percent_match")
    private Integer monthFeePercentMatch;

    /**
     * 月租金单价是否符合：0 否，1 是
     */
    @TableField("month_price_match")
    private Integer monthPriceMatch;

    /**
     * 月租金总额（元）
     */
    @TableField("month_fee")
    private BigDecimal monthFee;

    /**
     * 月租金总额是否符合：0 否，1 是
     */
    @TableField("month_fee_match")
    private Integer monthFeeMatch;

    /**
     * 月物业管理费总额（元）
     */
    @TableField("month_manage_fee")
    private BigDecimal monthManageFee;

    /**
     * 月物业管理费总额是否符合：0 否，1 是
     */
    @TableField("month_manage_fee_match")
    private Integer monthManageFeeMatch;

    /**
     * 月运营管理费总额（元）
     */
    @TableField("month_operate_fee")
    private BigDecimal monthOperateFee;

    /**
     * 月运营管理费总额是否符合：0 否，1 是
     */
    @TableField("month_operate_fee_match")
    private Integer monthOperateFeeMatch;

    /**
     * 其他费用总额（元）
     */
    @TableField("other_fee")
    private BigDecimal otherFee;

    /**
     * 其他费用总额是否符合：0 否，1 是
     */
    @TableField("other_fee_match")
    private Integer otherFeeMatch;

    /**
     * API接口费
     */
    @TableField("api_fee")
    private BigDecimal apiFee;

    /**
     * API接口费总款项
     */
    @TableField("api_fee_total_amount")
    private BigDecimal apiFeeTotalAmount;

    /**
     * API接口费首期款
     */
    @TableField("api_initial_payment_amount")
    private BigDecimal apiInitialPaymentAmount;

    /**
     * API接口费每期款项
     */
    @TableField("api_installment_amount")
    private BigDecimal apiInstallmentAmount;

    /**
     * API接口费尾期款
     */
    @TableField("api_final_payment_amount")
    private BigDecimal apiFinalPaymentAmount;

    /**
     * 产品保证金
     */
    @TableField("product_security_deposit")
    private BigDecimal productSecurityDeposit;

}
