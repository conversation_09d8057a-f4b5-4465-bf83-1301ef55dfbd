<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.commerce.mapper.CommerceAdvertContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.commerce.entity.CommerceAdvertContract">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="contract_code" property="contractCode" />
        <result column="advert_contract_type" property="advertContractType" />
        <result column="contract_type" property="contractType" />
        <result column="advert_id" property="advertId" />
        <result column="advert_name" property="advertName" />
        <result column="advert_type" property="advertType" />
        <result column="advert_area" property="advertArea" />
        <result column="lessor" property="lessor" />
        <result column="lessor_representative" property="lessorRepresentative" />
        <result column="lessor_address" property="lessorAddress" />
        <result column="third_party" property="thirdParty" />
        <result column="supplier_id" property="supplierId" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_certificate_address" property="supplierCertificateAddress" />
        <result column="contact_name" property="contactName" />
        <result column="contact_phon" property="contactPhon" />
        <result column="supplier_certificate_code" property="supplierCertificateCode" />
        <result column="brand_id" property="brandId" />
        <result column="brand_name" property="brandName" />
        <result column="brand_commercial_type_code" property="brandCommercialTypeCode" />
        <result column="brand_commercial_type_name" property="brandCommercialTypeName" />
        <result column="brand_category_id" property="brandCategoryId" />
        <result column="brand_category_name" property="brandCategoryName" />
        <result column="sign_entity" property="signEntity" />
        <result column="site_use" property="siteUse" />
        <result column="rent_start_date" property="rentStartDate" />
        <result column="rent_end_date" property="rentEndDate" />
        <result column="rent_type" property="rentType" />
        <result column="operate_price_pay_type" property="operatePricePayType" />
        <result column="month_fee_percent" property="monthFeePercent" />
        <result column="month_operate_price_contract" property="monthOperatePriceContract" />
        <result column="month_operate_fee_contract" property="monthOperateFeeContract" />
        <result column="fee_price_pay_type" property="feePricePayType" />
        <result column="first_pay_date" property="firstPayDate" />
        <result column="other_remark" property="otherRemark" />
        <result column="electricity_pay_type" property="electricityPayType" />
        <result column="electricity_price" property="electricityPrice" />
        <result column="electricity_pay_date" property="electricityPayDate" />
        <result column="electricity_start_date" property="electricityStartDate" />
        <result column="electricity_end_date" property="electricityEndDate" />
        <result column="electricity_fee" property="electricityFee" />
        <result column="bail_pay_type" property="bailPayType" />
        <result column="operate_manage_bail_fee_contract" property="operateManageBailFeeContract" />
        <result column="common_bail_fee_contract" property="commonBailFeeContract" />
        <result column="operate_manage_bail_fee_last" property="operateManageBailFeeLast" />
        <result column="common_bail_fee_last" property="commonBailFeeLast" />
        <result column="first_pay_operate_fee" property="firstPayOperateFee" />
        <result column="approve_status" property="approveStatus" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="approve_date" property="approveDate" />
        <result column="archive_date" property="archiveDate" />
        <result column="f_address_detail" property="faddressdetail" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
        <result column="third_party_company" property="thirdPartyCompany" />
        <result column="security_deposit_total_amount" property="securityDepositTotalAmount" />
    </resultMap>
    <select id="getIntersectionData" resultType="java.lang.Integer">
        select count(1)  from  som_commerce_advert_contract  t1  where advert_id=#{advertId}  and   del_status=0 and approve_status in (2,3) and tenant_id=#{tenantId}
        and t1.rent_start_date &lt;= #{rentEndDate}  and   #{rentStartDate} &lt;= t1.rent_end_date
        and t1.id not in
            (SELECT DISTINCT advert_contract_id FROM som_commerce_advert_entexit WHERE tenant_id = #{tenantId}
             and del_status = 0 and advert_contract_type = 1 and advert_status = 'withdrawal')
    </select>
    <select id="getIntersectionDataByAdvert" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        som_commerce_advert_contract t1
        LEFT JOIN som_commerce_advert_contract_detail t2 ON t1.id = t2.advert_contract_id
        WHERE
        t2.advert_id = #{advertId}
        AND t1.del_status = 0
        AND t1.approve_status IN ( 2, 3 )
        AND t1.tenant_id = #{tenantId}
        AND t1.rent_start_date &lt;= #{rentEndDate}
        AND #{rentStartDate} &lt;= t1.rent_end_date
        AND t1.id not in
            (SELECT DISTINCT advert_contract_id FROM som_commerce_advert_entexit WHERE tenant_id = #{tenantId}
            and del_status = 0 and advert_contract_type = 0 and advert_status = 'withdrawal')
    </select>
    <select id="listByReceivable" resultType="com.seewin.som.commerce.resp.CommerceChargeContractListVo">
        select
            t1.id,
            t1.contract_code,
            t1.advert_contract_type as shop_type,
            t1.advert_id as room_id,
            t1.advert_name as room_name,
            t1.brand_name
        from
            som_commerce_advert_contract t1
        where
         t1.del_status = 0  and t1.approve_status = 3 and t1.tenant_id=#{dto.tenantId}
        <if test="dto.roomName != null and dto.roomName != ''">
            and t1.advert_name like CONCAT('%',#{dto.roomName}, '%')
        </if>
        <if test="dto.brandName != null and dto.brandName != ''">
            and t1.brand_name like CONCAT('%',#{dto.brandName}, '%')
        </if>
        <if test="dto.shopType != null ">
            and t1.advert_contract_type =#{dto.shopType}
        </if>

        order by t1.approve_date  desc
    </select>

    <select id="getRentFeeCalculateInfo" resultType="com.seewin.som.commerce.resp.CommerceEffectiveContractVo">
        select * from som_commerce_advert_contract where del_status = 0 and contract_code = #{contractCode} limit 1
    </select>

</mapper>
