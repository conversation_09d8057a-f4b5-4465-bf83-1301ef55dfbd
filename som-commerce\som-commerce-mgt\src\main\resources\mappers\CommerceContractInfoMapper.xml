<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.commerce.mapper.CommerceContractInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.commerce.entity.CommerceContractInfo">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="contract_code" property="contractCode" />
        <result column="brand_id" property="brandId" />
        <result column="brand_name" property="brandName" />
        <result column="brand_commercial_type_code" property="brandCommercialTypeCode" />
        <result column="brand_commercial_type_name" property="brandCommercialTypeName" />
        <result column="brand_category_id" property="brandCategoryId" />
        <result column="brand_category_name" property="brandCategoryName" />
        <result column="business_scope" property="businessScope" />
        <result column="supplier_id" property="supplierId" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_certificate_code" property="supplierCertificateCode" />
        <result column="supplier_certificate_address" property="supplierCertificateAddress" />
        <result column="contact_name" property="contactName" />
        <result column="contact_phon" property="contactPhon" />
        <result column="supplier_quality" property="supplierQuality" />
        <result column="supplier_security" property="supplierSecurity" />
        <result column="supplier_violate" property="supplierViolate" />
        <result column="sign_entity" property="signEntity" />
        <result column="contract_type" property="contractType" />
        <result column="business_type" property="businessType" />
        <result column="decoration" property="decoration" />
        <result column="rent_type" property="rentType" />
        <result column="rent_start_date" property="rentStartDate" />
        <result column="rent_end_date" property="rentEndDate" />
        <result column="delivery_date" property="deliveryDate" />
        <result column="act_delivery_date" property="actDeliveryDate" />
        <result column="rent_fee_date" property="rentFeeDate" />
        <result column="open_date" property="openDate" />
        <result column="act_open_date" property="actOpenDate" />
        <result column="act_retrive_date" property="actRetriveDate" />
        <result column="points_mode" property="pointsMode" />
        <result column="settlement_date" property="settlementDate" />
        <result column="rent_increase_mode" property="rentIncreaseMode" />
        <result column="shop_type" property="shopType" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
        <result column="api_initial_payment_time" property="apiInitialPaymentTime" />
        <result column="api_fee_installments" property="apiFeeInstallments" />
        <result column="api_installment_payment_time" property="apiInstallmentPaymentTime" />
        <result column="api_final_payment_time" property="apiFinalPaymentTime" />
        <result column="product_cooperation_start_time" property="productCooperationStartTime" />
        <result column="product_cooperation_end_time" property="productCooperationEndTime" />
    </resultMap>
    <select id="selectContractInfoBySupplierId" resultMap="BaseResultMap">
        select i.* from
        som_commerce_contract_info i,som_commerce_contract c
        where i.supplier_id = #{supplierId}
        and i.contract_code = c.contract_code
        and c.approve_status =3
    </select>

    <select id="getRecommendStoreCount" resultType="com.seewin.som.commerce.resp.CommerceRecommendVo">
        SELECT t1.brand_id, IF(t2.lastStoreCount IS NULL OR t2.lastStoreCount = 0, 0, ROUND( (t1.curStoreCount - t2.lastStoreCount)*100 / t2.lastStoreCount, 2) ) as storePercent,
               ROW_NUMBER() OVER (ORDER BY IF(t2.lastStoreCount IS NULL OR t2.lastStoreCount = 0, 0, ROUND( (t1.curStoreCount - t2.lastStoreCount)*100 / t2.lastStoreCount, 2)) DESC) AS sort
        FROM
            (SELECT brand_id, count(contract_code) as curStoreCount FROM
                (SELECT contract_code, brand_id, rent_start_date FROM som_commerce.som_commerce_contract_info
                 WHERE del_status = 0 and contract_code in
                                          (SELECT DISTINCT contract_code FROM som_commerce.som_commerce_contract WHERE del_status = 0 and approve_status = 3)
                   and rent_start_date BETWEEN #{dto.createTimeStart} AND #{dto.createTimeEnd}) as tt
             GROUP BY brand_id) t1

                LEFT JOIN

            (SELECT brand_id, count(contract_code) as lastStoreCount FROM
                (SELECT contract_code, brand_id, rent_start_date FROM som_commerce.som_commerce_contract_info
                 WHERE del_status = 0 and contract_code in
                                          (SELECT DISTINCT contract_code FROM som_commerce.som_commerce_contract WHERE del_status = 0 and approve_status = 3)
                   and rent_start_date BETWEEN #{dto.createTimeStart} - INTERVAL 7 DAY AND #{dto.createTimeEnd} - INTERVAL 7 DAY) as tt
             GROUP BY brand_id) t2

            ON t1.brand_id = t2.brand_id
        ORDER BY storePercent desc
    </select>

</mapper>
