<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.commerce.mapper.CommerceContractPrintMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.commerce.entity.CommerceContractPrint">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="contract_code" property="contractCode" />
        <result column="contract_template_id" property="contractTemplateId" />
        <result column="template_name" property="templateName"/>
        <result column="template_type" property="templateType"/>
        <result column="contract_attr" property="contractAttr"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_type_name" property="contractTypeName"/>
        <result column="contract_attr_name" property="contractAttrName"/>
        <result column="contract_no" property="contractNo" />
        <result column="invoice_no" property="invoiceNo" />
        <result column="register_no" property="registerNo" />
        <result column="lessor" property="lessor" />
        <result column="lessor_representative" property="lessorRepresentative" />
        <result column="lessor_address" property="lessorAddress" />
        <result column="renter" property="renter" />
        <result column="renter_representative" property="renterRepresentative" />
        <result column="renter_address" property="renterAddress" />
        <result column="room_city" property="roomCity" />
        <result column="room_address" property="roomAddress" />
        <result column="room_name" property="roomName" />
        <result column="room_build_area" property="roomBuildArea" />
        <result column="rent_period" property="rentPeriod" />
        <result column="rent_start_date" property="rentStartDate" />
        <result column="rent_end_date" property="rentEndDate" />
        <result column="brand_id" property="brandId" />
        <result column="brand_name" property="brandName" />
        <result column="brand_commercial_type_code" property="brandCommercialTypeCode" />
        <result column="brand_commercial_type_name" property="brandCommercialTypeName" />
        <result column="business_scope" property="businessScope" />
        <result column="month_price_contract" property="monthPriceContract" />
        <result column="month_fee_contract" property="monthFeeContract" />
        <result column="incremental_start_contract" property="incrementalStartContract" />
        <result column="incremental_rate_contract" property="incrementalRateContract" />
        <result column="rent_bail_fee_contract" property="rentBailFeeContract" />
        <result column="common_bail_fee_contract" property="commonBailFeeContract" />
        <result column="accounting_org_name" property="accountingOrgName" />
        <result column="bank_account_name" property="bankAccountName" />
        <result column="bank_account_code" property="bankAccountCode" />
        <result column="delivery_date" property="deliveryDate" />
        <result column="fee_free_contract" property="feeFreeContract" />
        <result column="open_date" property="openDate" />
        <result column="decoration_bail_fee_contract" property="decorationBailFeeContract" />
        <result column="pos_fee" property="posFee" />
        <result column="lessor_notify" property="lessorNotify" />
        <result column="lessor_notify_address" property="lessorNotifyAddress" />
        <result column="renter_notify" property="renterNotify" />
        <result column="renter_notify_phone" property="renterNotifyPhone" />
        <result column="renter_notify_address" property="renterNotifyAddress" />
        <result column="renter_notify_email" property="renterNotifyEmail" />
        <result column="renter_notify_contact_name" property="renterNotifyContactName" />
        <result column="renter_notify_contact_phone" property="renterNotifyContactPhone" />
        <result column="standard_detail" property="standardDetail" />
        <result column="bank_account" property="bankAccount" />
        <result column="thirdparty" property="thirdparty" />
        <result column="interparty" property="interparty" />
        <result column="prep_rent_fee" property="prepRentFee" />
        <result column="floor_name" property="floorName" />
        <result column="function_name" property="functionName" />
        <result column="lessor_email" property="lessorEmail" />
        <result column="lessor_contact_name" property="lessorContactName" />
        <result column="lessor_contact_phone" property="lessorContactPhone" />
        <result column="renter_card_number" property="renterCardNumber" />
        <result column="month_operate_price_contract" property="monthOperatePriceContract" />
        <result column="month_operate_fee_contract" property="monthOperateFeeContract" />
        <result column="month_manage_price_contract" property="monthManagePriceContract" />
        <result column="month_manage_fee_contract" property="monthManageFeeContract" />
        <result column="composite_manage_fee_contract" property="compositeManageFeeContract" />
        <result column="prep_pay_days" property="prepPayDays" />
        <result column="prep_operate_fee" property="prepOperateFee" />
        <result column="prep_maintain_fee" property="prepMaintainFee" />
        <result column="property_manage_bail_fee_contract" property="propertyManageBailFeeContract" />
        <result column="operate_manage_bail_fee_contract" property="operateManageBailFeeContract" />
        <result column="other_price_contract" property="otherPriceContract" />
        <result column="other_fee_contract" property="otherFeeContract" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />

        <result column="sign_type" property="signType" />
        <result column="lessor_keyword" property="lessorKeyword" />
        <result column="renter_keyword" property="renterKeyword" />
        <result column="sign_flow_id" property="signFlowId" />
        <result column="sign_status" property="signStatus" />
        <result column="signed_download_url" property="signedDownloadUrl" />
        <result column="sign_company_code" property="signCompanyCode" />
        <result column="seal_type_code" property="sealTypeCode" />
        <result column="is_renter_seal" property="isRenterSeal" />
        <result column="is_renter_seal_first" property="isRenterSealFirst" />
        <result column="is_cross_page_seal" property="isCrossPageSeal" />
        <result column="renter_company_name" property="renterCompanyName" />
        <result column="renter_company_code" property="renterCompanyCode" />
        <result column="f_address_detail" property="faddressdetail" />

        <result column="security_deposit_total_amount" property="securityDepositTotalAmount" />
        <result column="third_party_company" property="thirdPartyCompany" />
        <result column="api_initial_payment_time" property="apiInitialPaymentTime" />
        <result column="api_fee_installments" property="apiFeeInstallments" />
        <result column="api_installment_payment_time" property="apiInstallmentPaymentTime" />
        <result column="api_final_payment_time" property="apiFinalPaymentTime" />
        <result column="product_cooperation_start_time" property="productCooperationStartTime" />
        <result column="product_cooperation_end_time" property="productCooperationEndTime" />
        <result column="api_fee" property="apiFee" />
        <result column="api_fee_total_amount" property="apiFeeTotalAmount" />
        <result column="api_initial_payment_amount" property="apiInitialPaymentAmount" />
        <result column="api_installment_amount" property="apiInstallmentAmount" />
        <result column="api_final_payment_amount" property="apiFinalPaymentAmount" />
        <result column="product_security_deposit" property="productSecurityDeposit" />
    </resultMap>

    <select id="getBasicInfoByContractCode" resultType="com.seewin.som.commerce.resp.CommerceContractPrintGetVo">
        SELECT
        t1.tenant_id AS tenantId,
        t1.name AS roomName,
        t1.rent_area AS roomBuildArea,
        t1.fname AS floorName,

        t2.supplier_id,
        t2.supplier_name AS renter,
        t2.supplier_certificate_address AS renterAddress,
        t2.lessor,
        t2.lessor_representative,
        t2.lessor_address,
        t2.third_party ,
        t4.legal_representative AS renterRepresentative,
        t4.contact_phon AS renterNotifyPhone,

        t4.email AS renterNotifyEmail,
        t4.contact_name AS renterNotifyContactName,
        t4.contact_phon AS renterNotifyContactPhone,
        t2.supplier_certificate_code AS renterCardNumber,
        t2.rent_type,
        t2.rent_start_date,
        t2.rent_end_date,
        t2.brand_id,
        t2.brand_name,

        t2.brand_commercial_type_code,
        t2.brand_commercial_type_name,
        t2.business_scope,

        t2.delivery_date,
        t2.business_scope,
        t2.open_date,
        t2.rent_increase_mode,

        t2.supplier_name,
        t2.supplier_certificate_code,

        t2.api_initial_payment_time,
        t2.api_fee_installments,
        t2.api_installment_payment_time,
        t2.api_final_payment_time,
        t2.product_cooperation_start_time,
        t2.product_cooperation_end_time,


        t3.month_price_contract,
        t3.month_fee_contract,

        t3.incremental_start_contract,
        t3.incremental_rate_contract,

        t3.rent_bail_fee_contract,
        t3.common_bail_fee_contract,

        t3.fee_free_contract,
        t3.decoration_bail_fee_contract,

        t3.month_manage_price_contract,
        t3.month_manage_fee_contract,
        t3.month_operate_price_contract,
        t3.month_operate_fee_contract,
        t3.composite_manage_fee_contract,
        t3.property_manage_bail_fee_contract,
        t3.operate_manage_bail_fee_contract,
        t3.other_price_contract,
        t3.incremental_rate_list,
        t3.decoration_operate_fee,
        t3.decoration_property_fee,

        t3.api_fee,
        t3.api_fee_total_amount,
        t3.api_initial_payment_amount,
        t3.api_installment_amount,
        t3.api_final_payment_amount,
        t3.product_security_deposit

        FROM som_commerce_contract t1
        LEFT JOIN som_commerce_contract_info t2 ON t1.contract_code = t2.contract_code
        LEFT JOIN som_commerce_contract_rent t3 ON t1.contract_code = t3.contract_code
        LEFT JOIN som_commerce_supplier t4 ON t2.supplier_id = t4.id
        WHERE t1.contract_code = #{contractCode} and t1.approve_status = 3 and t1.del_status = 0 and t2.del_status = 0 and t3.del_status = 0
    </select>


    <select id="selectSealId" resultType="java.lang.String">
        select seal_id from som_commerce_contract_seal where sign_company_code = ${signCompanyCode} and seal_type_code = ${sealTypeCode}
    </select>

    <select id="getSignInfoBySignCompanyCode" resultType="map">
        select sign_company_name AS signCompanyName , handler_account AS handlerAccount  from som_commerce_contract_seal where sign_company_code = ${signCompanyCode} limit 1
    </select>

    <select id="selectSealIdByOaUploadReq" resultType="map">
        select
            seal_id AS sealId,
            sign_company_code AS signCompanyCode,
            sign_company_name AS signCompanyName,
            seal_type_code AS sealTypeCode,
            seal_type_name AS sealTypeName,
            handler_account AS handlerAccount
        from som_commerce_contract_seal
        where sign_company_name = #{signCompanyName}  and seal_type_name = #{sealTypeName} limit 1
    </select>

    <select id="selectCompanyCodeByLessor" resultType="java.lang.Integer">
        select sign_company_code from som_commerce_contract_seal where sign_company_name = #{lessor} limit 1
    </select>

    <select id="selectSignInfoBySignFlowId" resultType="com.seewin.som.commerce.resp.CommerceContractPrintGetVo">
        select * from som_commerce_contract_print where sign_flow_id = #{signFlowId}  and del_status = 0  limit 1
    </select>

    <update id="updateSignStatusBySignFlowId">
        UPDATE som_commerce_contract_print SET sign_status = ${signStatus}
        WHERE sign_flow_id = #{signFlowId}
    </update>
    <update id="batchUpdateTemplateId">
        UPDATE som_commerce_contract_print SET contract_template_id = ${newTemplateId}
        WHERE contract_template_id = ${templateId}
    </update>


    <select id="getOtherFileById" resultType="com.seewin.som.commerce.resp.CommerceContractPrintListVo">
        select * from som_commerce_contract_print where same_contract_id in (
            select same_contract_id  from som_commerce_contract_print where id = #{id})
       and id != #{id}  and del_status  = 0
    </select>

    <select id="getAllFileBySameContractId" resultType="com.seewin.som.commerce.resp.CommerceContractPrintGetVo">
        select * from som_commerce_contract_print where same_contract_id = #{sameContractId} and del_status  = 0
    </select>

</mapper>
