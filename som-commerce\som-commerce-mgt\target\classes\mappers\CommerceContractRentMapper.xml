<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.commerce.mapper.CommerceContractRentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.commerce.entity.CommerceContractRent">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="contract_code" property="contractCode" />
        <result column="year_limit" property="yearLimit" />
        <result column="year_limit_contract" property="yearLimitContract" />
        <result column="year_limit_match" property="yearLimitMatch" />
        <result column="month_price" property="monthPrice" />
        <result column="month_price_contract" property="monthPriceContract" />
        <result column="month_fee_contract" property="monthFeeContract" />
        <result column="month_fee_percent" property="monthFeePercent" />
        <result column="month_manage_price" property="monthManagePrice" />
        <result column="month_manage_price_contract" property="monthManagePriceContract" />
        <result column="month_manage_price_match" property="monthManagePriceMatch" />
        <result column="month_manage_fee_contract" property="monthManageFeeContract" />
        <result column="month_operate_price" property="monthOperatePrice" />
        <result column="month_operate_price_contract" property="monthOperatePriceContract" />
        <result column="month_operate_price_match" property="monthOperatePriceMatch" />
        <result column="month_operate_fee_contract" property="monthOperateFeeContract" />
        <result column="other_price" property="otherPrice" />
        <result column="other_price_contract" property="otherPriceContract" />
        <result column="other_price_match" property="otherPriceMatch" />
        <result column="other_fee_contract" property="otherFeeContract" />
        <result column="incremental_rate" property="incrementalRate" />
        <result column="incremental_start_contract" property="incrementalStartContract" />
        <result column="incremental_interval_contract" property="incrementalIntervalContract" />
        <result column="incremental_rate_contract" property="incrementalRateContract" />
        <result column="incremental_rate_match" property="incrementalRateMatch" />
        <result column="fee_free" property="feeFree" />
        <result column="fee_free_contract" property="feeFreeContract" />
        <result column="fee_free_match" property="feeFreeMatch" />
        <result column="composite_manage_fee" property="compositeManageFee" />
        <result column="composite_manage_fee_contract" property="compositeManageFeeContract" />
        <result column="composite_manage_fee_match" property="compositeManageFeeMatch" />
        <result column="rent_bail_fee" property="rentBailFee" />
        <result column="rent_bail_fee_contract" property="rentBailFeeContract" />
        <result column="rent_bail_fee_match" property="rentBailFeeMatch" />
        <result column="property_manage_bail_fee" property="propertyManageBailFee" />
        <result column="property_manage_bail_fee_contract" property="propertyManageBailFeeContract" />
        <result column="property_manage_bail_fee_match" property="propertyManageBailFeeMatch" />
        <result column="operate_manage_bail_fee" property="operateManageBailFee" />
        <result column="operate_manage_bail_fee_contract" property="operateManageBailFeeContract" />
        <result column="operate_manage_bail_fee_match" property="operateManageBailFeeMatch" />
        <result column="common_bail_fee" property="commonBailFee" />
        <result column="common_bail_fee_contract" property="commonBailFeeContract" />
        <result column="common_bail_fee_match" property="commonBailFeeMatch" />
        <result column="decoration_bail_fee" property="decorationBailFee" />
        <result column="decoration_bail_fee_contract" property="decorationBailFeeContract" />
        <result column="decoration_bail_fee_match" property="decorationBailFeeMatch" />
        <result column="pos_rent_fee" property="posRentFee" />
        <result column="pos_bail_fee" property="posBailFee" />
        <result column="decoration_property_fee" property="decorationPropertyFee" />
        <result column="decoration_operate_fee" property="decorationOperateFee" />
        <result column="clean_fee" property="cleanFee" />
        <result column="guarant_sale_contract" property="guarantSaleContract" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
        <result column="month_fee_percent_match" property="monthFeePercentMatch" />
        <result column="month_price_match" property="monthPriceMatch" />
        <result column="month_fee" property="monthFee" />
        <result column="month_fee_match" property="monthFeeMatch" />
        <result column="month_manage_fee" property="monthManageFee" />
        <result column="month_manage_fee_match" property="monthManageFeeMatch" />
        <result column="month_operate_fee" property="monthOperateFee" />
        <result column="month_operate_fee_match" property="monthOperateFeeMatch" />
        <result column="other_fee" property="otherFee" />
        <result column="other_fee_match" property="otherFeeMatch" />
        <result column="api_fee" property="apiFee" />
        <result column="api_fee_total_amount" property="apiFeeTotalAmount" />
        <result column="api_initial_payment_amount" property="apiInitialPaymentAmount" />
        <result column="api_installment_amount" property="apiInstallmentAmount" />
        <result column="api_final_payment_amount" property="apiFinalPaymentAmount" />
        <result column="product_security_deposit" property="productSecurityDeposit" />
    </resultMap>

    <update id="updateFeeEmpty">
        UPDATE som_commerce_contract_rent SET month_fee_contract = NULL WHERE id = #{id}
    </update>

</mapper>
