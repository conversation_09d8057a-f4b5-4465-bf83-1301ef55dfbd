package com.seewin.som.commerce.service.impl;

import com.seewin.model.base.OptUser;
import com.seewin.som.commerce.enums.ComercePlanApproveStatusEnum;
import com.seewin.som.commerce.service.CommerceAdvertEntexitService;
import com.seewin.som.workflow.provider.FlwApplyProvider;
import com.seewin.som.workflow.provider.ProcessServiceProvider;
import com.seewin.som.workflow.provider.ProcessStatusEnum;
import com.seewin.som.workflow.req.FlwApplyListDto;
import com.seewin.som.workflow.req.ProcessStartDto;
import com.seewin.som.workflow.req.ReSubmitDto;
import com.seewin.som.workflow.resp.FlwApplyGetVo;
import com.seewin.som.workflow.resp.ProcessTaskVo;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.commerce.provider.CommerceAdvertEntexitProvider;
import com.seewin.som.commerce.req.CommerceAdvertEntexitAddDto;
import com.seewin.som.commerce.req.CommerceAdvertEntexitEditDto;
import com.seewin.som.commerce.req.CommerceAdvertEntexitListDto;
import com.seewin.som.commerce.resp.CommerceAdvertEntexitAddVo;
import com.seewin.som.commerce.resp.CommerceAdvertEntexitGetVo;
import com.seewin.som.commerce.resp.CommerceAdvertEntexitListVo;

import com.seewin.som.commerce.vo.req.CommerceAdvertEntexitListReq;
import com.seewin.som.commerce.vo.req.CommerceAdvertEntexitGetReq;
import com.seewin.som.commerce.vo.req.CommerceAdvertEntexitAddReq;
import com.seewin.som.commerce.vo.req.CommerceAdvertEntexitEditReq;
import com.seewin.som.commerce.vo.req.CommerceAdvertEntexitDelReq;
import com.seewin.som.commerce.vo.resp.CommerceAdvertEntexitListItem;
import com.seewin.som.commerce.vo.resp.CommerceAdvertEntexitGetResp;
import com.seewin.som.commerce.vo.resp.CommerceAdvertEntexitAddResp;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 广告位合同进撤场管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
public class CommerceAdvertEntexitServiceImpl implements CommerceAdvertEntexitService {

	/**
     * providedBy：兼容Mesh服务
     */
	@DubboReference(providedBy = "som-commerce-mgt")
	private CommerceAdvertEntexitProvider commerceAdvertEntexitProvider;

    @DubboReference(providedBy = "som-workflow-mgt")
    private ProcessServiceProvider processServiceProvider;

    @DubboReference(providedBy = "som-workflow-mgt")
    private FlwApplyProvider flwApplyProvider;

	/**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<CommerceAdvertEntexitListItem> page(CommerceAdvertEntexitListReq listReq) {
        PageResp<CommerceAdvertEntexitListItem> pageResp = new PageResp<>();
		User curUser = ApiUtils.getUser(User.class);
		
        CommerceAdvertEntexitListDto queryDto = BeanUtils.copyProperties(listReq, CommerceAdvertEntexitListDto.class);
				queryDto.setTenantId(curUser.getTenantId());
        
        PageQuery<CommerceAdvertEntexitListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<CommerceAdvertEntexitListVo> pageResult = commerceAdvertEntexitProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), CommerceAdvertEntexitListItem.class));

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public CommerceAdvertEntexitGetResp get(CommerceAdvertEntexitGetReq getReq) {
        CommerceAdvertEntexitGetVo getVo = commerceAdvertEntexitProvider.get(getReq.getId());

        CommerceAdvertEntexitGetResp getResp = BeanUtils.copyProperties(getVo, CommerceAdvertEntexitGetResp.class);

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public CommerceAdvertEntexitAddResp add(CommerceAdvertEntexitAddReq addReq) {
        CommerceAdvertEntexitAddDto dto = BeanUtils.copyProperties(addReq, CommerceAdvertEntexitAddDto.class);

		//设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());
       
		dto.setTenantId(curUser.getTenantId());
		
        CommerceAdvertEntexitAddVo addVo = commerceAdvertEntexitProvider.add(dto);

        CommerceAdvertEntexitAddResp addResp = BeanUtils.copyProperties(addVo, CommerceAdvertEntexitAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(CommerceAdvertEntexitEditReq editReq) {
        CommerceAdvertEntexitEditDto dto = BeanUtils.copyProperties(editReq, CommerceAdvertEntexitEditDto.class);

        //设置修改人信息
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());

        if (editReq.getOperate()!=null && editReq.getOperate()==1){
            commerceAdvertEntexitProvider.edit(dto);
            return;
        }

        FlwApplyListDto applyListDto = new FlwApplyListDto();
        applyListDto.setProcInstanceId(editReq.getExitProcessInstanceId());
        FlwApplyGetVo flwApplyGetVo = flwApplyProvider.get(applyListDto);
        if (editReq.getOperate()!=null && editReq.getOperate()==2 && Objects.nonNull(flwApplyGetVo) && flwApplyGetVo.getStatus() == ProcessStatusEnum.Reject.getStatus()) {
            ReSubmitDto reSubmitDto = new ReSubmitDto();
            reSubmitDto.setProcessInstanceId(editReq.getExitProcessInstanceId());
            reSubmitDto.setBizId(editReq.getId());
            reSubmitDto.setRemark(dto.getAdvertName() + " " + dto.getBrandName());
            reSubmitDto.setApplyTime(LocalDateTime.now());
            reSubmitDto.setUserId(curUser.getUserId());
            reSubmitDto.setUserCode(curUser.getUserName());
            reSubmitDto.setUserName(curUser.getRealName());
            reSubmitDto.setUserOrgId(curUser.getUserFid());
            reSubmitDto.setUserOrgName(curUser.getUserFname());
            processServiceProvider.reSubmit(reSubmitDto);
        }else {
            //启动审批流程
            ProcessStartDto processStartDto = new ProcessStartDto();
            processStartDto.setTenantId(curUser.getTenantId());
            processStartDto.setTenantName(curUser.getTenantName());

            processStartDto.setEntId(curUser.getEntId());
            processStartDto.setOrgFid(curUser.getOrgFid());
            processStartDto.setOrgFname(curUser.getOrgFname());

            processStartDto.setUserCode(curUser.getUserName());
            processStartDto.setUserId(curUser.getUserId());
            processStartDto.setUserName(curUser.getRealName());
            processStartDto.setUserOrgId(curUser.getUserFid());
            processStartDto.setUserOrgName(curUser.getUserFname());
            processStartDto.setProcDefCode("business_advert_exit");
            processStartDto.setRemark(dto.getAdvertName() + " " + dto.getBrandName());
            // 具体业务表单ID
            processStartDto.setBizId(editReq.getId());
            processStartDto.setApplyTime(LocalDateTime.now());

            Map<String, Object> variables = new HashMap<String, Object>();
            processStartDto.setVariables(variables);
            ProcessTaskVo vo = processServiceProvider.start(processStartDto);

            // 修改撤销信息
            dto.setExitProcessInstanceId(vo.getProcessInstanceId());
            dto.setExitApproveStatus(ComercePlanApproveStatusEnum.beingProcessed.getCode());
            dto.setExitGenDate(LocalDateTime.now());
            dto.setExitAplDate(LocalDate.now());
            dto.setExitAplName(curUser.getRealName());

            commerceAdvertEntexitProvider.edit(dto);
        }
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(CommerceAdvertEntexitDelReq delReq) {
        commerceAdvertEntexitProvider.delete(delReq.getId());
    }
}
