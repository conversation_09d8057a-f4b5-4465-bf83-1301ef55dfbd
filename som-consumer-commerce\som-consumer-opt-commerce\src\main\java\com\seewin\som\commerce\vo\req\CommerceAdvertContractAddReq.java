package com.seewin.som.commerce.vo.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.seewin.som.commerce.vo.resp.CommerceAdvertContractDetailListItem;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 广告位合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
@Setter
public class CommerceAdvertContractAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合同类型（0-广告位、1-多经点位）
     */
    @Schema(description = "合同类型（0-广告位、1-多经点位）")
    private Integer advertContractType;

    /**
     * 签约类型：1 新签，2 续签
     */
    @Schema(description = "签约类型：1 新签，2 续签")
    private Integer contractType;

    /**
     * 广告多经ID
     */
    @Schema(description = "广告多经ID")
    private Long advertId;

    /**
     * 广告多经编号
     */
    @Schema(description = "广告多经编号")
    @Size(max=255,message = "广告多经编号最大长度不能超过255")
    private String advertName;

    /**
     * 多经点位类型
     */
    @Schema(description = "多经点位类型")
    private Integer advertType;

    /**
     * 场地使用面积（m2）
     */
    @Schema(description = "场地使用面积（m2）")
    private BigDecimal advertArea;

    /**
     * 甲方
     */
    @Schema(description = "甲方")
    @Size(max=255,message = "甲方最大长度不能超过255")
    private String lessor;

    /**
     * 甲方法定代表人
     */
    @Schema(description = "甲方法定代表人")
    @Size(max=255,message = "甲方法定代表人最大长度不能超过255")
    private String lessorRepresentative;

    /**
     * 甲方地址
     */
    @Schema(description = "甲方地址")
    @Size(max=255,message = "甲方地址最大长度不能超过255")
    private String lessorAddress;

    /**
     * 第三方
     */
    @Schema(description = "第三方")
    @Size(max=255,message = "第三方最大长度不能超过255")
    private String thirdParty;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    @Size(max=255,message = "供应商名称最大长度不能超过255")
    private String supplierName;

    /**
     * 身份证地址/公司营业执照地址
     */
    @Schema(description = "身份证地址/公司营业执照地址")
    @Size(max=255,message = "身份证地址/公司营业执照地址最大长度不能超过255")
    private String supplierCertificateAddress;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    @Size(max=255,message = "联系人姓名最大长度不能超过255")
    private String contactName;

    /**
     * 联系人方式（号码）
     */
    @Schema(description = "联系人方式（号码）")
    @Size(max=255,message = "联系人方式（号码）最大长度不能超过255")
    private String contactPhon;

    /**
     * 身份证号码/统一信用代码
     */
    @Schema(description = "身份证号码/统一信用代码")
    @Size(max=255,message = "身份证号码/统一信用代码最大长度不能超过255")
    private String supplierCertificateCode;

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    private Long brandId;

    /**
     * 签约品牌名称
     */
    @Schema(description = "签约品牌名称")
    @Size(max=255,message = "签约品牌名称最大长度不能超过255")
    private String brandName;

    /**
     * 签约品牌业态字典code
     */
    @Schema(description = "签约品牌业态字典code")
    private Long brandCommercialTypeCode;

    /**
     * 签约品牌业态名称
     */
    @Schema(description = "签约品牌业态名称")
    @Size(max=255,message = "签约品牌业态名称最大长度不能超过255")
    private String brandCommercialTypeName;

    /**
     * 签约品牌一级品类
     */
    @Schema(description = "签约品牌一级品类")
    private Long brandCategoryId;

    /**
     * 签约品牌一级品类名称
     */
    @Schema(description = "签约品牌一级品类名称")
    @Size(max=255,message = "签约品牌一级品类名称最大长度不能超过255")
    private String brandCategoryName;

    /**
     * 签约主体：1公司，2个人
     */
    @Schema(description = "签约主体：1公司，2个人")
    private Integer signEntity;

    /**
     * 场地用途
     */
    @Schema(description = "场地用途")
    @Size(max=255,message = "场地用途最大长度不能超过255")
    private String siteUse;

    /**
     * 租赁起始时间
     */
    @Schema(description = "租赁起始时间")
    private LocalDate rentStartDate;

    /**
     * 租赁结束时间
     */
    @Schema(description = "租赁结束时间")
    private LocalDate rentEndDate;

    /**
     * 租赁类型：1 纯租、2-纯扣
     */
    @Schema(description = "租赁类型：1 纯租、2-纯扣")
    private Integer rentType;

    /**
     * 运营管理费收取方式(0-月,1-季度,2-半年,3-年,4-定价)
     */
    @Schema(description = "运营管理费收取方式(0-月,1-季度,2-半年,3-年,4-定价)")
    private Integer operatePricePayType;

    /**
     * 扣点百分率
     */
    @Schema(description = "扣点百分率")
    private BigDecimal monthFeePercent;

    /**
     * 运营管理费单价（小写）（元）
     */
    @Schema(description = "运营管理费单价（小写）（元）")
    private BigDecimal monthOperatePriceContract;

    /**
     * 运营管理费总额（小写）（元）
     */
    @Schema(description = "运营管理费总额（小写）（元）")
    private BigDecimal monthOperateFeeContract;

    /**
     * 费用支付方式(0-按月支付,1-一次性支付,2-其他支付方式)
     */
    @Schema(description = "费用支付方式(0-按月支付,1-一次性支付,2-其他支付方式)")
    private Integer feePricePayType;

    /**
     * 一次性支付日期
     */
    @Schema(description = "一次性支付日期")
    private LocalDate firstPayDate;

    /**
     * 其他支付方式说明
     */
    @Schema(description = "其他支付方式说明")
    @Size(max=255,message = "其他支付方式说明最大长度不能超过255")
    private String otherRemark;

    /**
     * 电费收取方式 广告位-(0-运营管理费已包含使用期限内广告位电费，乙方无需另行支付,1-乙方按照目前商业用电的收费标准向甲方交纳电费,2-该场地无电表) 多经点-（0-乙方自行安装独立水、电表并承担相应费用，水费、电费、基础设施服务费等公共事业收费标准按照甲方收费政策执行。1-场地无水电表。2-乙方缴纳的营运管理费已包含水电费，无需另行缴纳。）
     */
    @Schema(description = "电费收取方式 广告位-(0-运营管理费已包含使用期限内广告位电费，乙方无需另行支付,1-乙方按照目前商业用电的收费标准向甲方交纳电费,2-该场地无电表) 多经点-（0-乙方自行安装独立水、电表并承担相应费用，水费、电费、基础设施服务费等公共事业收费标准按照甲方收费政策执行。1-场地无水电表。2-乙方缴纳的营运管理费已包含水电费，无需另行缴纳。）")
    private Integer electricityPayType;

    /**
     * 电费标准（元/月）
     */
    @Schema(description = "电费标准（元/月）")
    private BigDecimal electricityPrice;

    /**
     * 首次电费支付日期
     */
    @Schema(description = "首次电费支付日期")
    private LocalDate electricityPayDate;

    /**
     * 首次电费周期开始日期
     */
    @Schema(description = "首次电费周期开始日期")
    private LocalDate electricityStartDate;

    /**
     * 首次电费周期结束日期
     */
    @Schema(description = "首次电费周期结束日期")
    private LocalDate electricityEndDate;

    /**
     * 首次水电费（元）
     */
    @Schema(description = "首次水电费（元）")
    private BigDecimal electricityFee;

    /**
     * 保证金收取方式 广告位-(0-合同签订之日起三日内由乙方一次性支付给甲方。,1-乙方在上期《广告位使用协议》（或《场地租赁合同》）签订时已缴纳保证金)多经点位-（0-乙方应当于签订本协议当日内一次性缴清本协议约定的营运管理费保证金、公共事业保证。1-乙方在上期《临促协议》签订时已缴纳营运管理费保证金、公共事业保证金，自动转为本期《临促协议》的营运管理费保证金）
     */
    @Schema(description = "保证金收取方式 广告位-(0-合同签订之日起三日内由乙方一次性支付给甲方。,1-乙方在上期《广告位使用协议》（或《场地租赁合同》）签订时已缴纳保证金)多经点位-（0-乙方应当于签订本协议当日内一次性缴清本协议约定的营运管理费保证金、公共事业保证。1-乙方在上期《临促协议》签订时已缴纳营运管理费保证金、公共事业保证金，自动转为本期《临促协议》的营运管理费保证金）")
    private String bailPayType;

    /**
     * 运营管理费保证金（元）
     */
    @Schema(description = "运营管理费保证金（元）")
    private BigDecimal operateManageBailFeeContract;

    /**
     * 公共事业保证金（元）
     */
    @Schema(description = "公共事业保证金（元）")
    private BigDecimal commonBailFeeContract;

    /**
     * 上期已缴纳运营管理费保证金（元）
     */
    @Schema(description = "上期已缴纳运营管理费保证金（元）")
    private BigDecimal operateManageBailFeeLast;

    /**
     * 上期已缴纳公共事业保证金（元）
     */
    @Schema(description = "上期已缴纳公共事业保证金（元）")
    private BigDecimal commonBailFeeLast;

    /**
     * 一次性支付运营管理费总费用（元）
     */
    @Schema(description = "一次性支付运营管理费总费用（元）")
    private BigDecimal firstPayOperateFee;

    /**
     * 审批状态: 2 审批中，3 已通过，4 已驳回
     */
    @Schema(description = "审批状态: 2 审批中，3 已通过，4 已驳回")
    private Integer approveStatus;

    /**
     * 流程实例ID
     */
    @Schema(description = "流程实例ID")
    @Size(max=255,message = "流程实例ID最大长度不能超过255")
    private String processInstanceId;

    /**
     * 审批时间
     */
    @Schema(description = "审批时间")
    private LocalDate approveDate;

    /**
     * 合同归档时间
     */
    @Schema(description = "合同归档时间")
    private LocalDate archiveDate;

    /**
     * 操作类型：1 保存，2 流转
     */
    @Schema(description = "操作类型：1 保存，2 流转")
    private Integer operate;

    /**
     * 撤场管理id
     */
    @Schema(description = "撤场管理id")
    private Long exitId;

    @Schema(description = "广告位明细数据")
    private List<CommerceAdvertContractDetailListItem> itemList;

    @Schema(description = "项目地址")
    private String faddressdetail;

    /**
     * 保证金总额
     */
    @Schema(description = "保障金总额")
    private BigDecimal securityDepositTotalAmount;

    /**
     * 第三方公司
     */
    @Schema(description = "第三方公司")
    private String thirdPartyCompany;

    /**
     * 合同模板类型, 0-普通模板 1-科技模板
     */
    @Schema(description = "合同模板类型, 0-普通模板 1-科技模板")
    private Integer templateType;
}
