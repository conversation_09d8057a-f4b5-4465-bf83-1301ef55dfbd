package com.seewin.som.commerce.vo.req;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 招商合同表-商铺
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Getter
@Setter
public class CommerceContractAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单编号
     */
    @Schema(description = "工单编号")
    @Size(max=255,message = "工单编号最大长度不能超过255")
    private String orderCode;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractCode;

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    private Long brandId;

    /**
     * 签约品牌名称
     */
    @Schema(description = "签约品牌名称")
    @Size(max=255,message = "签约品牌名称最大长度不能超过255")
    private String brandName;

    /**
     * 适配业态字典id
     */
    @Schema(description = "适配业态字典id")
    private Long commercialTypeCode;

    /**
     * 适配业态名称
     */
    @Schema(description = "适配业态名称")
    @Size(max=255,message = "适配业态名称最大长度不能超过255")
    private String commercialTypeName;

    /**
     * 所属一级品类
     */
    @Schema(description = "所属一级品类")
    private Long categoryId;

    /**
     * 所属一级品类名称
     */
    @Schema(description = "所属一级品类名称")
    @Size(max=255,message = "所属一级品类名称最大长度不能超过255")
    private String categoryName;

    /**
     *签约信息
     */
    @Schema(description = "签约信息")
    private CommerceContractInfoAddReq contractInfo;

    /**
     *租赁信息
     */
    @Schema(description = "租赁信息")
    private CommerceContractRentAddReq contractRent;

    @Schema(description = "操作类型：1 保存，2 流转")
    private Integer operate;

    @Schema(description = "项目地址")
    private String faddressdetail;

    /**
     * 合同模板类型, 0-普通模板 1-科技模板 2-小程序模板
     */
    @Schema(description = "合同模板类型, 0-普通模板 1-科技模板 2-小程序模板")
    private Integer templateType;
}
