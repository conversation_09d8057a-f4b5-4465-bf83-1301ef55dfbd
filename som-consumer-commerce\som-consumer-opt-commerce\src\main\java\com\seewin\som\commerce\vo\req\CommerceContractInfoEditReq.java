package com.seewin.som.commerce.vo.req;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 招商合同信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Getter
@Setter
public class CommerceContractInfoEditReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * 租户名称(项目名称)
     */
    @Schema(description = "租户名称(项目名称)")
    @Size(max=255,message = "租户名称(项目名称)最大长度不能超过255")
    private String tenantName;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @Schema(description = "所属组织ID路径")
    @NotBlank(message = "所属组织ID路径不能为空")
    @Size(max=255,message = "所属组织ID路径最大长度不能超过255")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @Schema(description = "所属组织名称路径")
    @Size(max=255,message = "所属组织名称路径最大长度不能超过255")
    private String orgFname;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    @Size(max=255,message = "合同编号最大长度不能超过255")
    private String contractCode;

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    private Long brandId;

    /**
     * 签约品牌名称
     */
    @Schema(description = "签约品牌名称")
    @Size(max=255,message = "签约品牌名称最大长度不能超过255")
    private String brandName;

    /**
     * 签约品牌业态字典code
     */
    @Schema(description = "签约品牌业态字典code")
    private Long brandCommercialTypeCode;

    /**
     * 签约品牌业态名称
     */
    @Schema(description = "签约品牌业态名称")
    @Size(max=255,message = "签约品牌业态名称最大长度不能超过255")
    private String brandCommercialTypeName;

    /**
     * 签约品牌一级品类
     */
    @Schema(description = "签约品牌一级品类")
    private Long brandCategoryId;

    /**
     * 签约品牌一级品类名称
     */
    @Schema(description = "签约品牌一级品类名称")
    @Size(max=255,message = "签约品牌一级品类名称最大长度不能超过255")
    private String brandCategoryName;

    /**
     * 签约品牌经营范围
     */
    @Schema(description = "签约品牌经营范围")
    @Size(max=255,message = "签约品牌经营范围最大长度不能超过255")
    private String businessScope;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    @Size(max=255,message = "供应商名称最大长度不能超过255")
    private String supplierName;

    /**
     * 身份证号码/统一信用代码
     */
    @Schema(description = "身份证号码/统一信用代码")
    @Size(max=50,message = "身份证号码/统一信用代码最大长度不能超过50")
    private String supplierCertificateCode;

    /**
     * 身份证地址/公司营业执照地址
     */
    @Schema(description = "身份证地址/公司营业执照地址")
    @Size(max=50,message = "身份证地址/公司营业执照地址最大长度不能超过50")
    private String supplierCertificateAddress;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    @NotBlank(message = "联系人姓名不能为空")
    @Size(max=20,message = "联系人姓名最大长度不能超过20")
    private String contactName;

    /**
     * 联系人方式（号码）
     */
    @Schema(description = "联系人方式（号码）")
    @NotBlank(message = "联系人方式（号码）不能为空")
    @Size(max=32,message = "联系人方式（号码）最大长度不能超过32")
    private String contactPhon;

    /**
     * 供应商质量事件
     */
    @Schema(description = "供应商质量事件")
    private Integer supplierQuality;

    /**
     * 供应商安全事故
     */
    @Schema(description = "供应商安全事故")
    private Integer supplierSecurity;

    /**
     * 供应商违约事件
     */
    @Schema(description = "供应商违约事件")
    private Integer supplierViolate;

    /**
     * 签约主体：1公司，2个人
     */
    @Schema(description = "签约主体：1公司，2个人")
    private Integer signEntity;

    /**
     * 签约类型：1 新签，2 续签
     */
    @Schema(description = "签约类型：1 新签，2 续签")
    private Integer contractType;

    /**
     * 经营模式：1 品牌直营，2 品牌代理，3 品牌加盟，4 个体经营
     */
    @Schema(description = "经营模式：1 品牌直营，2 品牌代理，3 品牌加盟，4 个体经营")
    private Integer businessType;

    /**
     * 是否重新装修：0 否 ，1 是
     */
    @Schema(description = "是否重新装修：0 否 ，1 是")
    private Integer decoration;

    /**
     * 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
     */
    @Schema(description = "租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）")
    private Integer rentType;

    /**
     * 租赁起始时间
     */
    @Schema(description = "租赁起始时间")
    private LocalDate rentStartDate;

    /**
     * 租赁结束时间
     */
    @Schema(description = "租赁结束时间")
    private LocalDate rentEndDate;

    /**
     * 商户交付日期
     */
    @Schema(description = "商户交付日期")
    private LocalDate deliveryDate;

    /**
     * 商户计租日期
     */
    @Schema(description = "商户计租日期")
    private LocalDate rentFeeDate;

    /**
     * 商户开业日期
     */
    @Schema(description = "商户开业日期")
    private LocalDate openDate;

    /**
     * 扣点方式，0-固定比例，1-阶梯变化，2-年份变化，只有当租赁类型为纯扣、保底扣（租金）、保底扣（租金+管理费）时才有该字段
     */
    @Schema(description = "扣点方式，0-固定比例，1-阶梯变化，2-年份变化")
    private Integer pointsMode;

    /**
     * 结算日期，扣点方式只有当租赁类型为纯扣、保底扣（租金）、保底扣（租金+管理费）时才有该字段
     */
    @Schema(description = "结算日期")
    private Integer settlementDate;

    /**
     * 租金递增方式,0-固定比例，1-固定金额,递增方式为【固定比例】时，租赁信息中的字段为【租金年递增率（%），递增方式为【固定金额】时，租赁信息中的字段为【租金年递增（元）】
     */
    @Schema(description = "租金递增方式,0-固定比例，1-固定金额")
    private Integer rentIncreaseMode;

    /**
     * 商铺类型：0：正铺；1：临促
     */
    @Schema(description = "商铺类型：0：正铺；1：临促")
    private Integer shopType;
    /**
     * 甲方
     */
    @Schema(description = "甲方")
    private String lessor;
    /**
     * 甲方法定代表人
     */
    @Schema(description = "甲方法定代表人")
    private String lessorRepresentative;
    /**
     * 甲方地址
     */
    @Schema(description = "甲方地址")
    private String lessorAddress;
    /**
     * 第三方
     */
    @Schema(description = "第三方")
    private String thirdParty;
    /**
     * 月运营管理费递增方式,0-比例递增，1-金额递增  2 不递增
     */
    @Schema(description = "月运营管理费递增方式,0-比例递增，1-金额递增  2 不递增")
    private Integer operateIncreaseMode;
    /**
     * 月物业管理费递增方式,0-比例递增 1-金额递增  2-不递增
     */
    @Schema(description = "月物业管理费递增方式,0-比例递增 1-金额递增  2-不递增")
    private Integer manageIncreaseMode;

    /**
     * API接口费首期款支付时间
     */
    @Schema(description = "API接口费首期款支付时间")
    private LocalDateTime apiInitialPaymentTime;

    /**
     * API接口费期数
     */
    @Schema(description = "API接口费期数")
    private Integer apiFeeInstallments;

    /**
     * API接口费每期款项支付时间
     */
    @Schema(description = "API接口费每期款项支付时间")
    @Size(max=255,message = "API接口费每期款项支付时间最大长度不能超过255")
    private String apiInstallmentPaymentTime;

    /**
     * API接口费尾期款支付时间
     */
    @Schema(description = "API接口费尾期款支付时间")
    private LocalDateTime apiFinalPaymentTime;

    /**
     * 产品合作开始时间
     */
    @Schema(description = "产品合作开始时间")
    private LocalDateTime productCooperationStartTime;

    /**
     * 产品合作结束时间
     */
    @Schema(description = "产品合作结束时间")
    private LocalDateTime productCooperationEndTime;

}
