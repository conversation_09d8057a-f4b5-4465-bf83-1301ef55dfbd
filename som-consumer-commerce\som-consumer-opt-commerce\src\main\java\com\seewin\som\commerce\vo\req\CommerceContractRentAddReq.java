package com.seewin.som.commerce.vo.req;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 招商合同租赁信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Getter
@Setter
public class CommerceContractRentAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户名称(项目名称)
     */
    @Schema(description = "租户名称(项目名称)")
    @Size(max=255,message = "租户名称(项目名称)最大长度不能超过255")
    private String tenantName;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @Schema(description = "所属组织ID路径")
    @NotBlank(message = "所属组织ID路径不能为空")
    @Size(max=255,message = "所属组织ID路径最大长度不能超过255")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @Schema(description = "所属组织名称路径")
    @Size(max=255,message = "所属组织名称路径最大长度不能超过255")
    private String orgFname;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    @Size(max=255,message = "合同编号最大长度不能超过255")
    private String contractCode;

    /**
     * 签约年限（年）
     */
    @Schema(description = "签约年限（年）")
    private Integer yearLimit;

    /**
     * 签约年限（年）
     */
    @Schema(description = "签约年限（年）")
    @Size(max=255,message = "签约年限（年）最大长度不能超过255")
    private String yearLimitContract;

    /**
     * 签约年限（年）是否符合：0 否，1 是
     */
    @Schema(description = "签约年限（年）是否符合：0 否，1 是")
    private Integer yearLimitMatch;

    /**
     * 月租金单价（元/㎡）
     */
    @Schema(description = "月租金单价（元/㎡）")
    private BigDecimal monthPrice;

    /**
     * 月租金单价
     */
    @Schema(description = "月租金单价")
    private BigDecimal monthPriceContract;

    /**
     * 月租金总额（元）
     */
    @Schema(description = "月租金总额（元）")
    private BigDecimal monthFeeContract;

    /**
     * 扣点百分率
     */
    @Schema(description = "扣点百分率")
    private Double monthFeePercent;

    /**
     * 月物业管理费单价（元/㎡）
     */
    @Schema(description = "月物业管理费单价（元/㎡）")
    private BigDecimal monthManagePrice;

    /**
     * 月物业管理费单价
     */
    @Schema(description = "月物业管理费单价")
    private BigDecimal monthManagePriceContract;

    /**
     * 月物业管理费单价是否符合：0 否，1 是
     */
    @Schema(description = "月物业管理费单价是否符合：0 否，1 是")
    private Integer monthManagePriceMatch;

    /**
     * 月物业管理费总额（元）
     */
    @Schema(description = "月物业管理费总额（元）")
    private BigDecimal monthManageFeeContract;

    /**
     * 月运营管理费单价（元/㎡）
     */
    @Schema(description = "月运营管理费单价（元/㎡）")
    private BigDecimal monthOperatePrice;

    /**
     * 月运营管理费单价
     */
    @Schema(description = "月运营管理费单价")
    private BigDecimal monthOperatePriceContract;

    /**
     * 月运营管理费单价是否符合：0 否，1 是
     */
    @Schema(description = "月运营管理费单价是否符合：0 否，1 是")
    private Integer monthOperatePriceMatch;

    /**
     * 月运营管理费总额（元）
     */
    @Schema(description = "月运营管理费总额（元）")
    private BigDecimal monthOperateFeeContract;

    /**
     * 其他费用单价（元/㎡）
     */
    @Schema(description = "其他费用单价（元/㎡）")
    private BigDecimal otherPrice;

    /**
     * 其他费用单价
     */
    @Schema(description = "其他费用单价")
    private BigDecimal otherPriceContract;

    /**
     * 其他费用单价是否符合：0 否，1 是
     */
    @Schema(description = "其他费用单价是否符合：0 否，1 是")
    private Integer otherPriceMatch;

    /**
     * 其他费用总额（元）
     */
    @Schema(description = "其他费用总额（元）")
    private BigDecimal otherFeeContract;

    /**
     * 租金年递增率（%）
     */
    @Schema(description = "租金年递增率（%）")
    private BigDecimal incrementalRate;

    /**
     * 租金递增集合
     */
    @Schema(description = "租金递增集合（%）")
    private List<String> incrementalRateList;

    /**
     * 扣点百分率集合
     */
    @Schema(description = "扣点百分率集合")
    private List<String> monthFeePercentList;

    /**
     * 租金递增起年
     */
    @Schema(description = "租金递增起年")
    private BigDecimal incrementalStartContract;

    /**
     * 租金递增间隔
     */
    @Schema(description = "租金递增间隔")
    private BigDecimal incrementalIntervalContract;

    /**
     * 租金年递增率（%）
     */
    @Schema(description = "租金年递增率（%）")
    private BigDecimal incrementalRateContract;

    /**
     * 租金年递增率是否符合：0 否，1 是
     */
    @Schema(description = "租金年递增率是否符合：0 否，1 是")
    private Integer incrementalRateMatch;

    /**
     * 装修免租期（天）
     */
    @Schema(description = "装修免租期（天）")
    private Integer feeFree;

    /**
     * 装修免租期
     */
    @Schema(description = "装修免租期")
    private Integer feeFreeContract;

    /**
     * 装修免租期是否符合：0 否，1 是
     */
    @Schema(description = "装修免租期是否符合：0 否，1 是")
    private Integer feeFreeMatch;

    /**
     * 综合管理费（元）
     */
    @Schema(description = "综合管理费（元）")
    private BigDecimal compositeManageFee;

    /**
     * 综合管理费
     */
    @Schema(description = "综合管理费")
    private BigDecimal compositeManageFeeContract;

    /**
     * 综合管理费是否符合：0 否，1 是
     */
    @Schema(description = "综合管理费是否符合：0 否，1 是")
    private Integer compositeManageFeeMatch;

    /**
     * 租赁保证金（元）
     */
    @Schema(description = "租赁保证金（元）")
    private BigDecimal rentBailFee;

    /**
     * 租赁保证金
     */
    @Schema(description = "租赁保证金")
    private BigDecimal rentBailFeeContract;

    /**
     * 租赁保证金是否符合：0 否，1 是
     */
    @Schema(description = "租赁保证金是否符合：0 否，1 是")
    private Integer rentBailFeeMatch;

    /**
     * 物业管理费保证金（元）
     */
    @Schema(description = "物业管理费保证金（元）")
    private BigDecimal propertyManageBailFee;

    /**
     * 物业管理费保证金
     */
    @Schema(description = "物业管理费保证金")
    private BigDecimal propertyManageBailFeeContract;

    /**
     * 物业管理费保证金是否符合：0 否，1 是
     */
    @Schema(description = "物业管理费保证金是否符合：0 否，1 是")
    private Integer propertyManageBailFeeMatch;

    /**
     * 运营管理费保证金（元）
     */
    @Schema(description = "运营管理费保证金（元）")
    private BigDecimal operateManageBailFee;

    /**
     * 运营管理费保证金
     */
    @Schema(description = "运营管理费保证金")
    private BigDecimal operateManageBailFeeContract;

    /**
     * 运营管理费保证金是否符合：0 否，1 是
     */
    @Schema(description = "运营管理费保证金是否符合：0 否，1 是")
    private Integer operateManageBailFeeMatch;

    /**
     * 公共事业保证金（元）
     */
    @Schema(description = "公共事业保证金（元）")
    private BigDecimal commonBailFee;

    /**
     * 公共事业保证金
     */
    @Schema(description = "公共事业保证金")
    private BigDecimal commonBailFeeContract;

    /**
     * 公共事业保证金是否符合：0 否，1 是
     */
    @Schema(description = "公共事业保证金是否符合：0 否，1 是")
    private Integer commonBailFeeMatch;

    /**
     * 装修押金（元）
     */
    @Schema(description = "装修押金（元）")
    private BigDecimal decorationBailFee;

    /**
     * 装修押金
     */
    @Schema(description = "装修押金")
    private BigDecimal decorationBailFeeContract;

    /**
     * 装修押金是否符合：0 否，1 是
     */
    @Schema(description = "装修押金是否符合：0 否，1 是")
    private Integer decorationBailFeeMatch;

    /**
     * POS机租金（元）
     */
    @Schema(description = "POS机租金（元）")
    private BigDecimal posRentFee;

    /**
     * POS机押金（元）
     */
    @Schema(description = "POS机押金（元）")
    private BigDecimal posBailFee;

    /**
     * 装修期物业管理费（元）
     */
    @Schema(description = "装修期物业管理费（元）")
    private BigDecimal decorationPropertyFee;

    /**
     * 装修期运营管理费（元）
     */
    @Schema(description = "装修期运营管理费（元）")
    private BigDecimal decorationOperateFee;

    /**
     * 垃圾清运费（元）
     */
    @Schema(description = "垃圾清运费（元）")
    private BigDecimal cleanFee;

    /**
     * 月保底销售额
     */
    @Schema(description = "月保底销售额")
    private BigDecimal guarantSaleContract;
    /**
     * 月运营管理费递增集合
     */
    @Schema(description = "月运营管理费递增集合")
    private List<String> operateIncrementalRateList;

    /**
     * 月物业管理费递增集合
     */
    @Schema(description = "月物业管理费递增集合")
    private List<String> manageIncrementalRateList;

    /**
     * 扣点百分率 是否符合：0 否，1 是
     */
    @Schema(description = "扣点百分率 是否符合：0 否，1 是")
    private Integer monthFeePercentMatch;

    /**
     * 月租金单价是否符合：0 否，1 是
     */
    @Schema(description = "月租金单价是否符合：0 否，1 是")
    private Integer monthPriceMatch;

    /**
     * 月租金总额（元）
     */
    @Schema(description = "月租金总额（元）")
    private BigDecimal monthFee;

    /**
     * 月租金总额是否符合：0 否，1 是
     */
    @Schema(description = "月租金总额是否符合：0 否，1 是")
    private Integer monthFeeMatch;

    /**
     * 月物业管理费总额（元）
     */
    @Schema(description = "月物业管理费总额（元）")
    private BigDecimal monthManageFee;

    /**
     * 月物业管理费总额是否符合：0 否，1 是
     */
    @Schema(description = "月物业管理费总额是否符合：0 否，1 是")
    private Integer monthManageFeeMatch;

    /**
     * 月运营管理费总额（元）
     */
    @Schema(description = "月运营管理费总额（元）")
    private BigDecimal monthOperateFee;

    /**
     * 月运营管理费总额是否符合：0 否，1 是
     */
    @Schema(description = "月运营管理费总额是否符合：0 否，1 是")
    private Integer monthOperateFeeMatch;

    /**
     * 其他费用总额（元）
     */
    @Schema(description = "其他费用总额（元）")
    private BigDecimal otherFee;

    /**
     * 其他费用总额是否符合：0 否，1 是
     */
    @Schema(description = "其他费用总额是否符合：0 否，1 是")
    private Integer otherFeeMatch;

    /**
     * API接口费
     */
    @Schema(description = "API接口费")
    private BigDecimal apiFee;

    /**
     * API接口费总款项
     */
    @Schema(description = "API接口费总款项")
    private BigDecimal apiFeeTotalAmount;

    /**
     * API接口费首期款
     */
    @Schema(description = "API接口费首期款")
    private BigDecimal apiInitialPaymentAmount;

    /**
     * API接口费每期款项
     */
    @Schema(description = "API接口费每期款项")
    private BigDecimal apiInstallmentAmount;

    /**
     * API接口费尾期款
     */
    @Schema(description = "API接口费尾期款")
    private BigDecimal apiFinalPaymentAmount;

    /**
     * 产品保证金
     */
    @Schema(description = "产品保证金")
    private BigDecimal productSecurityDeposit;
}
