package com.seewin.som.commerce.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/3/29 11:53
 * @Version 1.0
 * @description
 **/
@Getter
@Setter
public class CommerceEntExitContractAddReq extends CommerceContractAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "进撤场工单id")
    private Long entExitId;

    /**
     * 门店是否续签：0 否 ，1 是
     */
    @Schema(description = "门店是否续签：0 否 ，1 是")
    private Integer hasRenewal;

    /**
     * 续签暂存合同id
     */
    @Schema(description = "续签暂存合同id")
    private Long renewalId;
    
}
