package com.seewin.som.commerce.vo.resp;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import com.cscec1b.consumer.vo.FileResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招商合同表-商铺
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Getter
@Setter
public class CommerceContractGetResp implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 租户名称(项目名称)
     */
    @Schema(description = "租户名称(项目名称)")
    private String tenantName;
    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractCode;
    /**
     * 合同归档时间
     */
    @Schema(description = "合同归档时间")
    private LocalDate archiveDate;
    /**
     * 招商传签申请日期
     */
    @Schema(description = "招商传签申请日期")
    private LocalDate applyDate;
    /**
     * 招商传签人账号
     */
    @Schema(description = "招商传签人账号")
    private String executeUser;
    /**
     * 招商传签人姓名
     */
    @Schema(description = "招商传签人姓名")
    private String executeUserName;

    /**
     * 招商传签人所属组织编码
     */
    @Schema(description = "招商传签人所属组织编码")
    private String executeUserFid;

    /**
     * 招商传签人所属组织名称
     */
    @Schema(description = "招商传签人所属组织名称")
    private String executeUserFname;
    /**
     * 空间完整编码
     */
    @Schema(description = "空间完整编码")
    private String fcode;
    /**
     * 空间完整名称
     */
    @Schema(description = "空间完整名称")
    private String fname;
    /**
     * 房间ID
     */
    @Schema(description = "房间ID")
    private Long roomId;
    /**
     * 房间名称
     */
    @Schema(description = "房间名称")
    private String name;
    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private String roomShopId;
    /**
     * 商铺实用面积
     */
    @Schema(description = "商铺实用面积")
    private Double actualArea;
    /**
     * 商铺记租面积
     */
    @Schema(description = "商铺记租面积")
    private Double rentArea;
    /**
     * 铺位状态：1空置，2在营，3已签约（字典值）
     */
    @Schema(description = "铺位状态：1空置，2在营，3已签约（字典值）")
    private Integer roomStatus;
    /**
     * 空置开始时间
     */
    @Schema(description = "空置开始时间")
    private LocalDate emptyStartDate;
    /**
     * (上次)合同到期时间
     */
    @Schema(description = "(上次)合同到期时间")
    private LocalDate lastContractDate;
    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    private Long brandId;
    /**
     * 签约品牌名称
     */
    @Schema(description = "签约品牌名称")
    private String brandName;
    /**
     * 适配业态字典id
     */
    @Schema(description = "适配业态字典id")
    private Long commercialTypeCode;
    /**
     * 适配业态名称
     */
    @Schema(description = "适配业态名称")
    private String commercialTypeName;
    /**
     * 所属一级品类
     */
    @Schema(description = "所属一级品类")
    private Long categoryId;
    /**
     * 所属一级品类名称
     */
    @Schema(description = "所属一级品类名称")
    private String categoryName;
    /**
     * 电量
     */
    @Schema(description = "电量")
    private Integer electricity;
    /**
     * 给水
     */
    @Schema(description = "给水")
    private Integer waterSupply;
    /**
     * 排水
     */
    @Schema(description = "排水")
    private Integer drainage;
    /**
     * 排油烟量
     */
    @Schema(description = "排油烟量")
    private Integer exhaustFumes;
    /**
     * 签约信息、品牌及供应商信息
     */
    @Schema(description = "签约信息、品牌及供应商信息")
    private CommerceContractInfoGetResp contractInfo;
    /**
     * 租赁信息
     */
    @Schema(description = "租赁信息")
    private CommerceContractRentGetResp contractRent;

    @Schema(description = "合同附件")
    private List<FileResp> files = Collections.emptyList();

    @Schema(description = "所属城市公司")
    private String ogn;

    @Schema(description = "所属部门")
    private String dpt;

    /**
     * 数据来源 0 手动新增 1 初始化
     */
    @Schema(description = "数据来源 0 手动新增 1 初始化")
    private Integer dataType;

    /**
     * 用户输入
     */
    @Schema(description = "用户输入")
    private String userInput;

    @Schema(description = "项目地址")
    private String faddressdetail;

    /**
     * 合同模板类型, 0-普通模板 1-科技模板 2-小程序模板
     */
    @Schema(description = "合同模板类型, 0-普通模板 1-科技模板 2-小程序模板")
    private Integer templateType;

}
