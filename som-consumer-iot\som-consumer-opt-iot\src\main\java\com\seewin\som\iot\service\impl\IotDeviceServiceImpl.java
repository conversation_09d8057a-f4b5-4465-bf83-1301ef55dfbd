package com.seewin.som.iot.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.model.base.OptUser;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.resp.EntProjectGetVo;
import com.seewin.som.iot.enums.DeviceTypeEnum;
import com.seewin.som.iot.provider.*;
import com.seewin.som.iot.req.*;
import com.seewin.som.iot.resp.*;
import com.seewin.som.iot.service.IotDeviceService;
import com.seewin.som.iot.vo.req.*;
import com.seewin.som.iot.vo.resp.*;
import com.seewin.som.storage.provider.SysFileProvider;
import com.seewin.som.storage.resp.SysFileGetVo;
import com.seewin.system.service.FileService;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.seewin.som.iot.enums.DeviceTypeEnum.FLOW_DEVICE;
import static com.seewin.som.iot.enums.DeviceTypeEnum.SALE_DEVICE;

/**
 * <p>
 * 客采设备台账 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Service
@Slf4j
public class IotDeviceServiceImpl implements IotDeviceService {

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-iot-mgt")
    private IotDeviceProvider iotDeviceProvider;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotFlowBindProvider iotFlowBindProvider;

    @DubboReference
    private IotDeviceImportDetailProvider iotDeviceImportDetailProvider;

    @DubboReference
    private IotDeviceImportProvider iotDeviceImportProvider;

    @DubboReference
    private SysFileProvider sysFileProvider;

    @Autowired
    private FileService fileService;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotSaleBindProvider iotSaleBindProvider;

    @Value("${iot.deviceUrl:''}")
    private String iotUrl;

    /**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<IotDeviceListItem> page(IotDeviceListReq listReq) {
        PageResp<IotDeviceListItem> pageResp = new PageResp<>();
        OptUser curUser = ApiUtils.getUser(OptUser.class);
//        if (curUser.getOrgLevel() != 4) {
//            return pageResp;
//        }
        IotDeviceListDto queryDto = BeanUtils.copyProperties(listReq, IotDeviceListDto.class);
        queryDto.setEntId(curUser.getEntId());
        queryDto.setTenantId(curUser.getTenantId());


        PageQuery<IotDeviceListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<IotDeviceListVo> pageResult = iotDeviceProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), IotDeviceListItem.class));

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public IotDeviceGetResp get(IotDeviceGetReq getReq) {

        IotDeviceGetVo getVo = iotDeviceProvider.get(getReq.getId());

        IotDeviceGetResp getResp = BeanUtils.copyProperties(getVo, IotDeviceGetResp.class);

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public IotDeviceAddResp add(IotDeviceAddReq addReq) {
        IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
        iotDeviceListDto.setDeviceType(addReq.getDeviceType());
        iotDeviceListDto.setDeviceId(addReq.getDeviceId());
        int count = iotDeviceProvider.count(iotDeviceListDto);
        if (count != 0) {
            throw new ServiceException("该设备ID在其它项目已存在");
        }
        IotDeviceAddDto dto = BeanUtils.copyProperties(addReq, IotDeviceAddDto.class);
        //设置创建人信息
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        dto.setCreateBy(optUser.getUserId());
        dto.setCreateUser(optUser.getUserName());
        dto.setCreateUserName(optUser.getRealName());
        dto.setEntId(optUser.getEntId());
        dto.setOrgFid(optUser.getOrgFid());
        dto.setOrgFname(optUser.getOrgFname());
        dto.setTenantName(optUser.getTenantName());
        dto.setTenantId(optUser.getTenantId());

        EntProjectGetVo entProjectGetVo = entProjectProvider.get(optUser.getTenantId());
        String projectCode = entProjectGetVo.getCode();
        String devCode = iotDeviceProvider.getMaxDevCode(optUser.getTenantId(), addReq.getDeviceType());

        //获取生成的设备编码
        List<String> deviceCodeList = getGenerateDeviceCode(addReq.getDeviceType(), devCode, projectCode, 1);
        if (CollectionUtils.isNotEmpty(deviceCodeList)) {
            dto.setDeviceCode(deviceCodeList.get(0));
        }
        // TODO 设备状态处理
        dto.setDeviceStatus(1);
        dto.setDeviceEnable(0);
        dto.setBindStatus(0);
        IotDeviceAddVo addVo = iotDeviceProvider.add(dto);

        IotDeviceAddResp addResp = BeanUtils.copyProperties(addVo, IotDeviceAddResp.class);

        return addResp;
    }
    /**
     * 获取生成的设备编码
     * <p>
     * quantity 需要生成的数量
     *
     * @return
     */
    private static List<String> getGenerateDeviceCode(String deviceType, String devCode, String projectCode, int quantity) {
        List<String> deviceCodeList = new ArrayList<>();
        String prefix = "";
        Integer num = 0;
        devCode = ObjectUtils.isEmpty(devCode) ? "000" : devCode;
        if (deviceType.equals("flow")) {
            prefix = "-KL";
        } else if (deviceType.equals("fire")) {
            prefix = "-DH";
        } else if (deviceType.equals("lkl")) {
            prefix = "-LKL";
        } else {
            prefix = "-XS";
        }
        int klIndex = devCode.indexOf(prefix);
        int startIndex = klIndex + (prefix.length());
        int endIndex = devCode.length();
        String is = devCode.substring(startIndex, endIndex);
        if (StringUtils.isBlank(is)) {
            num = 0;
        }else{
            num = Integer.valueOf(is);
        }
        for (int i = 1; i <= quantity; i++) {
            String suffix = String.format("%03d", ++num);
            deviceCodeList.add(projectCode + prefix + suffix);
        }
        return deviceCodeList;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(IotDeviceEditReq editReq) {
        IotDeviceEditDto dto = BeanUtils.copyProperties(editReq, IotDeviceEditDto.class);

        //设置修改人信息
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());

        iotDeviceProvider.edit(dto);


        //修改绑定数据关系
        IotFlowBindListDto iotFlowBindListDto = new IotFlowBindListDto();
        iotFlowBindListDto.setDevicePkId(dto.getId());
        List<IotFlowBindListVo> list = iotFlowBindProvider.list(iotFlowBindListDto);

        for (IotFlowBindListVo vo : list) {
            IotFlowBindEditDto iotFlowBindEditDto = BeanUtils.copyProperties(vo, IotFlowBindEditDto.class);
            iotFlowBindEditDto.setDeviceId(dto.getDeviceId());
            iotFlowBindEditDto.setDeviceCode(dto.getDeviceCode());
            iotFlowBindEditDto.setDeviceName(dto.getDeviceName());

            iotFlowBindProvider.edit(iotFlowBindEditDto);

        }
        IotSaleBindListDto iotSaleBindListDto = new IotSaleBindListDto();
        iotSaleBindListDto.setDevicePkId(dto.getId());
        List<IotSaleBindListVo> list1 = iotSaleBindProvider.list(iotSaleBindListDto);
        for (IotSaleBindListVo vo : list1) {
            IotSaleBindEditDto iotSaleBindEditDto = BeanUtils.copyProperties(vo, IotSaleBindEditDto.class);
            iotSaleBindEditDto.setDeviceId(dto.getDeviceId());
            iotSaleBindEditDto.setDeviceCode(dto.getDeviceCode());
            iotSaleBindEditDto.setDeviceName(dto.getDeviceName());

            iotSaleBindProvider.edit(iotSaleBindEditDto);

        }

    }

    /**
     * 批量删除
     *
     * @param delReq
     */
    @Override
    public void batchDel(IotDeviceDelReq delReq) {
        iotDeviceProvider.batchDel(delReq.getIds(), delReq.getDeviceType());
    }

    /**
     * 设备停启用
     *
     * @param editReq
     */
    @Override
    public void deviceEnable(IotDeviceEditReq editReq) {
        IotDeviceEditDto dto = BeanUtils.copyProperties(editReq, IotDeviceEditDto.class);
        //设置修改人信息
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        dto.setDeviceEnable(editReq.getDeviceEnable());
        iotDeviceProvider.edit(dto);
    }


    /**
     * 项目下的设备列表/根据楼层编码获取设备列表
     *
     * @param listReq
     * @return
     */
    @Override
    public List<IotDeviceListItem> list(IotDeviceListReq listReq) {
        //设置修改人信息
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        IotDeviceListDto iotDeviceListDto = BeanUtils.copyProperties(listReq, IotDeviceListDto.class);
        iotDeviceListDto.setTenantId(curUser.getTenantId());

        List<IotDeviceListVo> list = iotFlowBindProvider.listInfo(iotDeviceListDto);


        List<IotDeviceListItem> listItems = BeanUtils.copyProperties(list, IotDeviceListItem.class);
        return listItems;
    }

    @Override
    public void exp(IotDeviceExpReq expReq) {
        List<IotDeviceListVo> items = iotDeviceProvider.listByIds(expReq.getIdList());
        int index = 1;
        ArrayList<IotDeviceExpResp> resps = new ArrayList<>();
        for (IotDeviceListVo item : items) {
            IotDeviceExpResp resp = new IotDeviceExpResp();
            resp.setNum(index);
            resp.setDeviceName(item.getDeviceName());
            resp.setTenantName(item.getTenantName());
            resp.setDeviceCode(item.getDeviceCode());
            resp.setDeviceId(item.getDeviceId());
            resp.setDeviceModel(item.getDeviceModel());
            resp.setDeviceStatus(item.getDeviceStatus() == 0 ? "在线" : item.getDeviceStatus() == 1 ? "离线" : "报警");
            resp.setCreateUserName(item.getCreateUserName());
            resp.setCreateTime(defaultFormatYMD(item.getCreateTime()));
            resps.add(resp);
            index++;
        }
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            //设置水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String fileName = "设备台账导出";
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
            EasyExcel.write(response.getOutputStream(), IotDeviceExpResp.class).registerWriteHandler(horizontalCellStyleStrategy).sheet("设备台账").doWrite(resps);
        } catch (Exception e) {
            // log.error("设备台账导出 ", e);
        }


    }

    @Override
    public void syncDevice(DeviceTypeEnum deviceTypeEnum) {
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        if (ObjectUtils.isEmpty(optUser.getTenantId())) {
            throw new ServiceException("请选择项目后进行同步");
        }
        EntProjectGetVo entProjectGetVo = entProjectProvider.get(optUser.getTenantId());
//        iotDeviceProvider.syncDevice(deviceTypeEnum,entProjectGetVo.getCode(),optUser);

        Long lastUpdateTime = iotDeviceProvider.getLastUpdateTime(optUser.getTenantId(), deviceTypeEnum);
        IotDeviceListDto listDto = new IotDeviceListDto();
        listDto.setDeviceType(deviceTypeEnum.getDeviceType());
        List<IotDeviceListVo> deviceListVos = iotDeviceProvider.list(listDto);
        Map<String, IotDeviceListVo> deviceMap = new HashMap<>();
        deviceListVos.forEach(e -> deviceMap.put(e.getDeviceId(), e));
        String projectCode = entProjectGetVo.getCode();
        if (ObjectUtils.isEmpty(projectCode)
                || !projectCode.contains(".")) {
            throw new ServiceException("项目编码不符合规范");
        }
        //同步数据
        String syncUrl = String.format(iotUrl, deviceTypeEnum.getIotDeviceType(), projectCode, lastUpdateTime);
        JSONArray deviceArray = getDeviceArray(syncUrl);

        if (deviceTypeEnum.getDeviceType().equals(FLOW_DEVICE.getDeviceType())) {
            //同步区域类型的客流设备数据（projectId 参数是全code）
            //景阳客流
            String areaFlowSyncUrl = String.format(iotUrl, DeviceTypeEnum.JYKL_DEVICE.getIotDeviceType(), projectCode, lastUpdateTime);
            JSONArray areaFlowArray = getDeviceArray(areaFlowSyncUrl);
            deviceArray.addAll(areaFlowArray);
            //旷世客流
            areaFlowSyncUrl = String.format(iotUrl, DeviceTypeEnum.KSKL_DEVICE.getIotDeviceType(), projectCode, lastUpdateTime);
            areaFlowArray = getDeviceArray(areaFlowSyncUrl);
            deviceArray.addAll(areaFlowArray);
        }

        //同步销售设备，需要多同步拉卡拉设备台账, 还需要同步二代小票打印机设备台账
        if (deviceTypeEnum.getDeviceType().equals(SALE_DEVICE.getDeviceType())) {
            // 拉卡拉
            String lklSyncUrl = String.format(iotUrl, DeviceTypeEnum.SALE_DEVICE_LKLPAY.getIotDeviceType(), projectCode, lastUpdateTime);
            JSONArray lklDeviceArray = getDeviceArray(lklSyncUrl);
            // 二代小票打印机（二代销售设备）
            String secondXpSyncUrl = String.format(iotUrl, DeviceTypeEnum.SALE_DEVICE_SECOND.getIotDeviceType(), projectCode, lastUpdateTime);
            JSONArray secondXpDeviceArray = getDeviceArray(secondXpSyncUrl);
            // 拾数平台外卖设备
            String wmSyncUrl = String.format(iotUrl, DeviceTypeEnum.SALE_DEVICE_WM.getIotDeviceType(), projectCode, lastUpdateTime);
            JSONArray wmDeviceArray = getDeviceArray(wmSyncUrl);

            //销售设备
            checkAddByProduct(optUser, SALE_DEVICE.getDeviceType(), SALE_DEVICE.getIotDeviceType(), deviceArray, projectCode, entProjectGetVo.getId(), deviceMap);
            //拉卡拉设备编码区分
            checkAddByProduct(optUser, DeviceTypeEnum.SALE_DEVICE_LKLPAY.getDeviceType(), DeviceTypeEnum.SALE_DEVICE_LKLPAY.getIotDeviceType(), lklDeviceArray, projectCode, entProjectGetVo.getId(),  deviceMap);
            // 二代小票打印机（二代销售设备）
            checkAddByProduct(optUser, DeviceTypeEnum.SALE_DEVICE_SECOND.getDeviceType(), DeviceTypeEnum.SALE_DEVICE_SECOND.getIotDeviceType(), secondXpDeviceArray, projectCode, entProjectGetVo.getId(),  deviceMap);
            // 拾数平台外卖设备
            checkAddByProduct(optUser, DeviceTypeEnum.SALE_DEVICE_WM.getDeviceType(), DeviceTypeEnum.SALE_DEVICE_WM.getIotDeviceType(), wmDeviceArray, projectCode, entProjectGetVo.getId(),  deviceMap);
            return;
        }

        checkAdd(optUser, deviceTypeEnum.getDeviceType(), deviceArray, projectCode, entProjectGetVo.getId(), deviceMap, entProjectGetVo);

    }

    @Override
    public IotDeviceGetOfflineResp getOfflineCount() {
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        Long tenantId = optUser.getTenantId();
        IotDeviceGetOfflineVo offlineVo = iotDeviceProvider.getOfflineCount(tenantId);
        IotDeviceGetOfflineResp resp = BeanUtils.copyProperties(offlineVo, IotDeviceGetOfflineResp.class);
        return resp;
    }

    /**
     * 获取项目下的设备list
     *
     * @param syncUrl
     * @return
     */
    private JSONArray getDeviceArray(String syncUrl) {
        //同步数据
        Response response = null;
        String responseBody = null;
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        Request request = new Request.Builder()
                .url(syncUrl)
                .method("GET", null)
                .build();
        try {
            response = client.newCall(request).execute();
            responseBody = response.body().string();
        } catch (Exception e) {
            log.error("同步数据异常：{}", e.getMessage());
        }
        log.info("同步数据[{}],:[{}]", syncUrl, responseBody);
        response.close();
        JSONArray array = JSONObject.parseObject(responseBody).getJSONObject("data").getJSONArray("data");
        return array;
    }

    /**
     * @param optUser
     * @param deviceType
     * @param array
     * @param projectCode
     * @param deviceMap
     */
    private void checkAddByProduct(OptUser optUser, String deviceType, String iotDeviceType, JSONArray array, String projectCode, Long projectId,
                                   Map<String, IotDeviceListVo> deviceMap) {
        String devCode = iotDeviceProvider.getMaxDevCodeByProduct(optUser.getTenantId(), deviceType, iotDeviceType);
        if (ObjectUtils.isEmpty(array) && array.size() == 0) {
            return;
        }
        //获取生成的设备编码
        List<String> deviceCodeList = getGenerateDeviceCode(iotDeviceType, devCode, projectCode, array.size());
        //设备编码所使用的下标
        int applyIndex = 0;
        List<IotDeviceEditDto> deviceEditDtoList = new ArrayList<>();
        List<IotDeviceAddDto> deviceAddDtoList = new ArrayList<>();
        //需要标记为删除的id
        List<Long> deleteDeviceIdList = new ArrayList<>();
        //fixme:更换项目
        for (int i = 0; i < array.size(); i++) {
            boolean isSave = true;
            IotDeviceItemVo item = array.getObject(i, IotDeviceItemVo.class);
            //如果存在则判断项目是否一致
            if (deviceMap.containsKey(item.getDeviceSn())) {
                IotDeviceListVo device = deviceMap.get(item.getDeviceSn());
                if (item.getDelFlag().equals(2)){
                    if(projectId.equals(device.getTenantId())) {
                        deleteDeviceIdList.add(device.getId());
                    }
                    continue;
                }
                //项目是否一致，不一致标记之前的为删除，重新新增
                if (!device.getTenantId().equals(optUser.getTenantId())) {
                    deleteDeviceIdList.add(device.getId());
                } else {
                    //如果一致需要修改
                    IotDeviceEditDto editDto = BeanUtils.copyProperties(device, IotDeviceEditDto.class);
                    editDto.setDeviceName(item.getDeviceName());
                    editDto.setDeviceType(deviceType);
                    editDto.setDeviceStatus(synDeviceStatus(item.getState()));
                    editDto.setDeviceEnable(fetchEnableByState(item.getDeviceState()));
                    editDto.setSyncTime(LocalDateTime.now());
                    deviceEditDtoList.add(editDto);
                    isSave = false;
                }
            }
            if (item.getDelFlag().equals(2)) {
                continue;
            }
            if (isSave) {
                IotDeviceAddDto device = new IotDeviceAddDto();
                device.setDeviceName(item.getDeviceName());
                device.setDeviceId(item.getDeviceSn());
                device.setDeviceCode(deviceCodeList.get(applyIndex));
                device.setDeviceType(deviceType);
                device.setDeviceModel(StringUtils.EMPTY);
                device.setDeviceStatus(synDeviceStatus(item.getState()));
                device.setDeviceEnable(fetchEnableByState(item.getDeviceState()));
                device.setBindStatus(0);
                device.setCreateBy(optUser.getUserId());
                device.setCreateUser(optUser.getUserName());
                device.setCreateUserName(optUser.getRealName());
                device.setEntId(optUser.getEntId());
                device.setOrgFid(optUser.getOrgFid());
                device.setOrgFname(optUser.getOrgFname());
                device.setTenantName(optUser.getTenantName());
                device.setTenantId(optUser.getTenantId());
                //区域类型的需要传入产品ID+名称
                device.setProductId(item.getProductId());
                device.setProductName(item.getProjectName());
                device.setProductTypeKey(item.getProductTypeKey());
                device.setSyncTime(LocalDateTime.now());
                deviceAddDtoList.add(device);
                applyIndex++;
            }
        }
        iotDeviceProvider.syncDeviceUpdate(deviceEditDtoList, deviceAddDtoList, deleteDeviceIdList);
    }

    /**
     * @param optUser
     * @param deviceType
     * @param array
     * @param projectCode
     * @param deviceMap
     * @param entProjectGetVo
     */
    private void checkAdd(OptUser optUser, String deviceType, JSONArray array, String projectCode,Long projectId,
                          Map<String, IotDeviceListVo> deviceMap, EntProjectGetVo entProjectGetVo) {
        String devCode = iotDeviceProvider.getMaxDevCode(optUser.getTenantId(), deviceType);
        if (ObjectUtils.isEmpty(array) && array.size() == 0) {
            return;
        }
        //获取生成的设备编码
        List<String> deviceCodeList = getGenerateDeviceCode(deviceType, devCode, projectCode, array.size());
        //设备编码所使用的下标
        int applyIndex = 0;
        List<IotDeviceEditDto> deviceEditDtoList = new ArrayList<>();
        List<IotDeviceAddDto> deviceAddDtoList = new ArrayList<>();
        //需要标记为删除的id
        List<Long> deleteDeviceIdList = new ArrayList<>();
        //fixme:更换项目
        for (int i = 0; i < array.size(); i++) {
            boolean isSave = true;
            IotDeviceItemVo item = array.getObject(i, IotDeviceItemVo.class);
            //如果存在则判断项目是否一致
            if (deviceMap.containsKey(item.getDeviceSn())) {
                IotDeviceListVo device = deviceMap.get(item.getDeviceSn());
                if (item.getDelFlag().equals(2)) {
                    if(projectId.equals(device.getTenantId())) {
                        deleteDeviceIdList.add(device.getId());
                    }
                    continue;
                }
                //项目是否一致，不一致标记之前的为删除，重新新增
                if (!device.getTenantId().equals(optUser.getTenantId())) {
                    deleteDeviceIdList.add(device.getId());
                } else {
                    //如果一致需要修改
                    IotDeviceEditDto editDto = BeanUtils.copyProperties(device, IotDeviceEditDto.class);
                    editDto.setDeviceName(item.getDeviceName());
                    editDto.setDeviceType(deviceType);
                    editDto.setDeviceStatus(synDeviceStatus(item.getState()));
                    editDto.setDeviceEnable(fetchEnableByState(item.getDeviceState()));
                    editDto.setSyncTime(LocalDateTime.now());
                    deviceEditDtoList.add(editDto);
                    isSave = false;
                }
            }
            if (item.getDelFlag().equals(2)) {
                continue;
            }
            if (isSave) {
                IotDeviceAddDto device = new IotDeviceAddDto();
                device.setDeviceName(item.getDeviceName());
                device.setDeviceId(item.getDeviceSn());
                device.setDeviceCode(deviceCodeList.get(applyIndex));
                device.setDeviceType(deviceType);
                device.setDeviceModel(StringUtils.EMPTY);
                device.setDeviceStatus(synDeviceStatus(item.getState()));
                device.setDeviceEnable(fetchEnableByState(item.getDeviceState()));
                device.setBindStatus(0);
                device.setCreateBy(optUser.getUserId());
                device.setCreateUser(optUser.getUserName());
                device.setCreateUserName(optUser.getRealName());
                device.setEntId(optUser.getEntId());
                device.setOrgFid(optUser.getOrgFid());
                device.setOrgFname(optUser.getOrgFname());
                device.setTenantName(optUser.getTenantName());
                device.setTenantId(optUser.getTenantId());
                //区域类型的需要传入产品ID+名称
                device.setProductId(item.getProductId());
                device.setProductName(item.getProjectName());
                device.setProductTypeKey(item.getProductTypeKey());
                device.setSyncTime(LocalDateTime.now());
                deviceAddDtoList.add(device);
                applyIndex++;
            }
        }
        iotDeviceProvider.syncDeviceUpdate(deviceEditDtoList, deviceAddDtoList, deleteDeviceIdList);
    }

    /**
     * 设备状态
     *
     * @return
     */
    private Integer synDeviceStatus(Integer deviceStatus) {
        Integer status = 1;
        if (ObjectUtils.isNotEmpty(deviceStatus) && deviceStatus.equals(1)) {
            status = 0;
        }
        return status;
    }

    private Integer fetchDelStatus(Integer delFlag) {
        if (Objects.nonNull(delFlag)) {
            switch (delFlag.intValue()) {
                case 0:
                    return 0;
            }
        }
        return 1;
    }

    private Integer fetchStatusByState(Integer deviceState) {
        if (Objects.nonNull(deviceState)) {
            switch (deviceState.intValue()) {
                case 1:
                    return 0;
            }
        }
        return 1;
    }

    private Integer fetchEnableByState(Integer deviceState) {
        if (Objects.nonNull(deviceState)) {
            switch (deviceState.intValue()) {
                case 0:
                case 1:
                case 2:
                    return 0;
            }
        }
        return 1;
    }

    private static String defaultFormatYMD(LocalDateTime date) {
        if (null != date) {
            // 创建一个 DateTimeFormatter
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 使用 formatter 将 LocalDate 格式化为字符串
            return date.format(formatter);
        } else {
            return null;
        }
    }

    /**
     * 导入记录
     *
     * @param listReq
     * @return
     */
    @Override
    public PageResp<IotDeviceImportListItem> importPage(IotDeviceImportListReq listReq) {
        PageResp<IotDeviceImportListItem> pageResp = new PageResp<>();
        User curUser = ApiUtils.getUser(User.class);

        IotDeviceImportListDto queryDto = BeanUtils.copyProperties(listReq, IotDeviceImportListDto.class);
        queryDto.setTenantId(curUser.getTenantId());

        PageQuery<IotDeviceImportListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<IotDeviceImportListVo> pageResult = iotDeviceImportProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), IotDeviceImportListItem.class));

        return pageResp;
    }


    /**
     * 导入明细
     *
     * @param listReq
     * @return
     */
    @Override
    public PageResp<IotDeviceImportDetailListItem> importDetailPage(IotDeviceImportDetailListReq listReq) {
        PageResp<IotDeviceImportDetailListItem> pageResp = new PageResp<>();
        User curUser = ApiUtils.getUser(User.class);

        IotDeviceImportDetailListDto queryDto = BeanUtils.copyProperties(listReq, IotDeviceImportDetailListDto.class);

        PageQuery<IotDeviceImportDetailListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<IotDeviceImportDetailListVo> pageResult = iotDeviceImportDetailProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), IotDeviceImportDetailListItem.class));

        return pageResp;
    }


    /**
     * 导入
     *
     * @param fileId
     * @param deviceType
     * @return
     */

    @Override
    public String importExcel(Long fileId, String deviceType) {
        String result = null;
        try {
            if (deviceType.equals("flow")) {
                result = flowImport(fileId, deviceType);
            } else {
                result = saleImport(fileId, deviceType);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return result;
    }

    private String saleImport(Long fileId, String deviceType) throws IOException {
        //上传附件
        SysFileGetVo sysFileGetVo = new SysFileGetVo();
        try {
            sysFileGetVo = sysFileProvider.get(fileId);
            if (sysFileGetVo == null) {
                throw new ServiceException("附件上传异常,请重新上传");
            }
        } catch (Exception e) {
            throw new ServiceException("附件上传异常,请重新上传");
        }
        InputStream inputstream = fileService.getObject(sysFileGetVo.getBuckets(), sysFileGetVo.getFilePath());
        List<IotDeviceExeclAddItem> addBatchList = EasyExcel.read(inputstream).head(IotDeviceExeclAddItem.class).sheet(0).headRowNumber(5).doReadSync();
        String failReason = "导入成功";
        List<String> reasonList = new ArrayList<>();
        if (addBatchList != null && addBatchList.size() != 0) {
            OptUser optUser = ApiUtils.getUser(OptUser.class);
            //导入失败数
            int failCount = 0;
            //新增导入记录
            IotDeviceImportAddDto iotDeviceImportAddDto = new IotDeviceImportAddDto();
            //设置创建人信息
            iotDeviceImportAddDto.setCreateBy(optUser.getUserId());
            iotDeviceImportAddDto.setCreateUser(optUser.getUserName());
            iotDeviceImportAddDto.setCreateUserName(optUser.getRealName());
            iotDeviceImportAddDto.setTenantId(optUser.getTenantId());
            iotDeviceImportAddDto.setTotalCount(addBatchList.size());
            iotDeviceImportAddDto.setDeviceType(deviceType);
            iotDeviceImportAddDto.setFileId(fileId);
            iotDeviceImportAddDto.setFileName(sysFileGetVo.getFileName());
            IotDeviceImportAddVo iotDeviceImportAddVo = iotDeviceImportProvider.add(iotDeviceImportAddDto);

            for (int i = 0; i < addBatchList.size(); i++) {
                IotDeviceExeclAddItem addReq = addBatchList.get(i);
                //新增导入明细
                IotDeviceImportDetailAddDto detailAddDto = new IotDeviceImportDetailAddDto();
                //设置创建人信息
                detailAddDto.setCreateBy(optUser.getUserId());
                detailAddDto.setCreateUser(optUser.getUserName());
                detailAddDto.setCreateUserName(optUser.getRealName());
                detailAddDto.setImportId(iotDeviceImportAddVo.getId());
                detailAddDto.setDeviceCode(addReq.getDeviceCode());
                detailAddDto.setDeviceId(addReq.getDeviceId());
                detailAddDto.setDeviceType(deviceType);
                //导入成功标识
                int flag = 1;
                IotDeviceAddDto addDto = new IotDeviceAddDto();
                if (addReq.getDeviceName() == null) {
                    flag = 0;
                    failCount++;
                    failReason = "设备名称不能为空";
                    reasonList.add("第" + (i + 1) + "行" + failReason);
                    detailAddDto.setLine(i + 1);
                    detailAddDto.setStatus(2);
                    detailAddDto.setFailReason(failReason);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    continue;
                }

                if (addReq.getDeviceCode() != null) {
                    //客流设备编码唯一判断
                    IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
                    iotDeviceListDto.setDeviceType(deviceType);
                    iotDeviceListDto.setDeviceCode(addReq.getDeviceCode());
                    int count = iotDeviceProvider.count(iotDeviceListDto);
                    if (count != 0) {
                        flag = 0;
                        failCount++;
                        failReason = "该设备编码在其它项目已存在";
                        reasonList.add("第" + (i + 1) + "行" + failReason);
                        detailAddDto.setLine(i + 1);
                        detailAddDto.setStatus(2);
                        detailAddDto.setFailReason(failReason);
                        iotDeviceImportDetailProvider.add(detailAddDto);
                        continue;
                    }
                } else {
                    flag = 0;
                    failCount++;
                    failReason = "设备编码不能为空";
                    reasonList.add("第" + (i + 1) + "行" + failReason);
                    detailAddDto.setLine(i + 1);
                    detailAddDto.setStatus(2);
                    detailAddDto.setFailReason(failReason);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    continue;
                }

                if (addReq.getDeviceId() != null) {
                    IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
                    iotDeviceListDto.setDeviceType(deviceType);
                    iotDeviceListDto.setDeviceId(addReq.getDeviceCode());
                    int count = iotDeviceProvider.count(iotDeviceListDto);
                    if (count != 0) {
                        flag = 0;
                        failCount++;
                        failReason = "该设备ID在其它项目已存在";
                        reasonList.add("第" + (i + 1) + "行" + failReason);
                        detailAddDto.setLine(i + 1);
                        detailAddDto.setStatus(2);
                        detailAddDto.setFailReason(failReason);
                        iotDeviceImportDetailProvider.add(detailAddDto);
                        continue;
                    }
                } else {
                    flag = 0;
                    failCount++;
                    failReason = "设备ID不能为空";
                    reasonList.add("第" + (i + 1) + "行" + failReason);
                    detailAddDto.setLine(i + 1);
                    detailAddDto.setStatus(2);
                    detailAddDto.setFailReason(failReason);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    continue;
                }

                if (addReq.getDeviceModel() != null) {


                } else {
                    flag = 0;
                    failCount++;
                    failReason = "设备型号不能为空";
                    reasonList.add("第" + (i + 1) + "行" + failReason);
                    detailAddDto.setLine(i + 1);
                    detailAddDto.setStatus(2);
                    detailAddDto.setFailReason(failReason);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    continue;
                }

                //如果信息正确
                if (flag == 1) {
                    detailAddDto.setStatus(1);
                    detailAddDto.setLine(i + 1);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    // TODO 插入设备信息
                    IotDeviceAddDto dto = BeanUtils.copyProperties(addReq, IotDeviceAddDto.class);
                    //设置创建人信息
                    dto.setCreateBy(optUser.getUserId());
                    dto.setCreateUser(optUser.getUserName());
                    dto.setCreateUserName(optUser.getRealName());
                    dto.setEntId(optUser.getEntId());
                    dto.setOrgFid(optUser.getOrgFid());
                    dto.setOrgFname(optUser.getOrgFname());
                    dto.setTenantName(optUser.getTenantName());
                    dto.setTenantId(optUser.getTenantId());
                    dto.setDeviceType(deviceType);
                    dto.setDeviceStatus(1);
                    dto.setDeviceEnable(0);
                    dto.setBindStatus(0);
                    IotDeviceAddVo add = iotDeviceProvider.add(dto);
                }
            }

            //编辑导入记录
            IotDeviceImportGetVo roomsImportGetVo = iotDeviceImportProvider.get(iotDeviceImportAddVo.getId());
            IotDeviceImportEditDto roomsImportEditDto = BeanUtils.copyProperties(roomsImportGetVo, IotDeviceImportEditDto.class);
            roomsImportEditDto.setStatus(1);
            roomsImportEditDto.setFileName(sysFileGetVo.getFileName());
            roomsImportEditDto.setFileId(sysFileGetVo.getId());
            roomsImportEditDto.setFailCount(failCount);
            String reason = "";
            if (reasonList.size() != 0) {
                int a = 5;
                if (reasonList.size() < 5) a = reasonList.size();
                for (int i = 0; i < a; i++) {
                    reason = reason + reasonList.get(i) + "; ";
                }
                reason = reason + "等";
            }
            roomsImportEditDto.setFailReason(reason);
            roomsImportEditDto.setUpdateBy(optUser.getUserId());
            roomsImportEditDto.setUpdateUser(optUser.getUserName());
            roomsImportEditDto.setUpdateUserName(optUser.getRealName());
            iotDeviceImportProvider.edit(roomsImportEditDto);
        }
        return failReason;
    }

    private String flowImport(Long fileId, String deviceType) throws IOException {

        //上传附件
        SysFileGetVo sysFileGetVo = new SysFileGetVo();

        try {
            sysFileGetVo = sysFileProvider.get(fileId);

            if (sysFileGetVo == null) {
                throw new ServiceException("附件上传异常,请重新上传");
            }
        } catch (Exception e) {
            throw new ServiceException("附件上传异常,请重新上传");
        }
        InputStream inputstream = fileService.getObject(sysFileGetVo.getBuckets(), sysFileGetVo.getFilePath());
        List<IotDeviceExeclAddItem> addBatchList = EasyExcel.read(inputstream).head(IotDeviceExeclAddItem.class).sheet(0).headRowNumber(5).doReadSync();

        String failReason = "导入成功";
        List<String> reasonList = new ArrayList<>();
        if (addBatchList != null && addBatchList.size() != 0) {
            OptUser optUser = ApiUtils.getUser(OptUser.class);
            //导入失败数
            int failCount = 0;
            //新增导入记录
            IotDeviceImportAddDto iotDeviceImportAddDto = new IotDeviceImportAddDto();
            //设置创建人信息
            iotDeviceImportAddDto.setCreateBy(optUser.getUserId());
            iotDeviceImportAddDto.setCreateUser(optUser.getUserName());
            iotDeviceImportAddDto.setCreateUserName(optUser.getRealName());
            iotDeviceImportAddDto.setTenantId(optUser.getTenantId());
            iotDeviceImportAddDto.setTotalCount(addBatchList.size());
            iotDeviceImportAddDto.setDeviceType(deviceType);
            iotDeviceImportAddDto.setFileId(fileId);
            iotDeviceImportAddDto.setFileName(sysFileGetVo.getFileName());
            IotDeviceImportAddVo iotDeviceImportAddVo = iotDeviceImportProvider.add(iotDeviceImportAddDto);

            for (int i = 0; i < addBatchList.size(); i++) {
                IotDeviceExeclAddItem addReq = addBatchList.get(i);
                //新增导入明细
                IotDeviceImportDetailAddDto detailAddDto = new IotDeviceImportDetailAddDto();
                //设置创建人信息
                detailAddDto.setCreateBy(optUser.getUserId());
                detailAddDto.setCreateUser(optUser.getUserName());
                detailAddDto.setCreateUserName(optUser.getRealName());
                detailAddDto.setImportId(iotDeviceImportAddVo.getId());
                detailAddDto.setDeviceCode(addReq.getDeviceCode());
                detailAddDto.setDeviceId(addReq.getDeviceId());
                detailAddDto.setDeviceType(deviceType);
                //导入成功标识
                int flag = 1;
                IotDeviceAddDto addDto = new IotDeviceAddDto();
                if (addReq.getDeviceName() == null) {
                    flag = 0;
                    failCount++;
                    failReason = "设备名称不能为空";
                    reasonList.add("第" + (i + 1) + "行" + failReason);
                    detailAddDto.setLine(i + 1);
                    detailAddDto.setStatus(2);
                    detailAddDto.setFailReason(failReason);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    continue;
                }

                if (addReq.getDeviceCode() != null) {
                    //客流设备编码唯一判断
                    IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
                    iotDeviceListDto.setDeviceType(deviceType);
                    iotDeviceListDto.setDeviceCode(addReq.getDeviceCode());
                    int count = iotDeviceProvider.count(iotDeviceListDto);
                    if (count != 0) {
                        flag = 0;
                        failCount++;
                        failReason = "该设备编码在其它项目已存在";
                        reasonList.add("第" + (i + 1) + "行" + failReason);
                        detailAddDto.setLine(i + 1);
                        detailAddDto.setStatus(2);
                        detailAddDto.setFailReason(failReason);
                        iotDeviceImportDetailProvider.add(detailAddDto);
                        continue;
                    }
                } else {
                    flag = 0;
                    failCount++;
                    failReason = "设备编码不能为空";
                    reasonList.add("第" + (i + 1) + "行" + failReason);
                    detailAddDto.setLine(i + 1);
                    detailAddDto.setStatus(2);
                    detailAddDto.setFailReason(failReason);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    continue;
                }

                if (addReq.getDeviceId() != null) {
                    IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
                    iotDeviceListDto.setDeviceType(deviceType);
                    iotDeviceListDto.setDeviceId(addReq.getDeviceCode());
                    int count = iotDeviceProvider.count(iotDeviceListDto);
                    if (count != 0) {
                        flag = 0;
                        failCount++;
                        failReason = "该设备ID在其它项目已存在";
                        reasonList.add("第" + (i + 1) + "行" + failReason);
                        detailAddDto.setLine(i + 1);
                        detailAddDto.setStatus(2);
                        detailAddDto.setFailReason(failReason);
                        iotDeviceImportDetailProvider.add(detailAddDto);
                        continue;
                    }
                } else {
                    flag = 0;
                    failCount++;
                    failReason = "设备ID不能为空";
                    reasonList.add("第" + (i + 1) + "行" + failReason);
                    detailAddDto.setLine(i + 1);
                    detailAddDto.setStatus(2);
                    detailAddDto.setFailReason(failReason);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    continue;
                }

                if (addReq.getDeviceModel() != null) {


                } else {
                    flag = 0;
                    failCount++;
                    failReason = "设备型号不能为空";
                    reasonList.add("第" + (i + 1) + "行" + failReason);
                    detailAddDto.setLine(i + 1);
                    detailAddDto.setStatus(2);
                    detailAddDto.setFailReason(failReason);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    continue;
                }

                //如果信息正确
                if (flag == 1) {
                    detailAddDto.setStatus(1);
                    detailAddDto.setLine(i + 1);
                    iotDeviceImportDetailProvider.add(detailAddDto);
                    // TODO 插入设备信息
                    IotDeviceAddDto dto = BeanUtils.copyProperties(addReq, IotDeviceAddDto.class);
                    //设置创建人信息
                    dto.setCreateBy(optUser.getUserId());
                    dto.setCreateUser(optUser.getUserName());
                    dto.setCreateUserName(optUser.getRealName());
                    dto.setEntId(optUser.getEntId());
                    dto.setOrgFid(optUser.getOrgFid());
                    dto.setOrgFname(optUser.getOrgFname());
                    dto.setTenantName(optUser.getTenantName());
                    dto.setTenantId(optUser.getTenantId());
                    dto.setDeviceType(deviceType);
                    dto.setDeviceStatus(1);
                    dto.setDeviceEnable(0);
                    dto.setBindStatus(0);
                    IotDeviceAddVo add = iotDeviceProvider.add(dto);
                }
            }

            //编辑导入记录
            IotDeviceImportGetVo roomsImportGetVo = iotDeviceImportProvider.get(iotDeviceImportAddVo.getId());
            IotDeviceImportEditDto roomsImportEditDto = BeanUtils.copyProperties(roomsImportGetVo, IotDeviceImportEditDto.class);
            roomsImportEditDto.setStatus(1);
            roomsImportEditDto.setFileName(sysFileGetVo.getFileName());
            roomsImportEditDto.setFileId(sysFileGetVo.getId());
            roomsImportEditDto.setFailCount(failCount);
            String reason = "";
            if (reasonList.size() != 0) {
                int a = 5;
                if (reasonList.size() < 5) a = reasonList.size();
                for (int i = 0; i < a; i++) {
                    reason = reason + reasonList.get(i) + "; ";
                }
                reason = reason + "等";
            }
            roomsImportEditDto.setFailReason(reason);
            roomsImportEditDto.setUpdateBy(optUser.getUserId());
            roomsImportEditDto.setUpdateUser(optUser.getUserName());
            roomsImportEditDto.setUpdateUserName(optUser.getRealName());
            iotDeviceImportProvider.edit(roomsImportEditDto);
        }
        return failReason;
    }


    /**
     * 下载导入模板
     */
    @Override
    public void download(String deviceType) {
        if ("flow".equals(deviceType)) {
            downloadFlowImportTemplate();
        } else {
            downloadSaleImportTemplate();
        }
    }

    private void downloadSaleImportTemplate() {
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            String[] head = {"*设备名称", "*设备编码", "*设备id", "*设备型号"};
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet();
            sheet.setDefaultColumnWidth((short) 18);
            Row row = sheet.createRow(4);
            row.setHeightInPoints(40);

            //设置字体样式
            CellStyle cellStyle = workbook.createCellStyle();
            XSSFFont font = workbook.createFont();
            font.setColor(Font.COLOR_RED);
            font.setBold(true);
            font.setFontHeightInPoints((short) 15);  // 设置字体大小
            cellStyle.setFont(font);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            for (int i = 0; i < head.length; i++) {
                Cell cell = row.createCell(i);
                cell.setCellType(CellType.STRING);
                cell.setCellValue(head[i]);
                if (head[i].startsWith("*")) {
                    cell.setCellStyle(cellStyle);
                }
            }

            //示例数据
            List<String> listData2 = new ArrayList<>();
            listData2.add("销售采集设备");
            listData2.add("XS001");
            listData2.add("12221S9CVNMT");
            listData2.add("XS-TP01-122");
            Row row2 = sheet.createRow(5);
            for (int x = 0; x < listData2.size(); x++) {
                Cell cell = row2.createCell(x);
                cell.setCellValue(listData2.get(x));
            }


            //规则说明
            //   参数： firstRow 起始行  lastRow 结束行  firstCol 起始列  lastCol 结束列
            CellRangeAddress region1 = new CellRangeAddress(0, 3, 0, 3);
            sheet.addMergedRegion(region1);
            String string = "说明：" + String.valueOf((char) 10) + "1、标注*的为必填字段；" + String.valueOf((char) 10) + "2、设备编码采用XS+001/002/003顺序编码规则编写，设备id和设备编码一一对应";
            Row promptRow = sheet.createRow(0);
            Cell cell = promptRow.createCell(0);
            CellStyle promptRowStyle = workbook.createCellStyle();
            XSSFFont promptRowFont = workbook.createFont();
            promptRowFont.setColor(Font.COLOR_NORMAL);
            promptRowFont.setFontHeightInPoints((short) 13);
            promptRowStyle.setFont(promptRowFont);
            promptRowStyle.setWrapText(true);
            cell.setCellValue(string);
            cell.setCellStyle(promptRowStyle);

            response.setContentType("application/octet-stream");
            String fileName = URLEncoder.encode("客流采集设备批量导入模板.xlsx", StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            response.flushBuffer();
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
            log.info("捕获到的导出异常{}", JSON.toJSONString(e.getMessage()));
        }
    }

    private void downloadFlowImportTemplate() {
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            String[] head = {"*设备名称", "*设备编码", "*设备id", "*设备型号"};
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet();
            sheet.setDefaultColumnWidth((short) 18);
            Row row = sheet.createRow(4);
            row.setHeightInPoints(40);

            //设置字体样式
            CellStyle cellStyle = workbook.createCellStyle();
            XSSFFont font = workbook.createFont();
            font.setColor(Font.COLOR_RED);
            font.setBold(true);
            font.setFontHeightInPoints((short) 15);  // 设置字体大小
            cellStyle.setFont(font);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            for (int i = 0; i < head.length; i++) {
                Cell cell = row.createCell(i);
                cell.setCellType(CellType.STRING);
                cell.setCellValue(head[i]);
                if (head[i].startsWith("*")) {
                    cell.setCellStyle(cellStyle);
                }
            }

            //示例数据
            List<String> listData2 = new ArrayList<>();
            listData2.add("客流采集设备");
            listData2.add("KL001");
            listData2.add("12221S9CVNMT");
            listData2.add("KL-TP01-122");
            Row row2 = sheet.createRow(5);
            for (int x = 0; x < listData2.size(); x++) {
                Cell cell = row2.createCell(x);
                cell.setCellValue(listData2.get(x));
            }


            //规则说明
            //   参数： firstRow 起始行  lastRow 结束行  firstCol 起始列  lastCol 结束列
            CellRangeAddress region1 = new CellRangeAddress(0, 3, 0, 3);
            sheet.addMergedRegion(region1);
            String string = "说明：" + String.valueOf((char) 10) + "1、标注*的为必填字段；" + String.valueOf((char) 10) + "2、设备编码采用KL+001/002/003顺序编码规则编写，设备id和设备编码一一对应";
            Row promptRow = sheet.createRow(0);
            Cell cell = promptRow.createCell(0);
            CellStyle promptRowStyle = workbook.createCellStyle();
            XSSFFont promptRowFont = workbook.createFont();
            promptRowFont.setColor(Font.COLOR_NORMAL);
            promptRowFont.setFontHeightInPoints((short) 13);
            promptRowStyle.setFont(promptRowFont);
            promptRowStyle.setWrapText(true);
            cell.setCellValue(string);
            cell.setCellStyle(promptRowStyle);

            response.setContentType("application/octet-stream");
            String fileName = URLEncoder.encode("客流采集设备批量导入模板.xlsx", StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            response.flushBuffer();
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
            log.info("捕获到的导出异常{}", JSON.toJSONString(e.getMessage()));
        }
    }
}
