package com.seewin.som.rent.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.seewin.som.commerce.provider.CommerceAdvertContractProvider;
import com.seewin.som.commerce.provider.CommerceContractProvider;
import com.seewin.som.commerce.req.CommerceAdvertContractListDto;
import com.seewin.som.commerce.req.CommerceContractInfoListDto;
import com.seewin.som.commerce.req.CommerceContractListDto;
import com.seewin.som.commerce.resp.*;
import com.seewin.som.rent.enums.StandardTypeEnum;
import com.seewin.som.rent.provider.*;
import com.seewin.som.rent.req.*;
import com.seewin.som.rent.resp.*;
import com.seewin.som.rent.service.RentChargingStandardStoreService;
import com.seewin.som.rent.vo.req.*;
import com.seewin.som.rent.vo.resp.*;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.storage.provider.SysFileProvider;
import com.seewin.som.storage.resp.SysFileGetVo;
import com.seewin.system.service.FileService;
import org.springframework.util.CollectionUtils;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsListVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.commerce.provider.CommerceContractInfoProvider;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 收费标准关联门店表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Slf4j
@Service
public class RentChargingStandardStoreServiceImpl implements RentChargingStandardStoreService {

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingStandardStoreProvider rentChargingStandardStoreProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingStandardProvider rentChargingStandardProvider;

    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomsProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractProvider commerceContractProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeImportProvider rentFeeImportProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeImportDetailProvider rentFeeImportDetailProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractInfoProvider commerceContractInfoProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceAdvertContractProvider commerceAdvertContractProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingItemProvider rentChargingItemProvider;

    @DubboReference(providedBy = "som-storage-mgt")
    private SysFileProvider sysFileProvider;

    @Autowired
    private FileService fileService;

    /**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<RentChargingStandardStoreListItem> page(RentChargingStandardStoreListReq listReq) {
        PageResp<RentChargingStandardStoreListItem> pageResp = new PageResp<>();
        User curUser = ApiUtils.getUser(User.class);

        RentChargingStandardStoreListDto queryDto = BeanUtils.copyProperties(listReq, RentChargingStandardStoreListDto.class);
        queryDto.setTenantId(curUser.getTenantId());


        PageQuery<RentChargingStandardStoreListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<RentChargingStandardStoreListVo> pageResult = rentChargingStandardStoreProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), RentChargingStandardStoreListItem.class));

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public RentChargingStandardStoreGetResp get(RentChargingStandardStoreGetReq getReq) {
        RentChargingStandardStoreGetVo getVo = rentChargingStandardStoreProvider.get(getReq.getId());

        RentChargingStandardStoreGetResp getResp = BeanUtils.copyProperties(getVo, RentChargingStandardStoreGetResp.class);

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public RentChargingStandardStoreAddResp add(RentChargingStandardStoreAddReq addReq) {
        RentChargingStandardStoreAddDto dto = BeanUtils.copyProperties(addReq, RentChargingStandardStoreAddDto.class);

        //设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());

        dto.setTenantId(curUser.getTenantId());

        RentChargingStandardStoreAddVo addVo = rentChargingStandardStoreProvider.add(dto);

        RentChargingStandardStoreAddResp addResp = BeanUtils.copyProperties(addVo, RentChargingStandardStoreAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(RentChargingStandardStoreEditReq editReq) {
        RentChargingStandardStoreEditDto dto = BeanUtils.copyProperties(editReq, RentChargingStandardStoreEditDto.class);

        //设置修改人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        rentChargingStandardStoreProvider.edit(dto);
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(RentChargingStandardStoreDelReq delReq) {
        rentChargingStandardStoreProvider.delete(delReq.getId());
    }

    @Override
    public boolean bind(RentChargingStandardStoreBindReq bindReq) {
        //RentChargingStandardStoreBindDto dto = BeanUtils.copyProperties(bindReq, RentChargingStandardStoreBindDto.class);
        //dto.setStoreIdList(bindReq.getStoreIdList());

        //设置创建人信息
        User optUser = ApiUtils.getUser(User.class);
        // 要删除的标准关联表的id
        List<Long> removePrincipalIdList = new ArrayList<>();

        Set<Long> principalStandardIdSet = new HashSet<>();
        principalStandardIdSet.add(bindReq.getPrincipalChargingStandardId());
        Set<Long> latePaymentStandardIdSet = new HashSet<>();
        latePaymentStandardIdSet.add(bindReq.getLatePaymentChargingStandardId());

        // 如果原来的合同在同一个收费项目下绑定了其他收费标准，也需要删除
        RentChargingStandardGetVo principalChargingStandard = rentChargingStandardProvider.get(bindReq.getPrincipalChargingStandardId());
        if (principalChargingStandard != null) {
            RentChargingStandardListDto principalChargingStandardListDto = new RentChargingStandardListDto();
            // 找出同一个项目下的所有标准id
            principalChargingStandardListDto.setChargingItemId(principalChargingStandard.getChargingItemId());
            List<RentChargingStandardListVo> principalChargingStandardList = rentChargingStandardProvider.list(principalChargingStandardListDto);
            if (CollectionUtil.isNotEmpty(principalChargingStandardList)) {
                for (RentChargingStandardListVo vo : principalChargingStandardList) {
                    principalStandardIdSet.add(vo.getId());
                }
            }

        }
        RentChargingStandardGetVo latePaymentChargingStandard = rentChargingStandardProvider.get(bindReq.getLatePaymentChargingStandardId());
        if (latePaymentChargingStandard != null) {
            RentChargingStandardListDto latePaymentChargingStandardListDto = new RentChargingStandardListDto();
            // 找出同一个项目下的所有标准id
            latePaymentChargingStandardListDto.setChargingItemId(latePaymentChargingStandard.getChargingItemId());
            List<RentChargingStandardListVo> latePaymentChargingStandardList = rentChargingStandardProvider.list(latePaymentChargingStandardListDto);
            if (CollectionUtil.isNotEmpty(latePaymentChargingStandardList)) {
                for (RentChargingStandardListVo vo : latePaymentChargingStandardList) {
                    latePaymentStandardIdSet.add(vo.getId());
                }
            }
        }

        RentChargingStandardStoreListDto standardStoreListDto = new RentChargingStandardStoreListDto();
        standardStoreListDto.setChargingStandardIdSet(principalStandardIdSet);
        // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
        standardStoreListDto.setCommonContractType(bindReq.getCommonContractType());
        // 必须指定tenantId 如果仅仅根据标准ID查询会重复
        standardStoreListDto.setTenantId(optUser.getTenantId());

        // 根据本金标准id，查询老的绑定关系
        List<RentChargingStandardStoreListVo> oldList = rentChargingStandardStoreProvider.list(standardStoreListDto);

        // 如果传的合同id列表为空 则删除原有的
        if (CollectionUtil.isEmpty(bindReq.getContractCodeList())) {
            // 删除标准id关联的所有的合同
            RentChargingStandardStoreListDto listDto = new RentChargingStandardStoreListDto();
            listDto.setChargingStandardId(bindReq.getPrincipalChargingStandardId());
            // 必须指定tenantId 如果仅仅根据门店ID查询会重复
            listDto.setTenantId(optUser.getTenantId());
            // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
            listDto.setCommonContractType(bindReq.getCommonContractType());
            // 根据本金标准id，查询老的绑定关系
            List<RentChargingStandardStoreListVo> oldPrincipalList = rentChargingStandardStoreProvider.list(listDto);
            if (CollectionUtil.isNotEmpty(oldPrincipalList)) {
                for (RentChargingStandardStoreListVo old : oldPrincipalList) {
                    removePrincipalIdList.add(old.getId());
                }
            }
            // 批量删除本金绑定的
            rentChargingStandardStoreProvider.removeBatchByIds(removePrincipalIdList);
        }

        // 如果传的合同列表不为空 先删后增
        if (CollectionUtil.isNotEmpty(bindReq.getContractCodeList())) {
            // 从合同维度，一个合同只能应用一个标准  应用的集合
            Set<String> contractCodeSet = new HashSet<>(bindReq.getContractCodeList());
            if (CollectionUtil.isNotEmpty(oldList)) {
                for (RentChargingStandardStoreListVo old : oldList) {
                    if (contractCodeSet.contains(old.getContractCode())) {
                        removePrincipalIdList.add(old.getId());
                    }
                }
            }

            List<RentChargingStandardStoreAddDto> principalStandardStoreAddDtoList = new ArrayList<>();
            List<RentChargingStandardStoreAddDto> latePaymentStandardStoreAddDtoList = new ArrayList<>();


            // 批量查询合同主表
            CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
            commerceContractListDto.setContractCodeList(bindReq.getContractCodeList());
            // 必须指定tenantId 如果仅仅根据门店ID查询会重复
            commerceContractListDto.setTenantId(optUser.getTenantId());
            // 从合同主表查询
            Map<String, CommerceContractListVo> contractCodeVoMap = new HashMap<>();

            // 批量查询合同信息
            CommerceContractInfoListDto commerceContractInfoListDto = new CommerceContractInfoListDto();
            commerceContractInfoListDto.setContractCodeList(bindReq.getContractCodeList());
            commerceContractInfoListDto.setTenantId(optUser.getTenantId());
            // 从合同信息查询
            Map<String, CommerceContractInfoListVo> contractCodeInfoVoMap = new HashMap<>();

            // 批量查询多经广告合同
            CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
            commerceAdvertContractListDto.setContractCodeList(bindReq.getContractCodeList());
            commerceAdvertContractListDto.setTenantId(optUser.getTenantId());
            // 从多经广告合同查询
            Map<String, CommerceAdvertContractListVo> advertContractListVoMap = new HashMap<>();

            // 如果是正铺/正铺临促合同
            if (bindReq.getCommonContractType() == 100) {

                List<CommerceContractListVo> contractVoList = commerceContractProvider.list(commerceContractListDto);
                List<CommerceContractInfoListVo> contracInfotVoList = commerceContractInfoProvider.list(commerceContractInfoListDto);

                if (CollectionUtil.isNotEmpty(contractVoList)) {
                    for (CommerceContractListVo contractVo : contractVoList) {
                        contractCodeVoMap.put(contractVo.getContractCode(), contractVo);
                    }
                }

                if (CollectionUtil.isNotEmpty(contracInfotVoList)) {
                    for (CommerceContractInfoListVo contractInfoVo : contracInfotVoList) {
                        contractCodeInfoVoMap.put(contractInfoVo.getContractCode(), contractInfoVo);
                    }
                }
            }

            // 如果是多经/广告位合同
            if (bindReq.getCommonContractType() == 200) {
                List<CommerceAdvertContractListVo> commerceAdvertContractVoList = commerceAdvertContractProvider.list(commerceAdvertContractListDto);
                if (CollectionUtil.isNotEmpty(commerceAdvertContractVoList)) {
                    for (CommerceAdvertContractListVo advertContractListVo : commerceAdvertContractVoList) {
                        advertContractListVoMap.put(advertContractListVo.getContractCode(), advertContractListVo);
                    }
                }
            }

            for (String contractCode : bindReq.getContractCodeList()) {
                LocalDateTime nowTime = LocalDateTime.now();
                // 通过门店id获取门店信息
                CommerceContractListVo contractVo = contractCodeVoMap.get(contractCode);

                CommerceContractInfoListVo contractInfoVo = contractCodeInfoVoMap.get(contractCode);

                CommerceAdvertContractListVo advertContractListVo = advertContractListVoMap.get(contractCode);

                // 本金标准绑定门店处理 （需要注意一个门店多个合同的情况，根据合同截止时间判断绑定哪个合同）
                RentChargingStandardStoreAddDto principal = new RentChargingStandardStoreAddDto();

                principal.setChargingStandardId(bindReq.getPrincipalChargingStandardId());
                principal.setCommonContractType(bindReq.getCommonContractType());
                if (contractVo != null) {
                    principal.setStoreId(contractVo.getRoomShopId());
                    principal.setEntId(contractVo.getEntId());
                    principal.setFid(null);
                    principal.setFcode(contractVo.getFcode());
                    principal.setFname(contractVo.getFname());
                    principal.setOrgFid(contractVo.getOrgFid());
                    principal.setOrgFname(contractVo.getOrgFname());
                    principal.setRoomId(contractVo.getId());
                    principal.setRoomName(contractVo.getName());
                    principal.setTenantId(contractVo.getTenantId());
                    principal.setTenantName(contractVo.getTenantName());
                    principal.setContractCode(contractVo.getContractCode());
                }
                if (contractInfoVo != null) {
                    principal.setShopType(contractInfoVo.getShopType());
                }
                if (advertContractListVo != null) {
                    principal.setAdvertContractType(advertContractListVo.getAdvertContractType());
                    principal.setAdvertName(advertContractListVo.getAdvertName());

                    principal.setEntId(advertContractListVo.getEntId());
                    principal.setOrgFid(advertContractListVo.getOrgFid());
                    principal.setOrgFname(advertContractListVo.getOrgFname());
                    principal.setTenantId(advertContractListVo.getTenantId());
                    principal.setTenantName(advertContractListVo.getTenantName());
                    principal.setContractCode(advertContractListVo.getContractCode());
                }
                principal.setApplyPaymentTermSrart(bindReq.getApplyPaymentTermSrart());
                principal.setApplyPaymentTermEnd(bindReq.getApplyPaymentTermEnd());

                principal.setCreateTime(nowTime);
                principal.setCreateBy(optUser.getUserId());
                principal.setCreateUser(optUser.getUserName());
                principal.setCreateUserName(optUser.getRealName());

                principalStandardStoreAddDtoList.add(principal);
                //standardStoreAddDtoList.add(latePayment);

            }
            // 合同维度 本金批量删除 先删后增
            rentChargingStandardStoreProvider.removeBatchByIds(removePrincipalIdList);
            // 合同维度 本金批量保存
            rentChargingStandardStoreProvider.saveBatch(principalStandardStoreAddDtoList);

            // 滞纳金  合同维度 对滞纳金先删后增
            RentChargingStandardStoreListDto standardStoreListDto2 = new RentChargingStandardStoreListDto();
            standardStoreListDto2.setContractCodeList(bindReq.getContractCodeList());
            // 必须指定tenantId 如果仅仅根据门店ID查询会重复
            standardStoreListDto2.setTenantId(optUser.getTenantId());
            // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
            standardStoreListDto2.setCommonContractType(bindReq.getCommonContractType());

            // 根据本金标准id，查询老的绑定关系
            List<RentChargingStandardStoreListVo> oldBindContractViewList = rentChargingStandardStoreProvider.list(standardStoreListDto2);
            List<Long> standardIdList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(oldBindContractViewList)) {
                for (RentChargingStandardStoreListVo oldBindContractView : oldBindContractViewList) {
                    standardIdList.add(oldBindContractView.getChargingStandardId());
                }
            }
            Map<Long, RentChargingStandardListVo> standardIdVoMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(standardIdList)) {
                RentChargingStandardListDto standardListDto = new RentChargingStandardListDto();
                standardListDto.setIdList(standardIdList);
                List<RentChargingStandardListVo> standardList = rentChargingStandardProvider.list(standardListDto);
                if (CollectionUtil.isNotEmpty(standardList)) {
                    for (RentChargingStandardListVo standard : standardList) {
                        standardIdVoMap.put(standard.getId(), standard);
                    }
                }
            }
            // 要删除的合同视角的绑定的滞纳金的id
            List<Long> removeLatePaymentIdList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(oldBindContractViewList)) {
                for (RentChargingStandardStoreListVo oldBindContractView : oldBindContractViewList) {
                    RentChargingStandardListVo standard = standardIdVoMap.get(oldBindContractView.getChargingStandardId());
                    if (standard != null && StandardTypeEnum.LatePayment.getCode().equals(standard.getType())) {
                        removeLatePaymentIdList.add(oldBindContractView.getId());
                    }
                }
                // 滞纳金批量删除 先删后增
                rentChargingStandardStoreProvider.removeBatchByIds(removeLatePaymentIdList);
            }
            for (String contractCode : bindReq.getContractCodeList()) {
                LocalDateTime nowTime = LocalDateTime.now();
                // 通过门店id获取门店信息
                CommerceContractListVo contractVo = contractCodeVoMap.get(contractCode);

                CommerceContractInfoListVo contractInfoVo = contractCodeInfoVoMap.get(contractCode);

                CommerceAdvertContractListVo advertContractListVo = advertContractListVoMap.get(contractCode);

                // 本金标准绑定门店处理 （需要注意一个门店多个合同的情况，根据合同截止时间判断绑定哪个合同）
                RentChargingStandardStoreAddDto latePayment = new RentChargingStandardStoreAddDto();

                latePayment.setChargingStandardId(bindReq.getLatePaymentChargingStandardId());
                latePayment.setCommonContractType(bindReq.getCommonContractType());
                if (contractVo != null) {
                    latePayment.setStoreId(contractVo.getRoomShopId());
                    latePayment.setEntId(contractVo.getEntId());
                    latePayment.setFid(null);
                    latePayment.setFcode(contractVo.getFcode());
                    latePayment.setFname(contractVo.getFname());
                    latePayment.setOrgFid(contractVo.getOrgFid());
                    latePayment.setOrgFname(contractVo.getOrgFname());
                    latePayment.setRoomId(contractVo.getId());
                    latePayment.setRoomName(contractVo.getName());
                    latePayment.setTenantId(contractVo.getTenantId());
                    latePayment.setTenantName(contractVo.getTenantName());
                    latePayment.setContractCode(contractVo.getContractCode());
                }
                if (contractInfoVo != null) {
                    latePayment.setShopType(contractInfoVo.getShopType());
                }
                if (advertContractListVo != null) {
                    latePayment.setAdvertContractType(advertContractListVo.getAdvertContractType());
                    latePayment.setAdvertName(advertContractListVo.getAdvertName());

                    latePayment.setEntId(advertContractListVo.getEntId());
                    latePayment.setOrgFid(advertContractListVo.getOrgFid());
                    latePayment.setOrgFname(advertContractListVo.getOrgFname());
                    latePayment.setTenantId(advertContractListVo.getTenantId());
                    latePayment.setTenantName(advertContractListVo.getTenantName());
                    latePayment.setContractCode(advertContractListVo.getContractCode());
                }
                latePayment.setApplyPaymentTermSrart(bindReq.getApplyPaymentTermSrart());
                latePayment.setApplyPaymentTermEnd(bindReq.getApplyPaymentTermEnd());

                latePayment.setCreateTime(nowTime);
                latePayment.setCreateBy(optUser.getUserId());
                latePayment.setCreateUser(optUser.getUserName());
                latePayment.setCreateUserName(optUser.getRealName());

                latePaymentStandardStoreAddDtoList.add(latePayment);
            }
            // 滞纳金批量保存
            rentChargingStandardStoreProvider.saveBatch(latePaymentStandardStoreAddDtoList);
        }
        return true;

    }

    @Override
    public PageResp<RentChargingStandardStoreRoomViewPageResp> roomViewPage(RentChargingStandardStoreRoomViewPageReq roomViewPageReq) {
        PageResp<RentChargingStandardStoreRoomViewPageResp> pageResp = new PageResp<>();
        User curUser = ApiUtils.getUser(User.class);

        // 搜索条件得到的合同号
        List<List<String>> inputLists = new ArrayList<>();
        Map<Long, RentChargingItemListVo> itemIdItemMap = new HashMap<>();
        Map<Long, RentChargingStandardListVo> standardIdIStandardMap = new HashMap<>();
        Map<String, CommerceContractInfoListVo> contractCodeInfoMap = new HashMap<>();
        Map<String, CommerceAdvertContractListVo> advertContractListVoMap = new HashMap<>();

        // 搜索条件1  搜索标准关联表
        RentChargingStandardStoreListDto standardStoreQueryDto = new RentChargingStandardStoreListDto();
        standardStoreQueryDto.setTenantId(curUser.getTenantId());

        if (CollectionUtil.isNotEmpty(roomViewPageReq.getRoomNameList())) {
            standardStoreQueryDto.setRoomNameList(roomViewPageReq.getRoomNameList());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getStoreIdLike())) {
            standardStoreQueryDto.setStoreIdLike(roomViewPageReq.getStoreIdLike());
        }
        if (roomViewPageReq.getMergeType() != null) {
            // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
            if (roomViewPageReq.getMergeType() == 0) {
                // 商铺类型：0：正铺；1：临促
                standardStoreQueryDto.setShopType(0);
            }
            if (roomViewPageReq.getMergeType() == 1) {
                // 商铺类型：0：正铺；1：临促
                standardStoreQueryDto.setShopType(1);
            }
            if (roomViewPageReq.getMergeType() == 2) {
                // 细分合同类型（0-广告位、1-多经点位）
                standardStoreQueryDto.setAdvertContractType(0);
            }
            if (roomViewPageReq.getMergeType() == 3) {
                // 细分合同类型（0-广告位、1-多经点位）
                standardStoreQueryDto.setAdvertContractType(1);
            }
        }
        // 广告多经场地编号
        if (StringUtils.isNotEmpty(roomViewPageReq.getAdvertName())) {
            standardStoreQueryDto.setAdvertName(roomViewPageReq.getAdvertName());
        }

        RentChargingItemListDto dto = new RentChargingItemListDto();
        // 只返回本金的
        dto.setType(StandardTypeEnum.Principal.getCode());
        if (StringUtils.isNotEmpty(roomViewPageReq.getPrincipalChargingItemNameLike())) {
            dto.setFeeNameLike(roomViewPageReq.getPrincipalChargingItemNameLike());
        }
        List<RentChargingItemListVo> rentChargingItemListVoList = rentChargingItemProvider.list(dto);

        if (CollectionUtil.isEmpty(rentChargingItemListVoList)) {
            pageResp.setPageNum(roomViewPageReq.getPageNum());
            pageResp.setPageSize(roomViewPageReq.getPageSize());
            pageResp.setPages(0);
            pageResp.setTotal(0);
            List<RentChargingStandardStoreRoomViewPageResp> retItems = new ArrayList<>();
            pageResp.setItems(retItems);
            return pageResp;
        }

        RentChargingStandardListDto standardDto = new RentChargingStandardListDto();
        // 只返回本金的
        standardDto.setType(StandardTypeEnum.Principal.getCode());
        if (CollectionUtil.isNotEmpty(rentChargingItemListVoList)) {
            List<Long> chargingItemIdList = new ArrayList<>();
            for (RentChargingItemListVo rentChargingItemListVo : rentChargingItemListVoList) {
                itemIdItemMap.put(rentChargingItemListVo.getId(), rentChargingItemListVo);
                chargingItemIdList.add(rentChargingItemListVo.getId());
            }
            standardDto.setChargingItemIdList(chargingItemIdList);
        }
        List<RentChargingStandardListVo> rentChargingStandardListVoList = rentChargingStandardProvider.list(standardDto);
        if (CollectionUtil.isNotEmpty(rentChargingStandardListVoList)) {
            Set<Long> chargingStandardIdSet = new HashSet<>();
            for (RentChargingStandardListVo rentChargingStandardListVo : rentChargingStandardListVoList) {
                Long standardId = rentChargingStandardListVo.getId();
                if (StringUtils.isNotEmpty(rentChargingStandardListVo.getName())) {
                    standardIdIStandardMap.put(standardId, rentChargingStandardListVo);
                }
                chargingStandardIdSet.add(rentChargingStandardListVo.getId());
            }
            standardStoreQueryDto.setChargingStandardIdSet(chargingStandardIdSet);
        }

        List<RentChargingStandardStoreListVo> standardStoreListVoList = rentChargingStandardStoreProvider.list(standardStoreQueryDto);
        List<String> contractCodeList1 = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(standardStoreListVoList)) {
            for (RentChargingStandardStoreListVo rentChargingStandardStoreListVo : standardStoreListVoList) {
                if (StringUtils.isNotEmpty(rentChargingStandardStoreListVo.getContractCode())) {
                    contractCodeList1.add(rentChargingStandardStoreListVo.getContractCode());
                }
            }
            // 去重
            HashSet<String> set1 = new HashSet<>(contractCodeList1);
            List<String> list1 = new ArrayList<>(set1);
            inputLists.add(list1);
        }

        // 搜索条件2  搜索合同信息表 为了得到合同编号
        CommerceContractInfoListDto infoQueryDto = new CommerceContractInfoListDto();
        infoQueryDto.setTenantId(curUser.getTenantId());
        if (StringUtils.isNotEmpty(roomViewPageReq.getBrandNameLike())) {
            infoQueryDto.setBrandNameLike(roomViewPageReq.getBrandNameLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getSupplierNameLike())) {
            infoQueryDto.setSupplierNameLike(roomViewPageReq.getSupplierNameLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getContractCodeLike())) {
            infoQueryDto.setContractCodeLike(roomViewPageReq.getContractCodeLike());
        }
        if (roomViewPageReq.getMergeType() != null) {
            // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
            if (roomViewPageReq.getMergeType() == 0) {
                infoQueryDto.setShopType(0);
            }
            if (roomViewPageReq.getMergeType() == 1) {
                infoQueryDto.setShopType(1);
            }
        }
        List<CommerceContractInfoListVo> infoVoList = commerceContractInfoProvider.list(infoQueryDto);
        List<String> contractCodeList2 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(infoVoList)) {

            for (CommerceContractInfoListVo commerceContractInfoListVo : infoVoList) {
                if (StringUtils.isNotEmpty(commerceContractInfoListVo.getContractCode())) {
                    contractCodeList2.add(commerceContractInfoListVo.getContractCode());
                    contractCodeInfoMap.put(commerceContractInfoListVo.getContractCode(), commerceContractInfoListVo);
                }
            }
            // 去重
            HashSet<String> set2 = new HashSet<>(contractCodeList2);
            list2 = new ArrayList<>(set2);
            //inputLists.add(list2);
        }

        // 搜索条件3  搜索多经广告合同表 为了得到合同编号
        // 批量查询多经广告合同
        CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
        commerceAdvertContractListDto.setTenantId(curUser.getTenantId());
        if (StringUtils.isNotEmpty(roomViewPageReq.getBrandNameLike())) {
            commerceAdvertContractListDto.setBrandNameLike(roomViewPageReq.getBrandNameLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getSupplierNameLike())) {
            commerceAdvertContractListDto.setSupplierNameLike(roomViewPageReq.getSupplierNameLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getContractCodeLike())) {
            commerceAdvertContractListDto.setContractCodeLike(roomViewPageReq.getContractCodeLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getAdvertName())) {
            commerceAdvertContractListDto.setAdvertName(roomViewPageReq.getAdvertName());
        }
        if (roomViewPageReq.getMergeType() != null) {
            // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
            if (roomViewPageReq.getMergeType() == 2) {
                // 细分合同类型（0-广告位、1-多经点位）
                commerceAdvertContractListDto.setAdvertContractType(0);
            }
            if (roomViewPageReq.getMergeType() == 3) {
                // 细分合同类型（0-广告位、1-多经点位）
                commerceAdvertContractListDto.setAdvertContractType(1);
            }
        }
        List<CommerceAdvertContractListVo> commerceAdvertContractVoList = commerceAdvertContractProvider.list(commerceAdvertContractListDto);
        List<String> contractCodeList3 = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(commerceAdvertContractVoList)) {

            for (CommerceAdvertContractListVo commerceAdvertContractListVo : commerceAdvertContractVoList) {
                if (StringUtils.isNotEmpty(commerceAdvertContractListVo.getContractCode())) {
                    contractCodeList3.add(commerceAdvertContractListVo.getContractCode());
                    advertContractListVoMap.put(commerceAdvertContractListVo.getContractCode(), commerceAdvertContractListVo);
                }
            }
            // 去重
            HashSet<String> set3 = new HashSet<>(contractCodeList3);
            List<String> list3 = new ArrayList<>(set3);
            if (CollectionUtil.isNotEmpty(list2)) {
                // list3和list2合并
                list3.addAll(list2);
            }
            inputLists.add(list3);
        }

        // 如果inputLists.size()为1，且list2不为空，则添加list2
        if (inputLists.size() == 1 && CollectionUtil.isNotEmpty(list2)) {
            inputLists.add(list2);
        }

        // 满足选择条件交集的合同编号
        log.info("inputLists = {}", JSON.toJSON(inputLists));
        List<String> selectContractCodeList = new ArrayList<>();
        // 没有符合条件的，返回
        if (inputLists.size() == 0 || inputLists.size() == 1) {
            pageResp.setPageNum(roomViewPageReq.getPageNum());
            pageResp.setPageSize(roomViewPageReq.getPageSize());
            pageResp.setPages(0);
            pageResp.setTotal(0);
            List<RentChargingStandardStoreRoomViewPageResp> retItems = new ArrayList<>();
            pageResp.setItems(retItems);
            return pageResp;
        }
        if (inputLists.size() > 1) {
            // 使用Stream API求交集
            selectContractCodeList = (inputLists.get(0).stream()
                    .filter(inputLists.get(1)::contains)
                    .collect(Collectors.toList()));
        }

        log.info("selectContractCodeList = {}", JSON.toJSON(selectContractCodeList));

        // 根据合同号列表去查询标准关联表
        RentChargingStandardStoreListDto standardStoreEffectiveQueryDto = new RentChargingStandardStoreListDto();
        standardStoreEffectiveQueryDto.setTenantId(curUser.getTenantId());

        standardStoreEffectiveQueryDto.setContractCodeList(selectContractCodeList);
        // 过滤本金标准id 重要
        List<RentChargingStandardStoreListVo> toFilterList = rentChargingStandardStoreProvider.list(standardStoreEffectiveQueryDto);
        if (CollectionUtil.isNotEmpty(toFilterList)) {
            Set<Long> principalStandardIdList = new HashSet<>();
            for (RentChargingStandardStoreListVo rentChargingStandardStoreListVo : toFilterList) {
                Long standardId = rentChargingStandardStoreListVo.getChargingStandardId();
                RentChargingStandardListVo rentChargingStandardListVo = standardIdIStandardMap.get(standardId);
                if (rentChargingStandardListVo != null) {
                    // 只有本金的才添加
                    if (StandardTypeEnum.Principal.getCode().equals(rentChargingStandardListVo.getType())) {
                        principalStandardIdList.add(standardId);
                    }
                }
            }
            // 限制只有本金的标准id才查询
            standardStoreEffectiveQueryDto.setChargingStandardIdSet(principalStandardIdList);
        }

        PageQuery<RentChargingStandardStoreListDto> pageQuery = new PageQuery<>(roomViewPageReq.getPageNum(), roomViewPageReq.getPageSize(), standardStoreEffectiveQueryDto);
        PageResult<RentChargingStandardStoreListVo> pageResult = rentChargingStandardStoreProvider.page(pageQuery);
        log.info("pageResult = {}", JSON.toJSON(pageResult));

        pageResp.setPageNum(roomViewPageReq.getPageNum());
        pageResp.setPageSize(roomViewPageReq.getPageSize());

        // 组装返回items
        List<RentChargingStandardStoreRoomViewPageResp> retItems = new ArrayList<>();
        if (pageResult != null) {

            pageResp.setPages(pageResult.getPages());
            pageResp.setTotal(pageResult.getTotal());

            List<RentChargingStandardStoreListVo> standardStores = pageResult.getItems();
            if (CollectionUtil.isNotEmpty(standardStores)) {
                for (RentChargingStandardStoreListVo standardStore : standardStores) {
                    RentChargingStandardStoreRoomViewPageResp resp = new RentChargingStandardStoreRoomViewPageResp();
                    resp = BeanUtils.copyProperties(standardStore, RentChargingStandardStoreRoomViewPageResp.class);


                    Long standardId = standardStore.getChargingStandardId();
                    if (standardId != null) {
                        resp.setChargingStandardId(standardId);
                        RentChargingStandardListVo standard = standardIdIStandardMap.get(standardId);


                        if (standard != null) {
                            Long itemId = standard.getChargingItemId();
                            if (itemId != null) {
                                resp.setChargingItemId(itemId);
                                // 收费项目
                                RentChargingItemListVo rentChargingItemListVo = itemIdItemMap.get(itemId);
                                if (rentChargingItemListVo != null && StringUtils.isNotEmpty(rentChargingItemListVo.getFeeName())) {
                                    resp.setChargingItemName(rentChargingItemListVo.getFeeName());
                                }
                            }


                            if (StringUtils.isNotEmpty(standard.getName())) {
                                resp.setChargingStandardName(standard.getName());
                            }
                            if (StringUtils.isNotEmpty(standard.getExpressionDesc())) {
                                resp.setExpressionDesc(standard.getExpressionDesc());
                            }
                        }
                    }

                    String contractCode = standardStore.getContractCode();
                    if (StringUtils.isNotEmpty(contractCode)) {
                        CommerceContractInfoListVo infoVo = contractCodeInfoMap.get(contractCode);
                        if (infoVo != null) {
                            if (infoVo.getBrandId() != null) {
                                resp.setBrandId(infoVo.getBrandId());
                            }
                            if (StringUtils.isNotEmpty(infoVo.getBrandName())) {
                                resp.setBrandName(infoVo.getBrandName());
                            }
                            if (infoVo.getSupplierId() != null) {
                                resp.setSupplierId(infoVo.getSupplierId());
                            }
                            if (StringUtils.isNotEmpty(infoVo.getSupplierName())) {
                                resp.setSupplierName(infoVo.getSupplierName());
                            }
                            if (infoVo.getRentStartDate() != null) {
                                resp.setRentStartDate(infoVo.getRentStartDate());
                            }
                            if (infoVo.getRentEndDate() != null) {
                                resp.setRentEndDate(infoVo.getRentEndDate());
                            }
                            if (infoVo.getShopType() != null) {
                                // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
                                resp.setMergeType(infoVo.getShopType());
                            }
                        }
                        CommerceAdvertContractListVo advertContractVo = advertContractListVoMap.get(contractCode);
                        if (advertContractVo != null) {
                            if (advertContractVo.getBrandId() != null) {
                                resp.setBrandId(advertContractVo.getBrandId());
                            }
                            if (StringUtils.isNotEmpty(advertContractVo.getBrandName())) {
                                resp.setBrandName(advertContractVo.getBrandName());
                            }
                            if (advertContractVo.getSupplierId() != null) {
                                resp.setSupplierId(advertContractVo.getSupplierId());
                            }
                            if (StringUtils.isNotEmpty(advertContractVo.getSupplierName())) {
                                resp.setSupplierName(advertContractVo.getSupplierName());
                            }
                            if (advertContractVo.getRentStartDate() != null) {
                                resp.setRentStartDate(advertContractVo.getRentStartDate());
                            }
                            if (advertContractVo.getRentEndDate() != null) {
                                resp.setRentEndDate(advertContractVo.getRentEndDate());
                            }
                            // 多经/广告的场地编号
                            if (advertContractVo.getAdvertName() != null) {
                                resp.setAdvertName(advertContractVo.getAdvertName());
                            }
                            if (advertContractVo.getAdvertContractType() != null) {
                                // 合同类型（0-广告位、1-多经点位）
                                if (advertContractVo.getAdvertContractType() == 0) {
                                    // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
                                    resp.setMergeType(2);
                                }
                                // 合同类型（0-广告位、1-多经点位）
                                if (advertContractVo.getAdvertContractType() == 1) {
                                    // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
                                    resp.setMergeType(3);
                                }
                            }
                        }
                    }
                    retItems.add(resp);
                }
            }

        }
        pageResp.setItems(retItems);
        log.info("pageResp = {}", JSON.toJSON(pageResp));

        return pageResp;

    }

    @Override
    public boolean roomViewEdit(RentChargingStandardStoreRoomViewEditReq roomViewEditReq) {

        //设置创建人信息
        User optUser = ApiUtils.getUser(User.class);
        LocalDateTime nowTime = LocalDateTime.now();

        // 获取合同主表和合同信息
        String contractCode = roomViewEditReq.getContractCode();
        CommerceContractGetVo contractVo = null;
        CommerceContractInfoGetVo contractInfoGetVo = null;
        // 如果不是多经也不是广告合同，即正铺或者正铺临促
        if (!contractCode.startsWith("D") && !contractCode.startsWith("G")) {
            CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
            commerceContractListDto.setContractCode(contractCode);
            contractVo = commerceContractProvider.get(commerceContractListDto);

            contractInfoGetVo = commerceContractInfoProvider.getByContractCode(contractCode);

        }
        // CommerceAdvertContractGetVo get(CommerceAdvertContractListDto dto)
        CommerceAdvertContractGetVo advertContractGetVo = null;
        // 如果是多经或者广告合同
        if (contractCode.startsWith("D") || contractCode.startsWith("G")) {
            CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
            commerceAdvertContractListDto.setContractCode(contractCode);
            advertContractGetVo = commerceAdvertContractProvider.get(commerceAdvertContractListDto);
        }
        // 根据合同号先删后增
        RentChargingStandardStoreListDto standardStoreListDto = new RentChargingStandardStoreListDto();
        standardStoreListDto.setContractCode(contractCode);
        // 必须指定tenantId 如果仅仅根据门店ID查询会重复
        standardStoreListDto.setTenantId(optUser.getTenantId());

        // 根据本金标准id和滞纳金标准id，查询老的绑定关系
        List<RentChargingStandardStoreListVo> oldList = rentChargingStandardStoreProvider.list(standardStoreListDto);
        List<Long> removeIdList = new ArrayList<>();
        // 过滤出要绑定的门店
        if (CollectionUtil.isNotEmpty(oldList)) {
            for (RentChargingStandardStoreListVo old : oldList) {
                removeIdList.add(old.getId());
            }
        }
        // 批量删除 先删后增
        rentChargingStandardStoreProvider.removeBatchByIds(removeIdList);

        List<RentChargingStandardStoreAddDto> standardStoreAddDtoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(roomViewEditReq.getPrincipalChargingStandardDetailList())) {
            for (RentChargingStandardStoreRoomViewEditDetailReq detailReq : roomViewEditReq.getPrincipalChargingStandardDetailList()) {
                // 本金
                RentChargingStandardStoreAddDto principal = new RentChargingStandardStoreAddDto();
                principal.setChargingStandardId(detailReq.getPrincipalChargingStandardId());
                principal.setApplyPaymentTermSrart(detailReq.getApplyPaymentTermSrart());
                principal.setApplyPaymentTermEnd(detailReq.getApplyPaymentTermEnd());
                principal.setContractCode(contractCode);

                if (contractVo != null) {
                    principal.setStoreId(contractVo.getRoomShopId());
                    principal.setEntId(contractVo.getEntId());
                    principal.setFid(contractVo.getOrgFid());
                    principal.setFcode(contractVo.getFcode());
                    principal.setFname(contractVo.getFname());
                    principal.setOrgFid(contractVo.getOrgFid());
                    principal.setOrgFname(contractVo.getOrgFname());
                    principal.setRoomId(contractVo.getRoomId());
                    principal.setRoomName(contractVo.getName());
                    principal.setTenantId(contractVo.getTenantId());
                    principal.setTenantName(contractVo.getTenantName());
                    principal.setContractCode(contractVo.getContractCode());
                    // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
                    principal.setCommonContractType(100);
                }
                if (contractInfoGetVo != null) {
                    principal.setShopType(contractInfoGetVo.getShopType());
                }

                if (advertContractGetVo != null) {
                    principal.setEntId(advertContractGetVo.getEntId());
                    principal.setFid(advertContractGetVo.getOrgFid());
                    principal.setOrgFid(advertContractGetVo.getOrgFid());
                    principal.setOrgFname(advertContractGetVo.getOrgFname());
                    principal.setTenantId(advertContractGetVo.getTenantId());
                    principal.setTenantName(advertContractGetVo.getTenantName());
                    principal.setContractCode(advertContractGetVo.getContractCode());
                    // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
                    principal.setCommonContractType(200);
                    principal.setAdvertName(advertContractGetVo.getAdvertName());
                    principal.setAdvertContractType(advertContractGetVo.getAdvertContractType());
                }

                principal.setCreateTime(nowTime);
                principal.setCreateBy(optUser.getUserId());
                principal.setCreateUser(optUser.getUserName());
                principal.setCreateUserName(optUser.getRealName());
                standardStoreAddDtoList.add(principal);
            }
        }
        // 滞纳金 一个合同一个滞纳金标准
        if (roomViewEditReq.getLatePaymentChargingStandardId() != null) {
            // 滞纳金
            RentChargingStandardStoreAddDto latePayment = new RentChargingStandardStoreAddDto();
            latePayment.setChargingStandardId(roomViewEditReq.getLatePaymentChargingStandardId());
            latePayment.setContractCode(contractCode);
            if (contractVo != null) {
                latePayment.setStoreId(contractVo.getRoomShopId());
                latePayment.setEntId(contractVo.getEntId());
                latePayment.setFid(contractVo.getOrgFid());
                latePayment.setFcode(contractVo.getFcode());
                latePayment.setFname(contractVo.getFname());
                latePayment.setOrgFid(contractVo.getOrgFid());
                latePayment.setOrgFname(contractVo.getOrgFname());
                latePayment.setRoomId(contractVo.getRoomId());
                latePayment.setRoomName(contractVo.getName());
                latePayment.setTenantId(contractVo.getTenantId());
                latePayment.setTenantName(contractVo.getTenantName());
                latePayment.setContractCode(contractVo.getContractCode());
                // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
                latePayment.setCommonContractType(100);
            }

            if (contractInfoGetVo != null) {
                latePayment.setShopType(contractInfoGetVo.getShopType());
            }

            if (advertContractGetVo != null) {
                latePayment.setEntId(advertContractGetVo.getEntId());
                latePayment.setFid(advertContractGetVo.getOrgFid());
                latePayment.setOrgFid(advertContractGetVo.getOrgFid());
                latePayment.setOrgFname(advertContractGetVo.getOrgFname());
                latePayment.setTenantId(advertContractGetVo.getTenantId());
                latePayment.setTenantName(advertContractGetVo.getTenantName());
                latePayment.setContractCode(advertContractGetVo.getContractCode());
                // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
                latePayment.setCommonContractType(200);
                latePayment.setAdvertName(advertContractGetVo.getAdvertName());
                latePayment.setAdvertContractType(advertContractGetVo.getAdvertContractType());
            }

            // 滞纳金的应用账期默认2000.1.1-2099.1.1 不受界面应用账期限制
            LocalDate latePaymentTermSrart = LocalDate.of(2000, 1, 1);
            LocalDate latePaymentTermEnd = LocalDate.of(2099, 1, 1);

            latePayment.setApplyPaymentTermSrart(latePaymentTermSrart);
            latePayment.setApplyPaymentTermEnd(latePaymentTermEnd);

            latePayment.setCreateTime(nowTime);
            latePayment.setCreateBy(optUser.getUserId());
            latePayment.setCreateUser(optUser.getUserName());
            latePayment.setCreateUserName(optUser.getRealName());

            standardStoreAddDtoList.add(latePayment);
        }

        // 批量保存
        return rentChargingStandardStoreProvider.saveBatch(standardStoreAddDtoList);

    }

    @Override
    public RentChargingStandardStoreRoomViewGetResp roomViewGet(RentChargingStandardStoreRoomViewGetReq roomViewGet) {
        //设置创建人信息
        User optUser = ApiUtils.getUser(User.class);

        // 获取合同主表和合同信息
        String contractCode = roomViewGet.getContractCode();


        CommerceContractGetVo contractVo = null;
        CommerceContractInfoGetVo infoVo = null;
        // 如果不是多经也不是广告合同，即正铺或者正铺临促
        if (!contractCode.startsWith("D") && !contractCode.startsWith("G")) {
            CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
            commerceContractListDto.setContractCode(contractCode);
            contractVo = commerceContractProvider.get(commerceContractListDto);
            infoVo = commerceContractInfoProvider.getByContractCode(contractCode);
        }
        // CommerceAdvertContractGetVo get(CommerceAdvertContractListDto dto)
        CommerceAdvertContractGetVo advertContractGetVo = null;
        // 如果是多经或者广告合同
        if (contractCode.startsWith("D") || contractCode.startsWith("G")) {
            CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
            commerceAdvertContractListDto.setContractCode(contractCode);
            advertContractGetVo = commerceAdvertContractProvider.get(commerceAdvertContractListDto);
        }

        RentChargingStandardStoreRoomViewGetResp resp = new RentChargingStandardStoreRoomViewGetResp();

        resp.setContractCode(contractCode);
        if (contractVo != null) {
            resp.setStoreId(contractVo.getRoomShopId());
            resp.setEntId(contractVo.getEntId());
            resp.setFid(contractVo.getOrgFid());
            resp.setFcode(contractVo.getFcode());
            resp.setFname(contractVo.getFname());
            resp.setOrgFid(contractVo.getOrgFid());
            resp.setOrgFname(contractVo.getOrgFname());
            resp.setRoomId(contractVo.getRoomId());
            resp.setRoomName(contractVo.getName());
            resp.setTenantName(contractVo.getTenantName());
        }
        if (infoVo != null) {
            resp.setBrandId(infoVo.getBrandId());
            resp.setBrandName(infoVo.getBrandName());
            resp.setSupplierId(infoVo.getSupplierId());
            resp.setSupplierName(infoVo.getSupplierName());
            resp.setRentStartDate(infoVo.getRentStartDate());
            resp.setRentEndDate(infoVo.getRentEndDate());
        }
        if (advertContractGetVo != null) {
            resp.setEntId(advertContractGetVo.getEntId());
            resp.setFid(advertContractGetVo.getOrgFid());
            resp.setOrgFid(advertContractGetVo.getOrgFid());
            resp.setOrgFname(advertContractGetVo.getOrgFname());
            resp.setTenantName(advertContractGetVo.getTenantName());
            resp.setBrandId(advertContractGetVo.getBrandId());
            resp.setBrandName(advertContractGetVo.getBrandName());
            resp.setSupplierId(advertContractGetVo.getSupplierId());
            resp.setSupplierName(advertContractGetVo.getSupplierName());
            resp.setRentStartDate(advertContractGetVo.getRentStartDate());
            resp.setRentEndDate(advertContractGetVo.getRentEndDate());
        }
        // 收费项目-收费标准信息
        List<RentChargingStandardStoreRoomViewGetItem> items = new ArrayList<>();
        // 根据合同号先删后增
        RentChargingStandardStoreListDto standardStoreListDto = new RentChargingStandardStoreListDto();
        standardStoreListDto.setContractCode(contractCode);
        // 必须指定tenantId 如果仅仅根据门店ID查询会重复
        standardStoreListDto.setTenantId(optUser.getTenantId());

        // 查询老的绑定关系
        List<RentChargingStandardStoreListVo> standardStoreList = rentChargingStandardStoreProvider.list(standardStoreListDto);
        List<Long> standardIdList = new ArrayList<>();
        Map<Long, RentChargingStandardStoreListVo> standardIdStandardStoreVoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(standardStoreList)) {
            for (RentChargingStandardStoreListVo standardStore : standardStoreList) {
                standardIdList.add(standardStore.getChargingStandardId());
                standardIdStandardStoreVoMap.put(standardStore.getChargingStandardId(), standardStore);
            }
        }

        // 标准
        RentChargingStandardListDto rentChargingStandardListDto = new RentChargingStandardListDto();
        rentChargingStandardListDto.setIdList(standardIdList);
        List<RentChargingStandardListVo> standardList = rentChargingStandardProvider.list(rentChargingStandardListDto);
        List<Long> chargingItemIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(standardList)) {
            for (RentChargingStandardListVo vo : standardList) {
                chargingItemIdList.add(vo.getChargingItemId());
            }
        }
        // 项目
        RentChargingItemListDto rentChargingItemListDto = new RentChargingItemListDto();
        rentChargingItemListDto.setIdList(chargingItemIdList);
        List<RentChargingItemListVo> chargingItemList = rentChargingItemProvider.list(rentChargingItemListDto);
        Map<Long, RentChargingItemListVo> chargingItemIdVoMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(chargingItemList)) {
            for (RentChargingItemListVo itemVo : chargingItemList) {
                chargingItemIdVoMap.put(itemVo.getId(), itemVo);
            }
        }

        log.info("chargingItemIdVoMap = {}", JSON.toJSON(chargingItemIdVoMap));

        if (CollectionUtil.isNotEmpty(standardList)) {
            for (RentChargingStandardListVo vo : standardList) {
                RentChargingStandardStoreRoomViewGetItem item = new RentChargingStandardStoreRoomViewGetItem();
                item = BeanUtils.copyProperties(vo, RentChargingStandardStoreRoomViewGetItem.class);

                // 项目信息
                RentChargingItemListVo rentChargingItemListVo = chargingItemIdVoMap.get(vo.getChargingItemId());
                if (rentChargingItemListVo != null) {
                    item.setChargingItemName(rentChargingItemListVo.getFeeName());
                }
                // 标准信息
                item.setChargingItemId(vo.getChargingItemId());
                item.setChargingStandardName(vo.getName());
                item.setChargingStandardId(vo.getId());

                RentChargingStandardStoreListVo standardStoreVo = standardIdStandardStoreVoMap.get(vo.getId());
                if (standardStoreVo != null) {
                    // 标准应用信息
                    item.setApplyPaymentTermSrart(standardStoreVo.getApplyPaymentTermSrart());
                    item.setApplyPaymentTermEnd(standardStoreVo.getApplyPaymentTermEnd());
                    item.setCreateBy(standardStoreVo.getCreateBy());
                    item.setCreateTime(standardStoreVo.getCreateTime());
                    item.setCreateUser(standardStoreVo.getCreateUser());
                    item.setCreateUserName(standardStoreVo.getCreateUserName());
                    item.setUpdateBy(standardStoreVo.getUpdateBy());
                    item.setUpdateTime(standardStoreVo.getUpdateTime());
                    item.setUpdateUser(standardStoreVo.getUpdateUser());
                    item.setUpdateUserName(standardStoreVo.getUpdateUserName());
                    items.add(item);
                }
            }
            List<RentChargingStandardStoreRoomViewGetItemResp> itemRespList = new ArrayList<>();
            RentChargingStandardStoreRoomViewGetItem latePaymentItem = new RentChargingStandardStoreRoomViewGetItem();
            List<RentChargingStandardStoreRoomViewGetItem> principalItemList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(items)) {
                for (RentChargingStandardStoreRoomViewGetItem item : items) {
                    // 滞纳金
                    if (StandardTypeEnum.LatePayment.getCode().equals(item.getType())) {
                        latePaymentItem = BeanUtils.copyProperties(item, RentChargingStandardStoreRoomViewGetItem.class);
                        break;
                    }
                }
                for (RentChargingStandardStoreRoomViewGetItem item : items) {
                    if (StandardTypeEnum.Principal.getCode().equals(item.getType())) {
                        principalItemList.add(item);
                    }
                }
            }
            // 对Items进行处理成本金和滞纳金在同一行
            if (CollectionUtil.isNotEmpty(principalItemList)) {
                for (RentChargingStandardStoreRoomViewGetItem principalItem : principalItemList) {
                    RentChargingStandardStoreRoomViewGetItemResp itemResp = new RentChargingStandardStoreRoomViewGetItemResp();
                    // 本金
                    itemResp.setId(principalItem.getId());
                    itemResp.setApplyPaymentTermSrart(principalItem.getApplyPaymentTermSrart());
                    itemResp.setApplyPaymentTermEnd(principalItem.getApplyPaymentTermEnd());
                    itemResp.setChargingItemName(principalItem.getChargingItemName());
                    itemResp.setChargingItemId(principalItem.getChargingItemId());
                    itemResp.setPrincipalStandardName(principalItem.getChargingStandardName());
                    itemResp.setPrincipalStandardId(principalItem.getChargingStandardId());
                    itemResp.setPrincipalExpressionDesc(principalItem.getExpressionDesc());
                    itemResp.setPrincipalCalType(principalItem.getCalType());
                    // 滞纳金
                    itemResp.setLatePaymentStandardId(latePaymentItem.getChargingStandardId());
                    itemResp.setLatePaymentStandardName(latePaymentItem.getChargingStandardName());
                    itemResp.setLatePaymentExpressionDesc(latePaymentItem.getExpressionDesc());
                    itemResp.setLatePaymentStartDate(latePaymentItem.getLatePaymentStartDate());
                    itemResp.setLatePaymentRate(latePaymentItem.getLatePaymentRate());
                    itemRespList.add(itemResp);

                }
            }
            resp.setItems(itemRespList);
        }
        return resp;
    }

    /**
     * <p>下载费用项目导入模板<br>
     * 导入模板下载时默认带出当前项目下已有合同数据
     */
    @Override
    public void downloadTemplate() {
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            response.setContentType("application/octet-stream");
            String endCodeFileName = URLEncoder.encode("费用项目导入模板.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
            
            // 获取当前项目下已有的费用项目映射数据
            List<RentChargingStandardStoreExcelTemplateItem> templateItems = getExistingTemplateData();
            
            try (OutputStream outputStream = response.getOutputStream()) {
                EasyExcel.write(outputStream, RentChargingStandardStoreExcelTemplateItem.class)
                        .sheet("费用项目导入模板")
                        .doWrite(templateItems);
            }
        } catch (Exception e) {
            log.error("导出模板异常: {}", e.getMessage(), e);
            throw new ServiceException("导出失败，请稍后再试。", e);
        }
    }

    /**
     * 获取当前项目下已有的费用项目映射数据，用于模板预填充
     */
    private List<RentChargingStandardStoreExcelTemplateItem> getExistingTemplateData() {
        List<RentChargingStandardStoreExcelTemplateItem> templateItems = new ArrayList<>();

        try {

            // 构建查询条件，获取当前项目下的所有费用项目映射数据
            RentChargingStandardStoreRoomViewPageReq pageReq = new RentChargingStandardStoreRoomViewPageReq();
            pageReq.setPageNum(1);
            pageReq.setPageSize(10000); // 设置较大的页面大小以获取所有数据

            // 调用roomViewPage方法获取数据
            PageResp<RentChargingStandardStoreRoomViewPageResp> pageResp = roomViewPage(pageReq);

            if (pageResp != null && pageResp.getItems() != null) {
                // 查询费用项目和收费标准的映射信息
                Map<Long, String> chargingItemMap = getChargingItemMap();
                Map<Long, String> chargingStandardMap = getChargingStandardMap();

                // 获取合同的滞纳金信息（按合同编号分组，每个合同只有一个滞纳金）
                Map<String, String> contractLatePaymentMap = getContractLatePaymentMap(pageResp.getItems());

                for (RentChargingStandardStoreRoomViewPageResp item : pageResp.getItems()) {
                    RentChargingStandardStoreExcelTemplateItem templateItem = new RentChargingStandardStoreExcelTemplateItem();

                    // 基础信息
                    templateItem.setBrandName(item.getBrandName());
                    templateItem.setStoreNumber(item.getRoomName());
                    templateItem.setContractCode(item.getContractCode());
                    // 将LocalDate转换为年-月格式字符串
                    templateItem.setApplyPaymentTermSrart(formatLocalDateToYearMonth(item.getApplyPaymentTermSrart()));
                    templateItem.setApplyPaymentTermEnd(formatLocalDateToYearMonth(item.getApplyPaymentTermEnd()));

                    // 费用项目名称
                    String chargingItemName = chargingItemMap.get(item.getChargingItemId());
                    templateItem.setChargingItemName(chargingItemName);

                    // 本金标准名称
                    String principalStandardName = chargingStandardMap.get(item.getChargingStandardId());
                    templateItem.setPrincipalStandardName(principalStandardName);

                    // 滞纳金标准名称（一个合同只有一个滞纳金标准）
                    String latePaymentStandardName = contractLatePaymentMap.get(item.getContractCode());
                    templateItem.setLatePaymentStandardName(latePaymentStandardName);

                    templateItems.add(templateItem);
                }
            }

            log.info("获取到{}条已有费用项目映射数据用于模板预填充", templateItems.size());

        } catch (Exception e) {
            log.error("获取已有模板数据异常: {}", e.getMessage(), e);
            // 如果获取失败，返回空列表，不影响模板下载
        }

        return templateItems;
    }

    /**
     * <p>费用项目导入<br>
     */
    @Override
    public void imp(RentFeeImportReq req) {
        if (CollectionUtils.isEmpty(req.getFileIds())) {
            throw new ServiceException("附件上传异常,请重新上传");
        }
        for (Long fileId : req.getFileIds()) {
            //上传附件
            SysFileGetVo sysFileGetVo = null;
            try {
                sysFileGetVo = sysFileProvider.get(fileId);
                if (sysFileGetVo == null) {
                    throw new ServiceException("附件上传异常,请重新上传");
                }
            } catch (Exception e) {
                throw new ServiceException("附件上传异常,请重新上传");
            }
            //解析数据
            InputStream inputstream = fileService.getObject(sysFileGetVo.getBuckets(), sysFileGetVo.getFilePath());

            importExcel(inputstream, sysFileGetVo, fileId);
        }
    }

    /**
     * 核心导入Excel处理方法
     */
    private void importExcel(InputStream inputStream, SysFileGetVo sysFileGetVo, Long fileId) {
        LocalDateTime now = LocalDateTime.now();
        User curUser = ApiUtils.getUser(User.class);

        // 1. 文件解析
        log.info("开始解析文件：fileName={}, fileId={}, buckets={}, filePath={}",
                sysFileGetVo.getFileName(), fileId, sysFileGetVo.getBuckets(), sysFileGetVo.getFilePath());

        List<RentChargingStandardStoreExcelTemplateItem> addBatchList = EasyExcel.read(inputStream)
                .head(RentChargingStandardStoreExcelTemplateItem.class)
                .sheet(0)
                .headRowNumber(1)
                .doReadSync();
        log.info("文件解析完成，导入记录数：{}", addBatchList.size());

        if (addBatchList.isEmpty()) {
            log.warn("导入文件为空，没有数据需要处理");
            return;
        }

        // 2. 初始化导入记录
        RentFeeImportAddDto rentFeeImportAddDto = new RentFeeImportAddDto();
        rentFeeImportAddDto.setTenantId(curUser.getTenantId());
        rentFeeImportAddDto.setFileName(sysFileGetVo.getFileName());
        rentFeeImportAddDto.setFileId(fileId);
        rentFeeImportAddDto.setStatus(1);
        rentFeeImportAddDto.setBizType(3); // 3表示费用项目导入
        rentFeeImportAddDto.setTotalCount(addBatchList.size());
        rentFeeImportAddDto.setFailCount(0);
        rentFeeImportAddDto.setCreateBy(curUser.getUserId());
        rentFeeImportAddDto.setCreateUser(curUser.getUserName());
        rentFeeImportAddDto.setCreateUserName(curUser.getRealName());
        rentFeeImportAddDto.setCreateTime(now);

        log.info("导入记录初始化：tenantId={}, fileName={}, fileId={}, bizType={}, totalCount={}",
                rentFeeImportAddDto.getTenantId(), rentFeeImportAddDto.getFileName(),
                rentFeeImportAddDto.getFileId(), rentFeeImportAddDto.getBizType(),
                rentFeeImportAddDto.getTotalCount());

        // 3. 数据验证和处理
        List<RentFeeImportDetailAddDto> rentFeeImportDetailAddDtos = new ArrayList<>();
        List<RentChargingStandardStoreAddDto> successList = new ArrayList<>();

        // 提取所有合同编号用于批量查询
        List<String> contractCodeList = addBatchList.stream()
                .map(RentChargingStandardStoreExcelTemplateItem::getContractCode)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        // 4. 批量查询合同信息
        log.info("开始批量查询合同信息，合同数量：{}, 合同编号：{}", contractCodeList.size(), contractCodeList);
        Map<String, Object> contractInfoMap = getContractInfoMap(contractCodeList);
        log.info("合同信息查询完成，找到有效合同数：{}", contractInfoMap.size());

        // 5. 验证业务规则
        log.info("开始验证业务规则");

        // 建立错误行集合，用于阻止错误数据插入
        Set<Integer> errorLineNumbers = new HashSet<>();

        // 5.1 验证同一合同+同一收费项目只能有一个本金标准
        Map<Integer, String> chargingItemErrorMap = validateContractChargingItemUniqueV2(addBatchList);
        if (!chargingItemErrorMap.isEmpty()) {
            log.warn("发现{}行收费项目唯一性验证错误", chargingItemErrorMap.size());
            errorLineNumbers.addAll(chargingItemErrorMap.keySet());
        } else {
            log.info("收费项目唯一性验证通过");
        }

        // 5.2 验证同一合同只能有一个滞纳金标准
        Map<Integer, String> latePaymentErrorMap = validateContractLatePaymentUniqueV2(addBatchList);
        if (!latePaymentErrorMap.isEmpty()) {
            log.warn("发现{}行滞纳金唯一性验证错误", latePaymentErrorMap.size());
            errorLineNumbers.addAll(latePaymentErrorMap.keySet());
        } else {
            log.info("滞纳金唯一性验证通过");
        }

        log.info("业务规则验证完成，错误行数：{}", errorLineNumbers.size());

        // 6. 逐行处理数据
        log.info("开始逐行处理数据，总行数：{}", addBatchList.size());
        int processedCount = 0;
        int errorCount = 0;

        for (int i = 0; i < addBatchList.size(); i++) {
            RentChargingStandardStoreExcelTemplateItem item = addBatchList.get(i);
            List<String> lineErrors = new ArrayList<>(); // 收集当前行的所有错误信息

            try {
                // 检查是否为业务规则验证失败的行
                if (errorLineNumbers.contains(i)) {
                    // 收集业务规则验证错误信息
                    if (chargingItemErrorMap.containsKey(i)) {
                        lineErrors.add(chargingItemErrorMap.get(i));
                    }
                    if (latePaymentErrorMap.containsKey(i)) {
                        lineErrors.add(latePaymentErrorMap.get(i));
                    }

                    // 即使是业务规则验证失败的行，也要执行数据验证以收集所有错误信息
                    // 不要直接continue，继续执行后续验证
                }
                // 数据验证
                RentFeeImportDetailAddDto validationError = validateImportItem(item, i);
                if (validationError != null) {
                    lineErrors.add(validationError.getFailReason());
                }

                // 合同信息匹配和有效性验证
                Object contractInfo = contractInfoMap.get(item.getContractCode());
                if (contractInfo == null) {
                    lineErrors.add("未找到对应的合同信息");
                }

                // 验证合同有效性（approve_status = 2审批中 或 3审核通过）
                if (contractInfo != null) {
                    RentFeeImportDetailAddDto contractValidationError = validateContractStatus(contractInfo, i, item.getContractCode());
                    if (contractValidationError != null) {
                        lineErrors.add(contractValidationError.getFailReason());
                    }

                    // 验证本金标准名称与租赁类型是否一致
                    RentFeeImportDetailAddDto rentTypeValidationError = validateRentType(item, contractInfo, i);
                    if (rentTypeValidationError != null) {
                        lineErrors.add(rentTypeValidationError.getFailReason());
                    }
                }

                // 如果前面的验证已经有错误，跳过后续验证
                if (!lineErrors.isEmpty()) {
                    String combinedErrorMessage = String.join("；", lineErrors);
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, combinedErrorMessage, item.getContractCode(), fileId));
                    errorCount++;
                    continue;
                }

                // 验证费用项目是否存在（从som_rent_charging_item表查询）
                Long chargingItemId = getChargingItemIdByName(item.getChargingItemName());
                if (chargingItemId == null) {
                    lineErrors.add(String.format("费用项目'%s'在系统中不存在，请检查费用项目名称", item.getChargingItemName()));
                }

                // 验证本金标准是否存在（从som_rent_charging_standard表查询）
                Long principalStandardId = getChargingStandardIdByName(item.getPrincipalStandardName());
                if (principalStandardId == null) {
                    lineErrors.add(String.format("本金标准'%s'在系统中不存在，请检查本金标准名称", item.getPrincipalStandardName()));
                }

                // 验证本金标准与费用项目的关联关系
                if (principalStandardId != null && chargingItemId != null) {
                    RentFeeImportDetailAddDto relationValidationError = validateStandardItemRelation(
                        principalStandardId, chargingItemId, item.getPrincipalStandardName(), item.getChargingItemName(), i, item.getContractCode());
                    if (relationValidationError != null) {
                        lineErrors.add(relationValidationError.getFailReason());
                    }
                }

                // 验证滞纳金标准（如果有的话）
                if (StringUtils.isNotEmpty(item.getLatePaymentStandardName())) {
                    Long latePaymentStandardId = getChargingStandardIdByName(item.getLatePaymentStandardName());
                    if (latePaymentStandardId == null) {
                        lineErrors.add(String.format("滞纳金标准'%s'在系统中不存在，请检查滞纳金标准名称", item.getLatePaymentStandardName()));
                    }
                }

                // 如果有任何验证错误，创建错误记录并跳过
                if (!lineErrors.isEmpty()) {
                    String combinedErrorMessage = String.join("；", lineErrors);
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, combinedErrorMessage, item.getContractCode(), fileId));
                    errorCount++;
                    continue;
                }

                // 数据转换 - 处理本金标准
                RentChargingStandardStoreAddDto principalDto = convertToAddDto(item, contractInfo, curUser, "principal");
                if (principalDto != null) {
                    successList.add(principalDto);
                }

                // 数据转换 - 处理滞纳金标准（如果有的话）
                if (StringUtils.isNotEmpty(item.getLatePaymentStandardName())) {
                    RentChargingStandardStoreAddDto latePaymentDto = convertToAddDto(item, contractInfo, curUser, "latePayment");
                    if (latePaymentDto != null) {
                        successList.add(latePaymentDto);
                    }
                }

                // 记录成功
                String roomName = principalDto != null ? principalDto.getRoomName() : "";
                rentFeeImportDetailAddDtos.add(createSuccessImportDetailDto(i, item.getContractCode(), roomName, fileId));
                processedCount++;

            } catch (Exception e) {
                log.error("处理第{}行数据异常: {}", i + 1, e.getMessage(), e);
                rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "数据处理异常: " + e.getMessage(), item.getContractCode(), fileId));
                errorCount++;
            }
        }

        log.info("数据处理完成，总行数：{}，成功处理：{}，错误：{}", addBatchList.size(), processedCount, errorCount);

        // 6. 批量插入数据
        if (!successList.isEmpty()) {
            // 6.1 先进行假删除操作
            List<String> contractCodes = successList.stream()
                    .map(RentChargingStandardStoreAddDto::getContractCode)
                    .distinct()
                    .collect(Collectors.toList());

            log.info("准备假删除现有数据，涉及合同数：{}, 合同编号：{}", contractCodes.size(), contractCodes);
            rentChargingStandardStoreProvider.deleteByContactCodes(contractCodes);

            // 6.2 批量插入新数据
            log.info("准备批量插入数据，成功记录数：{}", successList.size());
            rentChargingStandardStoreProvider.saveBatch(successList);
            log.info("批量插入完成，成功插入{}条记录", successList.size());
        } else {
            log.warn("没有成功的记录需要插入");
        }

        // 7. 记录导入日志
        rentFeeImportAddDto.setTotalCount(addBatchList.size());
        rentFeeImportAddDto.setFailCount((int) rentFeeImportDetailAddDtos.stream().filter(d -> d.getStatus() == 2).count());

        // 保存导入记录到数据库
        try {
            rentFeeImportProvider.add(rentFeeImportAddDto);
            rentFeeImportDetailAddDtos.forEach(dto -> rentFeeImportDetailProvider.add(dto));
            log.info("导入日志保存成功");
        } catch (Exception e) {
            log.error("保存导入记录异常：{}", e.getMessage(), e);
            // 导入日志保存失败不影响主流程，只记录错误日志
        }

        log.info("费用项目导入完成，文件：{}，总数：{}，成功：{}，失败：{}",
                sysFileGetVo.getFileName(),
                rentFeeImportAddDto.getTotalCount(),
                successList.size(),
                rentFeeImportAddDto.getFailCount());
    }


    /**
     * 合同信息统一DTO --dto在对应的生产者生成
     */


    /**
     * 批量查询合同信息
     */
    private Map<String, Object> getContractInfoMap(List<String> contractCodeList) {
        Map<String, Object> contractInfoMap = new HashMap<>();

        if (CollectionUtils.isEmpty(contractCodeList)) {
            return contractInfoMap;
        }

        // 分离不同类型的合同编号
        List<String> normalContractCodes = new ArrayList<>();
        List<String> advertContractCodes = new ArrayList<>();

        for (String contractCode : contractCodeList) {
            if (contractCode.startsWith("D") || contractCode.startsWith("G")) {
                advertContractCodes.add(contractCode);
            } else {
                normalContractCodes.add(contractCode);
            }
        }

        // 查询正铺临促合同信息
        if (!normalContractCodes.isEmpty()) {
            Map<String, CommerceContractListVo> normalContracts = queryNormalContracts(normalContractCodes);
            contractInfoMap.putAll(normalContracts);
        }

        // 查询多径广告位合同信息
        if (!advertContractCodes.isEmpty()) {
            Map<String, CommerceAdvertContractListVo> advertContracts = queryAdvertContracts(advertContractCodes);
            contractInfoMap.putAll(advertContracts);
        }

        return contractInfoMap;
    }

    /**
     * 查询正铺临促合同信息
     */
    private Map<String, CommerceContractListVo> queryNormalContracts(List<String> contractCodeList) {
        Map<String, CommerceContractListVo> result = new HashMap<>();

        try {
            // 查询合同主表信息
            CommerceContractListDto contractListDto = new CommerceContractListDto();
            contractListDto.setContractCodeList(contractCodeList);
            List<CommerceContractListVo> contractList = commerceContractProvider.list(contractListDto);


            // 构建合同信息映射
            result = contractList.stream()
                    .collect(Collectors.toMap(CommerceContractListVo::getContractCode, v -> v, (v1, v2) -> v1));

        } catch (Exception e) {
            log.error("查询正铺临促合同信息异常: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 查询多径广告位合同信息
     */
    private Map<String, CommerceAdvertContractListVo> queryAdvertContracts(List<String> contractCodeList) {
        Map<String, CommerceAdvertContractListVo> result = new HashMap<>();

        try {
            // 查询广告位合同信息
            CommerceAdvertContractListDto advertContractListDto = new CommerceAdvertContractListDto();
            advertContractListDto.setContractCodeList(contractCodeList);
            List<CommerceAdvertContractListVo> advertContractList = commerceAdvertContractProvider.list(advertContractListDto);

            // 构建合同信息映射
            result = advertContractList.stream()
                    .collect(Collectors.toMap(CommerceAdvertContractListVo::getContractCode, v -> v, (v1, v2) -> v1));

        } catch (Exception e) {
            log.error("查询多径广告位合同信息异常: {}", e.getMessage(), e);
        }

        return result;
    }


    /**
     * 获取费用项目ID和名称的映射
     */
    private Map<Long, String> getChargingItemMap() {
        Map<Long, String> itemMap = new HashMap<>();
        try {
            RentChargingItemListDto queryDto = new RentChargingItemListDto();
            List<RentChargingItemListVo> itemList = rentChargingItemProvider.list(queryDto);

            if (itemList != null) {
                itemMap = itemList.stream()
                        .collect(Collectors.toMap(RentChargingItemListVo::getId, RentChargingItemListVo::getFeeName, (v1, v2) -> v1));
            }
        } catch (Exception e) {
            log.error("获取费用项目映射异常: {}", e.getMessage(), e);
        }
        return itemMap;
    }

    /**
     * 获取费用项目名称到ID的映射
     */
    private Map<String, Long> getChargingItemNameToIdMap() {
        Map<String, Long> nameToIdMap = new HashMap<>();
        try {
            RentChargingItemListDto queryDto = new RentChargingItemListDto();
            List<RentChargingItemListVo> itemList = rentChargingItemProvider.list(queryDto);

            if (itemList != null) {
                nameToIdMap = itemList.stream()
                        .collect(Collectors.toMap(RentChargingItemListVo::getFeeName, RentChargingItemListVo::getId, (v1, v2) -> v1));
            }
        } catch (Exception e) {
            log.error("获取费用项目名称到ID映射异常: {}", e.getMessage(), e);
        }
        return nameToIdMap;
    }

    /**
     * 根据ID获取费用项目名称
     */
    private String getChargingItemNameById(Long chargingItemId, Map<String, Long> nameToIdMap) {
        for (Map.Entry<String, Long> entry : nameToIdMap.entrySet()) {
            if (entry.getValue().equals(chargingItemId)) {
                return entry.getKey();
            }
        }
        return "未知费用项目";
    }

    /**
     * 将LocalDate格式化为年-月字符串（如：2025-1）
     */
    private String formatLocalDateToYearMonth(LocalDate date) {
        if (date == null) {
            return null;
        }

        // 格式化为 yyyy-M（月份不补零）
        return date.getYear() + "-" + date.getMonthValue();
    }

    /**
     * 获取合同的滞纳金标准映射（合同编号 -> 滞纳金标准名称）
     * 优化版本：使用并行执行提升查询性能
     */
    private Map<String, String> getContractLatePaymentMap(List<RentChargingStandardStoreRoomViewPageResp> items) {
        // 使用ConcurrentHashMap保证线程安全
        Map<String, String> contractLatePaymentMap = new ConcurrentHashMap<>();

        try {
            // 获取所有唯一的合同编号
            Set<String> contractCodes = items.stream()
                    .map(RentChargingStandardStoreRoomViewPageResp::getContractCode)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());

            if (contractCodes.isEmpty()) {
                return contractLatePaymentMap;
            }

            log.info("开始并行查询{}个合同的滞纳金信息", contractCodes.size());
            long startTime = System.currentTimeMillis();

            // 使用并行流进行查询，提升性能
            contractCodes.parallelStream().forEach(contractCode -> {
                try {
                    RentChargingStandardStoreRoomViewGetReq getReq = new RentChargingStandardStoreRoomViewGetReq();
                    getReq.setContractCode(contractCode);

                    RentChargingStandardStoreRoomViewGetResp getResp = roomViewGet(getReq);

                    if (getResp != null && getResp.getItems() != null && !getResp.getItems().isEmpty()) {
                        // 查找滞纳金标准名称（一个合同只有一个滞纳金）
                        for (RentChargingStandardStoreRoomViewGetItemResp itemResp : getResp.getItems()) {
                            if (StringUtils.isNotEmpty(itemResp.getLatePaymentStandardName())) {
                                contractLatePaymentMap.put(contractCode, itemResp.getLatePaymentStandardName());
                                break; // 找到滞纳金后跳出循环，因为一个合同只有一个滞纳金
                            }
                        }
                    }

                } catch (Exception e) {
                    log.warn("查询合同{}的滞纳金信息异常: {}", contractCode, e.getMessage());
                }
            });

            long endTime = System.currentTimeMillis();
            log.info("并行查询完成，耗时{}ms，获取到{}个合同的滞纳金信息",
                endTime - startTime, contractLatePaymentMap.size());

        } catch (Exception e) {
            log.error("获取合同滞纳金映射异常: {}", e.getMessage(), e);
        }

        return contractLatePaymentMap;
    }

    /**
     * 获取收费标准ID和名称的映射
     */
    private Map<Long, String> getChargingStandardMap() {
        Map<Long, String> standardMap = new HashMap<>();
        try {
            RentChargingStandardListDto queryDto = new RentChargingStandardListDto();
            List<RentChargingStandardListVo> standardList = rentChargingStandardProvider.list(queryDto);

            if (standardList != null) {
                standardMap = standardList.stream()
                        .collect(Collectors.toMap(RentChargingStandardListVo::getId, RentChargingStandardListVo::getName, (v1, v2) -> v1));
                }
            } catch (Exception e) {
            log.error("获取收费标准映射异常: {}", e.getMessage(), e);
        }
        return standardMap;
    }



    /**
     * 验证同一合同+同一收费项目只能有一个本金标准
     * @param addBatchList 导入数据列表
     * @return 验证错误列表
     */
    private List<RentFeeImportDetailAddDto> validateContractChargingItemUnique(List<RentChargingStandardStoreExcelTemplateItem> addBatchList) {
        List<RentFeeImportDetailAddDto> errors = new ArrayList<>();

        try {
            // 按合同编号分组
            Map<String, List<RentChargingStandardStoreExcelTemplateItem>> contractGroups = new HashMap<>();
            Map<String, List<Integer>> contractLineNumbers = new HashMap<>();

            for (int i = 0; i < addBatchList.size(); i++) {
                RentChargingStandardStoreExcelTemplateItem item = addBatchList.get(i);

                // 跳过空数据
                if (StringUtils.isEmpty(item.getContractCode()) || StringUtils.isEmpty(item.getChargingItemName())
                    || StringUtils.isEmpty(item.getPrincipalStandardName())) {
                    continue;
                }

                String contractCode = item.getContractCode();
                contractGroups.computeIfAbsent(contractCode, k -> new ArrayList<>()).add(item);
                contractLineNumbers.computeIfAbsent(contractCode, k -> new ArrayList<>()).add(i);
            }

            // 获取费用项目映射（费用项目名称 -> charging_item_id）
            Map<String, Long> chargingItemNameToIdMap = getChargingItemNameToIdMap();

            // 验证每个合同
            for (Map.Entry<String, List<RentChargingStandardStoreExcelTemplateItem>> contractEntry : contractGroups.entrySet()) {
                String contractCode = contractEntry.getKey();
                List<RentChargingStandardStoreExcelTemplateItem> contractItems = contractEntry.getValue();
                List<Integer> lineNumbers = contractLineNumbers.get(contractCode);

                // 按charging_item_id分组
                Map<Long, List<String>> chargingItemGroups = new HashMap<>();
                Map<Long, List<Integer>> chargingItemLineNumbers = new HashMap<>();

                for (int i = 0; i < contractItems.size(); i++) {
                    RentChargingStandardStoreExcelTemplateItem item = contractItems.get(i);
                    Long chargingItemId = chargingItemNameToIdMap.get(item.getChargingItemName());

                    if (chargingItemId != null) {
                        chargingItemGroups.computeIfAbsent(chargingItemId, k -> new ArrayList<>()).add(item.getPrincipalStandardName());
                        chargingItemLineNumbers.computeIfAbsent(chargingItemId, k -> new ArrayList<>()).add(lineNumbers.get(i));
                    }
                }

                // 检查每个charging_item_id下是否有多个不同的本金标准
                for (Map.Entry<Long, List<String>> chargingItemEntry : chargingItemGroups.entrySet()) {
                    Long chargingItemId = chargingItemEntry.getKey();
                    List<String> principalStandards = chargingItemEntry.getValue();
                    List<Integer> itemLineNumbers = chargingItemLineNumbers.get(chargingItemId);

                    // 去重后检查是否有多个不同的本金标准
                    Set<String> uniqueStandards = new HashSet<>(principalStandards);
                    if (uniqueStandards.size() > 1) {
                        // 找到费用项目名称用于错误提示
                        String chargingItemName = getChargingItemNameById(chargingItemId, chargingItemNameToIdMap);

                        // 为每个相关行添加错误记录
                        for (Integer lineNumber : itemLineNumbers) {
                            errors.add(createImportDetailDto(lineNumber,
                                String.format("合同'%s'的费用项目'%s'存在多个不同的本金标准：%s，同一合同下同一费用项目只能有一个本金标准",
                                    contractCode, chargingItemName, String.join("、", uniqueStandards)), contractCode));
                        }

                        log.warn("发现违反唯一性规则：合同编号={}, 费用项目ID={}, 费用项目名称={}, 本金标准={}",
                            contractCode, chargingItemId, chargingItemName, uniqueStandards);
                    }
                }
            }

        } catch (Exception e) {
            log.error("验证收费项目唯一性异常: {}", e.getMessage(), e);
            errors.add(createImportDetailDto(0, "验证收费项目唯一性异常: " + e.getMessage(), ""));
        }

        return errors;
    }

    /**
     * 验证同一合同+同一收费项目只能有一个本金标准（V2版本，返回错误信息映射）
     * @param addBatchList 导入数据列表
     * @return 行号到错误信息的映射
     */
    private Map<Integer, String> validateContractChargingItemUniqueV2(List<RentChargingStandardStoreExcelTemplateItem> addBatchList) {
        Map<Integer, String> errorMap = new HashMap<>();

        try {
            // 按合同编号分组
            Map<String, List<RentChargingStandardStoreExcelTemplateItem>> contractGroups = new HashMap<>();
            Map<String, List<Integer>> contractLineNumbers = new HashMap<>();

            for (int i = 0; i < addBatchList.size(); i++) {
                RentChargingStandardStoreExcelTemplateItem item = addBatchList.get(i);

                // 跳过空数据
                if (StringUtils.isEmpty(item.getContractCode()) || StringUtils.isEmpty(item.getChargingItemName())
                    || StringUtils.isEmpty(item.getPrincipalStandardName())) {
                    continue;
                }

                String contractCode = item.getContractCode();
                contractGroups.computeIfAbsent(contractCode, k -> new ArrayList<>()).add(item);
                contractLineNumbers.computeIfAbsent(contractCode, k -> new ArrayList<>()).add(i);
            }

            // 获取费用项目映射（费用项目名称 -> charging_item_id）
            Map<String, Long> chargingItemNameToIdMap = getChargingItemNameToIdMap();

            // 验证每个合同
            for (Map.Entry<String, List<RentChargingStandardStoreExcelTemplateItem>> contractEntry : contractGroups.entrySet()) {
                String contractCode = contractEntry.getKey();
                List<RentChargingStandardStoreExcelTemplateItem> contractItems = contractEntry.getValue();
                List<Integer> lineNumbers = contractLineNumbers.get(contractCode);

                // 按charging_item_id分组
                Map<Long, List<String>> chargingItemGroups = new HashMap<>();
                Map<Long, List<Integer>> chargingItemLineNumbers = new HashMap<>();

                for (int i = 0; i < contractItems.size(); i++) {
                    RentChargingStandardStoreExcelTemplateItem item = contractItems.get(i);
                    Long chargingItemId = chargingItemNameToIdMap.get(item.getChargingItemName());

                    if (chargingItemId != null) {
                        chargingItemGroups.computeIfAbsent(chargingItemId, k -> new ArrayList<>()).add(item.getPrincipalStandardName());
                        chargingItemLineNumbers.computeIfAbsent(chargingItemId, k -> new ArrayList<>()).add(lineNumbers.get(i));
                    }
                }

                // 检查每个charging_item_id下是否有多个不同的本金标准
                for (Map.Entry<Long, List<String>> chargingItemEntry : chargingItemGroups.entrySet()) {
                    Long chargingItemId = chargingItemEntry.getKey();
                    List<String> principalStandards = chargingItemEntry.getValue();
                    List<Integer> itemLineNumbers = chargingItemLineNumbers.get(chargingItemId);

                    // 去重后检查是否有多个不同的本金标准
                    Set<String> uniqueStandards = new HashSet<>(principalStandards);
                    if (uniqueStandards.size() > 1) {
                        // 找到费用项目名称用于错误提示
                        String chargingItemName = getChargingItemNameById(chargingItemId, chargingItemNameToIdMap);
                        String errorMessage = String.format("合同'%s'的费用项目'%s'存在多个不同的本金标准：%s，同一合同下同一费用项目只能有一个本金标准",
                            contractCode, chargingItemName, String.join("、", uniqueStandards));

                        // 为每个相关行添加错误记录
                        for (Integer lineNumber : itemLineNumbers) {
                            errorMap.put(lineNumber, errorMessage);
                        }

                        log.warn("发现违反唯一性规则：合同编号={}, 费用项目ID={}, 费用项目名称={}, 本金标准={}",
                            contractCode, chargingItemId, chargingItemName, uniqueStandards);
                    }
                }
            }

        } catch (Exception e) {
            log.error("验证收费项目唯一性异常: {}", e.getMessage(), e);
        }

        return errorMap;
    }

    /**
     * 验证同一合同只能有一个滞纳金标准
     * @param addBatchList 导入数据列表
     * @return 验证错误列表
     */
    private List<RentFeeImportDetailAddDto> validateContractLatePaymentUnique(List<RentChargingStandardStoreExcelTemplateItem> addBatchList) {
        List<RentFeeImportDetailAddDto> errors = new ArrayList<>();

        try {
            // 按合同编号分组
            Map<String, List<String>> contractLatePaymentMap = new HashMap<>();
            Map<String, List<Integer>> contractLineNumbers = new HashMap<>();

            for (int i = 0; i < addBatchList.size(); i++) {
                RentChargingStandardStoreExcelTemplateItem item = addBatchList.get(i);

                // 跳过空数据
                if (StringUtils.isEmpty(item.getContractCode())) {
                    continue;
                }

                String contractCode = item.getContractCode();
                contractLineNumbers.computeIfAbsent(contractCode, k -> new ArrayList<>()).add(i);

                // 收集滞纳金标准（只收集非空的）
                if (StringUtils.isNotEmpty(item.getLatePaymentStandardName())) {
                    contractLatePaymentMap.computeIfAbsent(contractCode, k -> new ArrayList<>()).add(item.getLatePaymentStandardName());
                }
            }

            // 验证每个合同的滞纳金标准唯一性
            for (Map.Entry<String, List<String>> entry : contractLatePaymentMap.entrySet()) {
                String contractCode = entry.getKey();
                List<String> latePaymentStandards = entry.getValue();
                List<Integer> lineNumbers = contractLineNumbers.get(contractCode);

                // 去重后检查是否有多个不同的滞纳金标准
                Set<String> uniqueLatePaymentStandards = new HashSet<>(latePaymentStandards);
                if (uniqueLatePaymentStandards.size() > 1) {
                    // 为该合同的所有相关行添加错误记录
                    for (Integer lineNumber : lineNumbers) {
                        errors.add(createImportDetailDto(lineNumber,
                            String.format("合同'%s'存在多个不同的滞纳金标准：%s，同一合同只能有一个滞纳金标准",
                                contractCode, String.join("、", uniqueLatePaymentStandards)), contractCode));
                    }

                    log.warn("发现违反滞纳金唯一性规则：合同编号={}, 滞纳金标准={}",
                        contractCode, uniqueLatePaymentStandards);
                }
            }

        } catch (Exception e) {
            log.error("验证滞纳金唯一性异常: {}", e.getMessage(), e);
            errors.add(createImportDetailDto(0, "验证滞纳金唯一性异常: " + e.getMessage(), ""));
        }

        return errors;
    }

    /**
     * 验证同一合同只能有一个滞纳金标准（V2版本，返回错误信息映射）
     * @param addBatchList 导入数据列表
     * @return 行号到错误信息的映射
     */
    private Map<Integer, String> validateContractLatePaymentUniqueV2(List<RentChargingStandardStoreExcelTemplateItem> addBatchList) {
        Map<Integer, String> errorMap = new HashMap<>();

        try {
            // 按合同编号分组
            Map<String, List<String>> contractLatePaymentMap = new HashMap<>();
            Map<String, List<Integer>> contractLineNumbers = new HashMap<>();

            for (int i = 0; i < addBatchList.size(); i++) {
                RentChargingStandardStoreExcelTemplateItem item = addBatchList.get(i);

                // 跳过空数据
                if (StringUtils.isEmpty(item.getContractCode())) {
                    continue;
                }

                String contractCode = item.getContractCode();
                contractLineNumbers.computeIfAbsent(contractCode, k -> new ArrayList<>()).add(i);

                // 收集滞纳金标准（只收集非空的）
                if (StringUtils.isNotEmpty(item.getLatePaymentStandardName())) {
                    contractLatePaymentMap.computeIfAbsent(contractCode, k -> new ArrayList<>()).add(item.getLatePaymentStandardName());
                }
            }

            // 验证每个合同的滞纳金标准唯一性
            for (Map.Entry<String, List<String>> entry : contractLatePaymentMap.entrySet()) {
                String contractCode = entry.getKey();
                List<String> latePaymentStandards = entry.getValue();
                List<Integer> lineNumbers = contractLineNumbers.get(contractCode);

                // 去重后检查是否有多个不同的滞纳金标准
                Set<String> uniqueLatePaymentStandards = new HashSet<>(latePaymentStandards);
                if (uniqueLatePaymentStandards.size() > 1) {
                    String errorMessage = String.format("合同'%s'存在多个不同的滞纳金标准：%s，同一合同只能有一个滞纳金标准",
                        contractCode, String.join("、", uniqueLatePaymentStandards));

                    // 为该合同的所有相关行添加错误记录
                    for (Integer lineNumber : lineNumbers) {
                        errorMap.put(lineNumber, errorMessage);
                    }

                    log.warn("发现违反滞纳金唯一性规则：合同编号={}, 滞纳金标准={}",
                        contractCode, uniqueLatePaymentStandards);
                }
            }

        } catch (Exception e) {
            log.error("验证滞纳金唯一性异常: {}", e.getMessage(), e);
        }

        return errorMap;
    }

    /**
     * 验证合同状态是否有效
     * @param contractInfo 合同信息对象
     * @param lineNumber 行号
     * @param contractCode 合同编号
     * @return 验证失败时返回错误详情，验证通过返回null
     */
    private RentFeeImportDetailAddDto validateContractStatus(Object contractInfo, int lineNumber, String contractCode) {
        try {
            Integer approveStatus = null;

            // 根据合同类型获取审批状态
            if (contractInfo instanceof CommerceContractListVo) {
                CommerceContractListVo contract = (CommerceContractListVo) contractInfo;
                approveStatus = contract.getApproveStatus();
            } else if (contractInfo instanceof CommerceAdvertContractListVo) {
                CommerceAdvertContractListVo advertContract = (CommerceAdvertContractListVo) contractInfo;
                approveStatus = advertContract.getApproveStatus();
            }

            // 验证审批状态：2-审批中，3-审核通过
            if (approveStatus == null) {
                return createImportDetailDto(lineNumber, "合同审批状态为空，无法确定合同有效性", contractCode);
            }

            if (approveStatus != 2 && approveStatus != 3) {
                String statusDesc = getApproveStatusDesc(approveStatus);
                return createImportDetailDto(lineNumber,
                    String.format("合同状态无效：%s，只有审批中(2)或审核通过(3)的合同才能导入", statusDesc),
                    contractCode);
            }

            log.debug("合同状态验证通过：合同编号={}, 审批状态={}", contractCode, approveStatus);
            return null; // 验证通过

            } catch (Exception e) {
            log.error("验证合同状态异常：合同编号={}, 异常={}", contractCode, e.getMessage(), e);
            return createImportDetailDto(lineNumber, "验证合同状态异常: " + e.getMessage(), contractCode);
        }
    }

    /**
     * 获取审批状态描述
     */
    private String getApproveStatusDesc(Integer approveStatus) {
        if (approveStatus == null) {
            return "未知状态";
        }
        switch (approveStatus) {
            case 2:
                return "审批中";
            case 3:
                return "审核通过";
            case 4:
                return "已驳回";
            default:
                return "状态码:" + approveStatus;
        }
    }

    /**
     * 验证收费标准与费用项目的关联关系
     * @param standardId 收费标准ID
     * @param itemId 费用项目ID
     * @param standardName 收费标准名称
     * @param itemName 费用项目名称
     * @param lineNumber 行号
     * @param contractCode 合同编号
     * @return 验证失败时返回错误详情，验证通过返回null
     */
    private RentFeeImportDetailAddDto validateStandardItemRelation(Long standardId, Long itemId,
                                                                  String standardName, String itemName,
                                                                  int lineNumber, String contractCode) {
        try {
            // 查询收费标准详情
            RentChargingStandardGetVo standardVo = rentChargingStandardProvider.get(standardId);
            if (standardVo == null) {
                return createImportDetailDto(lineNumber,
                    String.format("收费标准'%s'详情查询失败", standardName), contractCode);
            }

            // 验证收费标准关联的费用项目ID是否匹配
            if (!itemId.equals(standardVo.getChargingItemId())) {
                return createImportDetailDto(lineNumber,
                    String.format("收费标准'%s'与费用项目'%s'不匹配，该标准关联的费用项目ID为%d",
                        standardName, itemName, standardVo.getChargingItemId()), contractCode);
            }

            log.debug("收费标准与费用项目关联关系验证通过：标准名称={}, 项目名称={}", standardName, itemName);
            return null; // 验证通过

        } catch (Exception e) {
            log.error("验证收费标准与费用项目关联关系异常：标准名称={}, 项目名称={}, 异常={}",
                standardName, itemName, e.getMessage(), e);
            return createImportDetailDto(lineNumber,
                String.format("验证收费标准与费用项目关联关系异常: %s", e.getMessage()), contractCode);
        }
    }

    /**
     * 验证日期格式并解析
     * 支持 yyyy-MM-dd 和 yyyy/M/d 两种格式
     * @param dateStr 日期字符串
     * @return 解析后的LocalDate，解析失败返回null
     */
    private LocalDate parseDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }

        try {
            // 尝试解析 yyyy-MM-dd 格式
            if (dateStr.contains("-")) {
                return LocalDate.parse(dateStr, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            // 尝试解析 yyyy/M/d 格式
            else if (dateStr.contains("/")) {
                return LocalDate.parse(dateStr, java.time.format.DateTimeFormatter.ofPattern("yyyy/M/d"));
            }
            // 如果都不匹配，尝试默认解析
            else {
                return LocalDate.parse(dateStr);
            }
            } catch (Exception e) {
            log.warn("日期格式解析失败：{}, 异常：{}", dateStr, e.getMessage());
            return null;
        }
    }

    /**
     * 验证日期格式
     * @param dateStr 日期字符串
     * @param fieldName 字段名称
     * @param lineNumber 行号
     * @param contractCode 合同编号
     * @return 验证失败时返回错误详情，验证通过返回null
     */
    private RentFeeImportDetailAddDto validateDateFormat(String dateStr, String fieldName, int lineNumber, String contractCode) {
        if (StringUtils.isEmpty(dateStr)) {
            return null; // 空值不验证
        }

        LocalDate parsedDate = parseDate(dateStr);
        if (parsedDate == null) {
            return createImportDetailDto(lineNumber,
                String.format("%s格式不正确：'%s'，支持格式：yyyy-MM-dd 或 yyyy/M/d", fieldName, dateStr),
                contractCode);
        }

        return null; // 验证通过
    }

    /**
     * 验证本金标准名称与合同租赁类型是否一致
     */
    private RentFeeImportDetailAddDto validateRentType(RentChargingStandardStoreExcelTemplateItem item, Object contractInfo, int lineNumber) {
        try {
            String contractCode = item.getContractCode();
            String principalStandardName = item.getPrincipalStandardName();

            log.info("开始验证租赁类型：合同编号={}, Excel中本金标准名称={}", contractCode, principalStandardName);

            if (StringUtils.isEmpty(principalStandardName)) {
                log.debug("本金标准名称为空，跳过租赁类型验证");
                return null; // 本金标准名称为空，在其他地方已经验证
            }

            Set<String> validRentTypes = Set.of(
                    "纯租",
                    "纯扣",
                    "保底扣（租金）",
                    "保底扣（租金+运营管理费+物业管理费）"
            );
            if (!validRentTypes.contains(principalStandardName)) {
                log.info("本金标准名称[{}]不在校验范围内，跳过租赁类型校验", principalStandardName);
                return null;
            }

            // 获取合同详细信息中的租赁类型
            String rentType = null;


            // 根据合同类型获取租赁类型
            log.info("合同类型判断：合同编号={}, contractInfo类型={}", contractCode, contractInfo.getClass().getSimpleName());

            if (!contractCode.startsWith("D") && !contractCode.startsWith("G")) {
                // 正铺或正铺临促合同
                log.info("判断为正铺合同：{}", contractCode);
                if (contractInfo instanceof CommerceContractListVo) {
                    log.info("contractInfo类型匹配CommerceContractListVo");
                    CommerceContractListVo contractVo = (CommerceContractListVo) contractInfo;
                    CommerceContractInfoGetVo contractInfoVo = commerceContractInfoProvider.getByContractCode(contractVo.getContractCode());
                    if (contractInfoVo != null) {
                        // 正铺合同租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
                        String rentTypeCode = String.valueOf(contractInfoVo.getRentType());
                        rentType = convertRentTypeCodeToName(rentTypeCode, "normal");
                        log.info("正铺合同租赁类型获取结果：合同编号={}, 数据库租赁类型代码={}, 转换后租赁类型名称={}",
                            contractCode, rentTypeCode, rentType);
                    } else {
                        log.warn("正铺合同{}的详细信息为空", contractCode);
                    }
                }
            } else {
                // 多经或广告位合同
                if (contractInfo instanceof CommerceAdvertContractListVo) {
                    log.info("contractInfo类型匹配CommerceAdvertContractListVo");
                    CommerceAdvertContractListVo advertContractVo = (CommerceAdvertContractListVo) contractInfo;
                    try {
                        // 多经/广告位合同租赁类型：1 纯租、2 纯扣
                        CommerceEffectiveContractVo rentFeeCalculateInfo = commerceAdvertContractProvider.getRentFeeCalculateInfo(advertContractVo.getContractCode());
                        if (rentFeeCalculateInfo != null) {
                            String rentTypeCode = String.valueOf(rentFeeCalculateInfo.getType());
                            rentType = convertRentTypeCodeToName(rentTypeCode, "advert");
                        }
                    } catch (Exception e) {
                        log.warn("获取多经/广告位合同{}租赁类型异常: {}", contractCode, e.getMessage());
                    }
                }
            }

            // 验证租赁类型与本金标准名称是否一致
            log.info("开始比较租赁类型：Excel本金标准名称='{}', 数据库租赁类型名称='{}'", principalStandardName, rentType);

            if (StringUtils.isNotEmpty(rentType)) {
                if (!rentType.equals(principalStandardName)) {
                    log.error("租赁类型验证失败：Excel本金标准名称='{}' != 数据库租赁类型名称='{}'", principalStandardName, rentType);
                    return createImportDetailDto(lineNumber,
                        String.format("本金标准名称'%s'与合同租赁类型'%s'不一致，请检查数据",
                        principalStandardName, rentType), contractCode);
                } else {
                    log.info("租赁类型验证通过：Excel本金标准名称='{}' == 数据库租赁类型名称='{}'", principalStandardName, rentType);
                }
            } else {
                log.warn("合同{}的租赁类型为空，跳过租赁类型验证", contractCode);
            }

        } catch (Exception e) {
            log.error("验证租赁类型异常：合同编号={}, 异常={}", item.getContractCode(), e.getMessage(), e);
            return createImportDetailDto(lineNumber, "验证租赁类型异常: " + e.getMessage(), item.getContractCode());
        }

        return null; // 验证通过
    }

    /**
     * 将租赁类型代码转换为名称
     * @param rentTypeCode 租赁类型代码
     * @param contractType 合同类型：normal-正铺合同，advert-多经/广告位合同
     * @return 租赁类型名称
     */
    private String convertRentTypeCodeToName(String rentTypeCode, String contractType) {
        if (StringUtils.isEmpty(rentTypeCode)) {
            return null;
        }

        try {
            if ("normal".equals(contractType)) {
                // 正铺合同租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
                switch (rentTypeCode) {
                    case "1":
                        return "纯租";
                    case "2":
                        return "纯扣";
                    case "3":
                        return "保底扣（租金）";
                    case "4":
                        return "保底扣（租金+运营管理费+物业管理费）";
                    default:
                        log.warn("未知的正铺合同租赁类型代码：{}", rentTypeCode);
                        return rentTypeCode; // 返回原始代码
                }
            } else if ("advert".equals(contractType)) {
                // 多经/广告位合同租赁类型：1 纯租、2 纯扣
                switch (rentTypeCode) {
                    case "1":
                        return "纯租";
                    case "2":
                        return "纯扣";
                    default:
                        log.warn("未知的多经/广告位合同租赁类型代码：{}", rentTypeCode);
                        return rentTypeCode; // 返回原始代码
                }
            } else {
                log.warn("未知的合同类型：{}", contractType);
                return rentTypeCode; // 返回原始代码
            }
        } catch (Exception e) {
            log.error("转换租赁类型代码异常：rentTypeCode={}, contractType={}, error={}",
                rentTypeCode, contractType, e.getMessage());
            return rentTypeCode; // 返回原始代码
        }
    }

    /**
     * 验证导入数据
     */
    private RentFeeImportDetailAddDto validateImportItem(RentChargingStandardStoreExcelTemplateItem item, int lineNumber) {
        // 合同编号必填验证
        if (StringUtils.isEmpty(item.getContractCode())) {
            return createImportDetailDto(lineNumber, "合同编号不能为空", "");
        }

        // 费用项目必填验证
        if (StringUtils.isEmpty(item.getChargingItemName())) {
            return createImportDetailDto(lineNumber, "费用项目不能为空", item.getContractCode());
        }

        // 本金标准名称必填验证
        if (StringUtils.isEmpty(item.getPrincipalStandardName())) {
            return createImportDetailDto(lineNumber, "本金标准名称不能为空", item.getContractCode());
        }

        // 滞纳金标准名称验证（可选）
        // 如果填写了滞纳金标准名称，则进行验证
        if (StringUtils.isNotEmpty(item.getLatePaymentStandardName())) {
            log.debug("检测到滞纳金标准名称：{}", item.getLatePaymentStandardName());
        }

        // 账期时间格式验证 - 支持 yyyy-M 和 yyyy/M 两种格式
        RentFeeImportDetailAddDto dateValidationError = validateDateFormat(item, lineNumber);
        if (dateValidationError != null) {
            return dateValidationError;
        }

        return null; // 验证通过
    }

    /**
     * 验证日期格式（年-月 或 年/月）
     */
    private RentFeeImportDetailAddDto validateDateFormat(RentChargingStandardStoreExcelTemplateItem item, int lineNumber) {
        // 验证账期开始时间格式
        if (StringUtils.isNotEmpty(item.getApplyPaymentTermSrart())) {
            if (!isValidYearMonthFormat(item.getApplyPaymentTermSrart())) {
                return createImportDetailDto(lineNumber,
                    String.format("账期开始时间格式错误：'%s'，正确格式应为：年-月 或 年/月（如：2025-1 或 2025/1）",
                    item.getApplyPaymentTermSrart()), item.getContractCode());
            }
        }

        // 验证账期结束时间格式
        if (StringUtils.isNotEmpty(item.getApplyPaymentTermEnd())) {
            if (!isValidYearMonthFormat(item.getApplyPaymentTermEnd())) {
                return createImportDetailDto(lineNumber,
                    String.format("账期结束时间格式错误：'%s'，正确格式应为：年-月 或 年/月（如：2025-12 或 2025/12）",
                    item.getApplyPaymentTermEnd()), item.getContractCode());
            }
        }

        // 验证时间逻辑关系
        if (StringUtils.isNotEmpty(item.getApplyPaymentTermSrart()) && StringUtils.isNotEmpty(item.getApplyPaymentTermEnd())) {
            try {
                LocalDate startDate = parseYearMonthToLocalDate(item.getApplyPaymentTermSrart());
                LocalDate endDate = parseYearMonthToLocalDate(item.getApplyPaymentTermEnd());

                if (startDate != null && endDate != null && startDate.isAfter(endDate)) {
                    return createImportDetailDto(lineNumber, "账期开始时间不能晚于结束时间", item.getContractCode());
                }
            } catch (Exception e) {
                return createImportDetailDto(lineNumber, "账期时间解析异常：" + e.getMessage(), item.getContractCode());
            }
        }

        return null;
    }

    /**
     * 验证年-月格式是否正确
     */
    private boolean isValidYearMonthFormat(String yearMonth) {
        if (StringUtils.isEmpty(yearMonth)) {
            return false;
        }

        // 支持 yyyy-M 和 yyyy/M 两种格式
        String pattern1 = "^\\d{4}-([1-9]|1[0-2])$";  // 2025-1 到 2025-12
        String pattern2 = "^\\d{4}/([1-9]|1[0-2])$";  // 2025/1 到 2025/12

        return yearMonth.matches(pattern1) || yearMonth.matches(pattern2);
    }

    /**
     * 将年-月字符串转换为LocalDate（月初第一天）
     */
    private LocalDate parseYearMonthToLocalDate(String yearMonth) {
        if (StringUtils.isEmpty(yearMonth)) {
            return null;
        }

        try {
            String[] parts;
            if (yearMonth.contains("-")) {
                parts = yearMonth.split("-");
            } else if (yearMonth.contains("/")) {
                parts = yearMonth.split("/");
            } else {
                return null;
            }

            if (parts.length != 2) {
                return null;
            }

            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            return LocalDate.of(year, month, 1); // 月初第一天
        } catch (Exception e) {
            log.warn("解析年月格式异常：{}", yearMonth, e);
            return null;
        }
    }

    /**
     * 将Excel数据转换为实体DTO
     */
    private RentChargingStandardStoreAddDto convertToAddDto(RentChargingStandardStoreExcelTemplateItem item,
                                                           Object contractInfo,
                                                           User curUser,
                                                           String standardType) {
        RentChargingStandardStoreAddDto addDto = new RentChargingStandardStoreAddDto();

        // 基础信息
        addDto.setTenantId(curUser.getTenantId());
        addDto.setContractCode(item.getContractCode());

        // 根据合同类型设置不同的信息
        if (contractInfo instanceof CommerceContractListVo) {
            // 正铺临促合同
            CommerceContractListVo contractVo = (CommerceContractListVo) contractInfo;
            addDto.setTenantName(contractVo.getTenantName());
            addDto.setEntId(contractVo.getEntId());
            addDto.setOrgFid(contractVo.getOrgFid());
            addDto.setOrgFname(contractVo.getOrgFname());
            addDto.setFcode(contractVo.getFcode());
            addDto.setFname(contractVo.getFname());
            addDto.setRoomId(contractVo.getRoomId());
            addDto.setRoomName(contractVo.getName());
            addDto.setStoreId(contractVo.getRoomShopId());
            addDto.setCommonContractType(100);
            // 查询合同信息表获取商铺类型
            try {
                CommerceContractInfoGetVo contractInfoVo = commerceContractInfoProvider.getByContractCode(contractVo.getContractCode());
                if (contractInfoVo != null) {
                    addDto.setShopType(contractInfoVo.getShopType());
                } else {
                    log.warn("未找到合同信息：合同编号={}", contractVo.getContractCode());
                }
            } catch (Exception e) {
                log.error("查询合同信息异常：合同编号={}, 异常={}", contractVo.getContractCode(), e.getMessage(), e);
            }

        } else if (contractInfo instanceof CommerceAdvertContractListVo) {
            // 多径广告位合同
            CommerceAdvertContractListVo advertContractVo = (CommerceAdvertContractListVo) contractInfo;
            addDto.setTenantName(advertContractVo.getTenantName());
            addDto.setEntId(advertContractVo.getEntId());
            addDto.setOrgFid(advertContractVo.getOrgFid());
            addDto.setOrgFname(advertContractVo.getOrgFname());
            addDto.setRoomId(null); // 广告位没有房间概念
            addDto.setCommonContractType(200);
            addDto.setRoomName(advertContractVo.getAdvertName());
            addDto.setStoreId(advertContractVo.getAdvertId().toString()); // 广告位ID作为门店ID
            addDto.setAdvertContractType(advertContractVo.getAdvertContractType());
            addDto.setAdvertName(advertContractVo.getAdvertName());
        }

        // 账期信息 - 将字符串格式转换为LocalDate
        if (StringUtils.isNotEmpty(item.getApplyPaymentTermSrart())) {
            LocalDate startDate = parseYearMonthToLocalDate(item.getApplyPaymentTermSrart());
            addDto.setApplyPaymentTermSrart(startDate);
        }
        if (StringUtils.isNotEmpty(item.getApplyPaymentTermEnd())) {
            LocalDate endDate = parseYearMonthToLocalDate(item.getApplyPaymentTermEnd());
            addDto.setApplyPaymentTermEnd(endDate);
        }

        // 根据标准类型查询对应的收费标准ID
        Long chargingStandardId = null;
        if ("principal".equals(standardType)) {
            // 处理本金标准
            chargingStandardId = getChargingStandardIdByName(item.getPrincipalStandardName());
            log.debug("本金标准查询结果：标准名称={}, 标准ID={}", item.getPrincipalStandardName(), chargingStandardId);
        } else if ("latePayment".equals(standardType)) {
            // 处理滞纳金标准
            chargingStandardId = getChargingStandardIdByName(item.getLatePaymentStandardName());
            log.debug("滞纳金标准查询结果：标准名称={}, 标准ID={}", item.getLatePaymentStandardName(), chargingStandardId);
        }

        if (chargingStandardId == null) {
            log.warn("未找到收费标准ID：类型={}, 本金标准名称={}, 滞纳金标准名称={}",
                    standardType, item.getPrincipalStandardName(), item.getLatePaymentStandardName());
            return null;
        }

        addDto.setChargingStandardId(chargingStandardId);

        // 创建人信息
        addDto.setCreateBy(curUser.getUserId());
        addDto.setCreateUser(curUser.getUserName());
        addDto.setCreateUserName(curUser.getRealName());
        addDto.setCreateTime(LocalDateTime.now());
        addDto.setHistoryFlag(0); // 当前数据

        return addDto;
    }

    /**
     * 根据合同编号获取room_shop_id
     */
    private String getRoomShopIdByContractCode(String contractCode) {
        try {
            if (StringUtils.isEmpty(contractCode)) {
                return null;
            }

            // 根据合同编号前缀判断合同类型
            if (!contractCode.startsWith("D") && !contractCode.startsWith("G")) {
                // 正铺或正铺临促合同
                CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
                commerceContractListDto.setContractCode(contractCode);
                CommerceContractGetVo contractVo = commerceContractProvider.get(commerceContractListDto);

                if (contractVo != null && contractVo.getRoomShopId() != null) {
                    return contractVo.getRoomShopId();
                }
            } else {
                // 多经或广告位合同 - 门店id对应advert_name 多径广告编号
                CommerceAdvertContractListDto advertContractListDto = new CommerceAdvertContractListDto();
                advertContractListDto.setContractCode(contractCode);
                CommerceAdvertContractGetVo advertContractGetVo = commerceAdvertContractProvider.get(advertContractListDto);

                if (advertContractGetVo != null && advertContractGetVo.getAdvertName() != null) {
                    return advertContractGetVo.getAdvertName();
                }
            }

        } catch (Exception e) {
            log.warn("根据合同编号{}获取room_shop_id异常: {}", contractCode, e.getMessage());
        }

        return contractCode; // 如果获取失败，返回合同编号作为备用值
    }

    /**
     * 创建导入错误记录
     */
    private RentFeeImportDetailAddDto createImportDetailDto(int lineNumber, String errorMessage, String contractCode, String roomShopId, Long fileId) {
        RentFeeImportDetailAddDto addDto = new RentFeeImportDetailAddDto();
        User curUser = ApiUtils.getUser(User.class);

        addDto.setLine(lineNumber + 1);
        addDto.setStatus(2); // 失败
        addDto.setFname(contractCode); // 使用合同编号作为标识
        addDto.setName(roomShopId); // name字段设置为room_shop_id
        addDto.setImportId(fileId); // import_id字段设置为导入文件ID
        addDto.setFailReason(errorMessage);
        addDto.setCreateBy(curUser.getUserId());
        addDto.setCreateUser(curUser.getUserName());
        addDto.setCreateUserName(curUser.getRealName());
        addDto.setCreateTime(LocalDateTime.now());

        return addDto;
    }

    /**
     * 创建导入错误记录（重载方法，向后兼容）
     */
    private RentFeeImportDetailAddDto createImportDetailDto(int lineNumber, String errorMessage, String contractCode) {
        // 根据合同编号获取room_shop_id
        String roomShopId = getRoomShopIdByContractCode(contractCode);
        return createImportDetailDto(lineNumber, errorMessage, contractCode, roomShopId, null);
    }

    /**
     * 创建导入错误记录（带fileId参数）
     */
    private RentFeeImportDetailAddDto createImportDetailDto(int lineNumber, String errorMessage, String contractCode, Long fileId) {
        // 根据合同编号获取room_shop_id
        String roomShopId = getRoomShopIdByContractCode(contractCode);
        return createImportDetailDto(lineNumber, errorMessage, contractCode, roomShopId, fileId);
    }

    /**
     * 创建导入成功记录
     */
    private RentFeeImportDetailAddDto createSuccessImportDetailDto(int lineNumber, String contractCode, String roomName, Long fileId) {
        RentFeeImportDetailAddDto addDto = new RentFeeImportDetailAddDto();
        User curUser = ApiUtils.getUser(User.class);
        String roomShopId = getRoomShopIdByContractCode(contractCode);
        addDto.setLine(lineNumber + 1);
        addDto.setStatus(1); // 成功
        addDto.setFname(contractCode);
        addDto.setName(roomShopId); // name字段设置为room_shop_id
        addDto.setImportId(fileId); // import_id字段设置为导入文件ID
        addDto.setFailReason(null);
        addDto.setCreateBy(curUser.getUserId());
        addDto.setCreateUser(curUser.getUserName());
        addDto.setCreateUserName(curUser.getRealName());
        addDto.setCreateTime(LocalDateTime.now());

        return addDto;
    }

    /**
     * 根据费用项目名称查询对应的费用项目ID
     */
    private Long getChargingItemIdByName(String chargingItemName) {
        try {
            if (StringUtils.isEmpty(chargingItemName)) {
                log.warn("费用项目名称为空，无法查询费用项目ID");
                return null;
            }

            // 构建查询条件
            RentChargingItemListDto queryDto = new RentChargingItemListDto();
            queryDto.setFeeName(chargingItemName);

            // 查询费用项目
            List<RentChargingItemListVo> itemList = rentChargingItemProvider.list(queryDto);

            if (CollectionUtils.isEmpty(itemList)) {
                log.warn("未找到匹配的费用项目：项目名称={}", chargingItemName);
                return null;
            }

            if (itemList.size() > 1) {
                log.warn("找到多个匹配的费用项目：项目名称={}, 数量={}", chargingItemName, itemList.size());
                // 返回第一个匹配的记录
            }

            Long chargingItemId = itemList.get(0).getId();
            log.debug("成功查询到费用项目ID：项目名称={}, 费用项目ID={}", chargingItemName, chargingItemId);

            return chargingItemId;

        } catch (Exception e) {
            log.error("查询费用项目ID异常：项目名称={}, 异常={}", chargingItemName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据费用本金标准名称查询对应的收费标准ID
     * 通过查询 som_rent_charging_standard 表，根据 name 字段匹配来获取对应的 id
     */
    private Long getChargingStandardIdByName(String principalStandardName) {
        try {
            if (StringUtils.isEmpty(principalStandardName)) {
                log.warn("费用本金标准名称为空，无法查询收费标准ID");
                return null;
            }

            // 构建查询条件
            RentChargingStandardListDto queryDto = new RentChargingStandardListDto();
            queryDto.setName(principalStandardName);
//            queryDto.setTenantId(tenantId);
//            queryDto.setType(1); // 1-本金标准

            // 查询收费标准
            List<RentChargingStandardListVo> standardList = rentChargingStandardProvider.list(queryDto);

            if (CollectionUtils.isEmpty(standardList)) {
                log.warn("未找到匹配的收费标准：标准名称={}", principalStandardName);
                return null;
            }

            if (standardList.size() > 1) {
                log.warn("找到多个匹配的收费标准：标准名称={}, 数量={}", principalStandardName, standardList.size());
                // 返回第一个匹配的记录
            }

            Long chargingStandardId = standardList.get(0).getId();
            log.debug("成功查询到收费标准ID：标准名称={}, 收费标准ID={}", principalStandardName, chargingStandardId);

            return chargingStandardId;

        } catch (Exception e) {
            log.error("查询收费标准ID异常：标准名称={}, 异常={}", principalStandardName, e.getMessage(), e);
            return null;
        }
    }
}
