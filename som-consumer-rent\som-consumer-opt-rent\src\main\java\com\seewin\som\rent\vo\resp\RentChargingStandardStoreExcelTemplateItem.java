package com.seewin.som.rent.vo.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 费用项目导入模板Excel项
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@Setter
@ColumnWidth(20)
public class RentChargingStandardStoreExcelTemplateItem implements Serializable {

    /**
     * 品牌名称
     */
    @ExcelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 铺位号
     */
    @ExcelProperty(value = "铺位号")
    private String storeNumber;

    /**
     * 合同编号
     */
    @ExcelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 账期开始时间
     */
    @ExcelProperty(value = "账期开始时间")
    private LocalDate applyPaymentTermSrart;

    /**
     * 账期结束时间
     */
    @ExcelProperty(value = "账期结束时间")
    private LocalDate applyPaymentTermEnd;

    /**
     * 费用项目
     */
    @ExcelProperty(value = "费用项目")
    private String chargingItemName;

    /**
     * 本金标准名称
     */
    @ExcelProperty(value = "本金标准名称")
    private String principalStandardName;

    /**
     * 滞纳金标准名称
     */
    @ExcelProperty(value = "滞纳金标准名称")
    private String latePaymentStandardName;

}