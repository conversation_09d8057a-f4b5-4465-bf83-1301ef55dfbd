package com.seewin.som.report.service;

import com.seewin.som.report.vo.req.MultiAnalyseReq;
import com.seewin.som.report.vo.resp.MultiAnalyseResp;
import com.seewin.som.report.vo.resp.MultiRentAnalyseResp;

public interface MultiAnalyseService {

    MultiAnalyseResp salesAnalysis(MultiAnalyseReq req);

    MultiAnalyseResp salesDetail(MultiAnalyseReq req);

    MultiRentAnalyseResp rentAnalysis(MultiAnalyseReq req);

    MultiRentAnalyseResp rentDetail(MultiAnalyseReq req);

    void expRentDetail(MultiAnalyseReq req);

}
