package com.seewin.som.report.service.impl;

import com.alibaba.excel.EasyExcel;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.req.EntProjectListDto;
import com.seewin.som.ent.resp.EntProjectListVo;
import com.seewin.som.report.enums.SerchTypeEnum;
import com.seewin.som.report.provider.ReportStatisFlowDataProvider;
import com.seewin.som.report.req.MultiDataAnalyseDto;
import com.seewin.som.report.req.ReportFlowMultiAnalyseListDto;
import com.seewin.som.report.resp.FlowDataAnalyseVo;
import com.seewin.som.report.resp.FlowProjectAnalyseVo;
import com.seewin.som.report.resp.FlowStoreAnalyseVo;
import com.seewin.som.report.service.MultiAnalyseFlowService;
import com.seewin.som.report.utils.BigDecimalUtil;
import com.seewin.som.report.utils.RatioCalculateUtil;
import com.seewin.som.report.vo.req.MultiAnalyseReq;
import com.seewin.som.report.vo.resp.FlowDataAnalyseItem;
import com.seewin.som.report.vo.resp.MultiAnalyseFlowProjectRespDetail;
import com.seewin.som.report.vo.resp.MultiAnalyseFlowResp;
import com.seewin.som.report.vo.resp.MultiAnalyseFlowRespChart;
import com.seewin.som.report.vo.resp.MultiAnalyseFlowRespDetail;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MultiAnalyseFlowServiceImpl implements MultiAnalyseFlowService {

    private final static Logger logger = LoggerFactory.getLogger(MultiAnalyseFlowServiceImpl.class);

    @DubboReference(providedBy = "som-report-mgt")
    private ReportStatisFlowDataProvider reportStatisFlowDataProvider;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;

    @Override
    public MultiAnalyseFlowResp flowAnalysis(MultiAnalyseReq req) {
        MultiAnalyseFlowResp resp = new MultiAnalyseFlowResp();
        //表头统计部分

        // 计算相差天数
        int endStartBetween = (int) ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate()) + 1;

        //查询项目数据
        List<EntProjectListVo> projectListVos = new ArrayList<>();
        EntProjectListDto entProjectDto = new EntProjectListDto();

        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            //查询全国下的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            if (CollectionUtils.isEmpty(req.getCity())) throw new ServiceException("查询城市维度时，城市无数据");
            entProjectDto.setCityList(req.getCity());
            //查询城市下面的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else {
            if (CollectionUtils.isEmpty(req.getTenantId())) throw new ServiceException("查询项目维度时，项目无数据");
            entProjectDto.setIdList(req.getTenantId());
            projectListVos = entProjectProvider.list(entProjectDto);
        }

        List<Long> projectIds = projectListVos.stream().map(EntProjectListVo::getId).toList();
        Map<Long, String> tenantIdCityMap = projectListVos.stream().collect(Collectors.toMap(EntProjectListVo::getId, EntProjectListVo::getFcity));
        Map<Long, String> tenantIdNameMap = projectListVos.stream().collect(Collectors.toMap(EntProjectListVo::getId, EntProjectListVo::getName));

        //组装查询客流DTO
        ReportFlowMultiAnalyseListDto queryDto = new ReportFlowMultiAnalyseListDto();
        queryDto.setStartDate(req.getStartDate());
        queryDto.setEndDate(req.getEndDate());
        queryDto.setSearchType(req.getSearchType());
        queryDto.setTenantIdList(projectIds);

        BigDecimal flowTotalAmount = reportStatisFlowDataProvider.getFlowTotalAmount(queryDto);
        resp.setFlowTotal(flowTotalAmount);
        resp.setFlowTotalAvg(flowTotalAmount.divide(new BigDecimal(endStartBetween), 2, RoundingMode.HALF_UP));

        //商业项目总客流对比
        List<MultiAnalyseFlowRespChart> respChartList = new ArrayList<>();
        //查询各项目客流
        List<FlowProjectAnalyseVo> flowTotalAmountByProject = reportStatisFlowDataProvider.getFlowTotalAmountByProject(queryDto);

        //全国级别显示城市分组数据
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            Map<String, Long> cityCountMap = new HashMap<>();
            for (FlowProjectAnalyseVo flowProjectAnalyseVo : flowTotalAmountByProject) {
                String city = tenantIdCityMap.get(flowProjectAnalyseVo.getTenantId());
                cityCountMap.merge(city, flowProjectAnalyseVo.getEnterCount(), Long::sum);
            }

            for (String key : cityCountMap.keySet()) {
                MultiAnalyseFlowRespChart respChart = new MultiAnalyseFlowRespChart();
                respChart.setName(key);
                BigDecimal currentValue = BigDecimal.valueOf(cityCountMap.get(key));
                respChart.setValue(currentValue);
                respChart.setPercent(BigDecimalUtil.multiply(BigDecimalUtil.divide(currentValue, flowTotalAmount, 4),BigDecimal.valueOf(100)));
                respChartList.add(respChart);
            }
        } else {//城市和项目级别展示项目级别数据
            for (FlowProjectAnalyseVo flowProjectAnalyseVo : flowTotalAmountByProject) {
                if (null == flowProjectAnalyseVo.getEnterCount()) {
                    continue;
                }
                MultiAnalyseFlowRespChart respChart = new MultiAnalyseFlowRespChart();
                respChart.setName(tenantIdNameMap.get(flowProjectAnalyseVo.getTenantId()));
                respChart.setValue(BigDecimal.valueOf(flowProjectAnalyseVo.getEnterCount()));
                respChart.setPercent(BigDecimalUtil.multiply(BigDecimalUtil.divide(BigDecimal.valueOf(flowProjectAnalyseVo.getEnterCount()), flowTotalAmount, 4), BigDecimal.valueOf(100)));
                respChartList.add(respChart);
            }
        }
        resp.setFlowCompareList(respChartList);

        MultiDataAnalyseDto dto = new MultiDataAnalyseDto();
        dto.setTimeType(req.getTimeType());
        dto.setTenantIdList(projectListVos.stream().map(EntProjectListVo::getId).toList());
        dto.setStartLocalDate(req.getStartDate());
        dto.setEndLocalDate(req.getEndDate());
        List<FlowDataAnalyseVo> flowDataAnalyseVoList = reportStatisFlowDataProvider.flowAnalyse(dto);
        List<FlowDataAnalyseItem> flowDataAnalyseItems = BeanUtils.copyProperties(flowDataAnalyseVoList, FlowDataAnalyseItem.class);
        resp.setFlowDataAnalyseItemList(flowDataAnalyseItems);

        return resp;
    }


    public MultiAnalyseFlowResp flowDetail(MultiAnalyseReq req) {
        MultiAnalyseFlowResp resp = new MultiAnalyseFlowResp();
        resp.setFlowDetailList(getFlowDetail(req));
        return resp;
    }

    public void expFlowDetail(MultiAnalyseReq req) {
        List<MultiAnalyseFlowRespDetail> respDetailList = getFlowDetail(req);
        if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            try {
                HttpServletResponse response = ApiUtils.getResponse();
                response.setContentType("application/octet-stream");
                String endCodeFileName = URLEncoder.encode("客流多维数据分析导出.xlsx", "UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
                response.setContentType("application/octet-stream");
                EasyExcel.write(response.getOutputStream(), MultiAnalyseFlowProjectRespDetail.class).sheet("客流多维数据分析导出").doWrite(respDetailList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (req.getSearchType() == SerchTypeEnum.TENANT.value()) {
            try {
                HttpServletResponse response = ApiUtils.getResponse();
                response.setContentType("application/octet-stream");
                String endCodeFileName = URLEncoder.encode("客流多维数据分析导出.xlsx", "UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
                response.setContentType("application/octet-stream");
                EasyExcel.write(response.getOutputStream(), MultiAnalyseFlowRespDetail.class).sheet("客流多维数据分析导出").doWrite(respDetailList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    private List<MultiAnalyseFlowRespDetail> getFlowDetail(MultiAnalyseReq req){
        //查询项目数据
        List<EntProjectListVo> projectListVos = new ArrayList<>();
        EntProjectListDto entProjectDto = new EntProjectListDto();

        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            //查询全国下的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            if (CollectionUtils.isEmpty(req.getCity())) throw new ServiceException("查询城市维度时，城市无数据");
            entProjectDto.setCityList(req.getCity());
            //查询城市下面的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else {
            if (CollectionUtils.isEmpty(req.getTenantId())) throw new ServiceException("查询项目维度时，项目无数据");
            entProjectDto.setIdList(req.getTenantId());
            projectListVos = entProjectProvider.list(entProjectDto);
        }

        List<Long> projectIds = projectListVos.stream().map(EntProjectListVo::getId).toList();

        Map<Long, String> tenantIdNameMap = projectListVos.stream().collect(Collectors.toMap(EntProjectListVo::getId, EntProjectListVo::getName));

        //组装查询客流DTO
        ReportFlowMultiAnalyseListDto queryDto = new ReportFlowMultiAnalyseListDto();
        queryDto.setStartDate(req.getStartDate());
        queryDto.setEndDate(req.getEndDate());
        queryDto.setSearchType(req.getSearchType());
        queryDto.setTenantIdList(projectIds);

        //查询各项目客流
        List<FlowProjectAnalyseVo> flowTotalAmountByProject = reportStatisFlowDataProvider.getFlowTotalAmountByProject(queryDto);

        //同比
        MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
        //环比
        MultiAnalyseReq ringDiff = RatioCalculateUtil.getRingDiff(req);

        List<MultiAnalyseFlowRespDetail> respDetailList = new ArrayList<>();
        if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            //组装查询客流DTO
            queryDto.setStartDate(pariReq.getStartDate());
            queryDto.setEndDate(pariReq.getEndDate());
            List<FlowProjectAnalyseVo> pariFlowTotalAmountByProject = reportStatisFlowDataProvider.getFlowTotalAmountByProject(queryDto);
            Map<Long, Long> pariFlowMap = pariFlowTotalAmountByProject.stream().collect(Collectors.toMap(FlowProjectAnalyseVo::getTenantId, FlowProjectAnalyseVo::getEnterCount));

            queryDto.setStartDate(ringDiff.getStartDate());
            queryDto.setEndDate(ringDiff.getEndDate());
            List<FlowProjectAnalyseVo> ringFlowTotalAmountByProject = reportStatisFlowDataProvider.getFlowTotalAmountByProject(queryDto);
            Map<Long, Long> ringFlowMap = ringFlowTotalAmountByProject.stream().collect(Collectors.toMap(FlowProjectAnalyseVo::getTenantId, FlowProjectAnalyseVo::getEnterCount));


            for (FlowProjectAnalyseVo flowProjectAnalyseVo : flowTotalAmountByProject) {
                MultiAnalyseFlowRespDetail detail = new MultiAnalyseFlowRespDetail();
                detail.setTenantId(flowProjectAnalyseVo.getTenantId());
                detail.setTenantName(tenantIdNameMap.get(flowProjectAnalyseVo.getTenantId()));

                BigDecimal currentValue = BigDecimal.valueOf(flowProjectAnalyseVo.getEnterCount());
                detail.setFlowTotal(currentValue);
                Long pariValueLong = pariFlowMap.get(flowProjectAnalyseVo.getTenantId());
                if (null != pariValueLong) {
                    BigDecimal pariValue = BigDecimal.valueOf(pariValueLong);
                    detail.setFlowPariPassu(RatioCalculateUtil.calcPariAndRingDiff(currentValue, pariValue));
                }else {
                    detail.setFlowPariPassu(BigDecimal.ZERO);
                }

                Long ringValueLong = ringFlowMap.get(flowProjectAnalyseVo.getTenantId());
                if (null != ringValueLong) {
                    BigDecimal ringValue = BigDecimal.valueOf(ringValueLong);
                    detail.setFlowRingThan(RatioCalculateUtil.calcPariAndRingDiff(currentValue, ringValue));
                }else {
                    detail.setFlowRingThan(BigDecimal.ZERO);
                }

                respDetailList.add(detail);
            }

        } else if (req.getSearchType() == SerchTypeEnum.TENANT.value()) {
            List<FlowStoreAnalyseVo> flowTotalAmountByStore = reportStatisFlowDataProvider.getFlowTotalAmountByStore(queryDto);

            queryDto.setStartDate(pariReq.getStartDate());
            queryDto.setEndDate(pariReq.getEndDate());
            List<FlowStoreAnalyseVo> pairFlowTotalAmountByStore = reportStatisFlowDataProvider.getFlowTotalAmountByStore(queryDto);
            Map<String, BigDecimal> pariFrontFlowMap = pairFlowTotalAmountByStore.stream().collect(Collectors.toMap(e->e.getTenantId()+e.getStoreId(), FlowStoreAnalyseVo::getFrontCount));
            Map<String, BigDecimal> pariInShopFlowMap = pairFlowTotalAmountByStore.stream().collect(Collectors.toMap(e->e.getTenantId()+e.getStoreId(), FlowStoreAnalyseVo::getInshopCount));

            queryDto.setStartDate(ringDiff.getStartDate());
            queryDto.setEndDate(ringDiff.getEndDate());
            List<FlowStoreAnalyseVo> ringFlowTotalAmountByStore = reportStatisFlowDataProvider.getFlowTotalAmountByStore(queryDto);
            Map<String, BigDecimal> ringFrontFlowMap = ringFlowTotalAmountByStore.stream().collect(Collectors.toMap(e->e.getTenantId()+e.getStoreId(), FlowStoreAnalyseVo::getFrontCount));
            Map<String, BigDecimal> ringInShopFlowMap = ringFlowTotalAmountByStore.stream().collect(Collectors.toMap(e->e.getTenantId()+e.getStoreId(), FlowStoreAnalyseVo::getInshopCount));

            for (FlowStoreAnalyseVo flowStoreAnalyseVo : flowTotalAmountByStore) {
                MultiAnalyseFlowRespDetail detail = BeanUtils.copyProperties(flowStoreAnalyseVo, MultiAnalyseFlowRespDetail.class);

                //门前客流
                BigDecimal currentFrontValue = flowStoreAnalyseVo.getFrontCount();
                detail.setFlowTotal(currentFrontValue);

                BigDecimal pariFrontValue = pariFrontFlowMap.get(flowStoreAnalyseVo.getTenantId()+flowStoreAnalyseVo.getStoreId());
                if (null != pariFrontValue) {
                    detail.setFlowPariPassu(RatioCalculateUtil.calcPariAndRingDiff(currentFrontValue, pariFrontValue));
                }else {
                    detail.setFlowPariPassu(BigDecimal.ZERO);
                }

                BigDecimal ringFrontValue = ringFrontFlowMap.get(flowStoreAnalyseVo.getTenantId()+flowStoreAnalyseVo.getStoreId());
                if (null != ringFrontValue) {
                    detail.setFlowRingThan(RatioCalculateUtil.calcPariAndRingDiff(currentFrontValue, ringFrontValue));
                }else {
                    detail.setFlowRingThan(BigDecimal.ZERO);
                }

                //进店转化率
                BigDecimal currentRateValue = flowStoreAnalyseVo.getInshopCount().divide(currentFrontValue, 4, RoundingMode.HALF_UP);
                detail.setInShopConvertRate(currentRateValue.multiply(BigDecimal.valueOf(100)));

                if (pariFrontValue != null && pariFrontValue.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal pariRateValue = pariInShopFlowMap.get(flowStoreAnalyseVo.getTenantId()+flowStoreAnalyseVo.getStoreId()).divide(pariFrontValue, 4, RoundingMode.HALF_UP);
                    detail.setInShopConvertRatePariPassu(RatioCalculateUtil.calcPariAndRingDiff(currentRateValue, pariRateValue));
                }else {
                    detail.setInShopConvertRatePariPassu(BigDecimal.ZERO);
                }
                if (ringFrontValue != null && ringFrontValue.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal ringRateValue = ringInShopFlowMap.get(flowStoreAnalyseVo.getTenantId()+flowStoreAnalyseVo.getStoreId()).divide(ringFrontValue, 4, RoundingMode.HALF_UP);
                    detail.setInShopConvertRateRingThan(RatioCalculateUtil.calcPariAndRingDiff(currentRateValue, ringRateValue));
                }
                respDetailList.add(detail);
            }
        }

        return respDetailList;

    }

}
