package com.seewin.som.report.service.impl;

import com.alibaba.excel.EasyExcel;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.req.EntProjectListDto;
import com.seewin.som.ent.resp.EntProjectListVo;
import com.seewin.som.report.enums.SerchTypeEnum;
import com.seewin.som.report.provider.ReportMasterSaleDataProvider;
import com.seewin.som.report.provider.ReportStatisFlowDataProvider;
import com.seewin.som.report.req.ReportManageMultiAnalyseListDto;
import com.seewin.som.report.resp.ManageAnalyseVo;
import com.seewin.som.report.service.MultiAnalyseManageService;
import com.seewin.som.report.utils.BigDecimalUtil;
import com.seewin.som.report.utils.RatioCalculateUtil;
import com.seewin.som.report.vo.req.MultiAnalyseManageReq;
import com.seewin.som.report.vo.req.MultiAnalyseReq;
import com.seewin.som.report.vo.resp.ManageDataAnalyseItem;
import com.seewin.som.report.vo.resp.MultiAnalyseManageDetail;
import com.seewin.som.report.vo.resp.MultiAnalyseManageResp;
import com.seewin.som.report.vo.resp.MultiAnalyseManageRespItem;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MultiAnalyseManageServiceImpl implements MultiAnalyseManageService {

    private final static Logger logger = LoggerFactory.getLogger(MultiAnalyseManageServiceImpl.class);

    @DubboReference(providedBy = "som-report-mgt")
    private ReportStatisFlowDataProvider reportStatisFlowDataProvider;


    @DubboReference(providedBy = "som-report-mgt")
    private ReportMasterSaleDataProvider reportMasterSaleDataProvider;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;


    @Override
    public MultiAnalyseManageResp manageAnalysis(MultiAnalyseManageReq request) {
        MultiAnalyseReq req = BeanUtils.copyProperties(request, MultiAnalyseReq.class);
        //表头统计部分
        MultiAnalyseManageResp resp = new MultiAnalyseManageResp();
        // 计算相差天数
        int endStartBetween = (int) ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate()) + 1;

        //同比
        MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
        //环比
        MultiAnalyseReq ringDiff = RatioCalculateUtil.getRingDiff(req);

        //查询项目数据
        List<EntProjectListVo> projectListVos = new ArrayList<>();
        EntProjectListDto entProjectDto = new EntProjectListDto();
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            //查询全国下的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            if (CollectionUtils.isEmpty(req.getCity())) throw new ServiceException("查询城市维度时，城市无数据");
            entProjectDto.setCityList(req.getCity());
            //查询城市下面的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else {
            if (CollectionUtils.isEmpty(req.getTenantId())) throw new ServiceException("查询项目维度时，项目无数据");
            entProjectDto.setIdList(req.getTenantId());
            projectListVos = entProjectProvider.list(entProjectDto);
        }

        List<Long> projectIds = projectListVos.stream().map(EntProjectListVo::getId).toList();

        ReportManageMultiAnalyseListDto dto = new ReportManageMultiAnalyseListDto();
        dto.setTenantIdList(projectIds);
        dto.setStartDate(req.getStartDate());
        dto.setEndDate(req.getEndDate());
        dto.setTimeType(req.getTimeType());
        dto.setIndicatorType(request.getIndicatorType());
        dto.setGatherType(3);

        //全国级别的汇总
        List<ManageAnalyseVo> countryFeeByProjectCurrent = reportMasterSaleDataProvider.getFlowAmountFeeByProject(dto);
        dto.setStartDate(pariReq.getStartDate());
        dto.setEndDate(pariReq.getEndDate());
        List<ManageAnalyseVo> countryFeeByProjectPari = reportMasterSaleDataProvider.getFlowAmountFeeByProject(dto);
        dto.setStartDate(ringDiff.getStartDate());
        dto.setEndDate(ringDiff.getEndDate());
        List<ManageAnalyseVo> countryFeeByProjectRing = reportMasterSaleDataProvider.getFlowAmountFeeByProject(dto);

        if (request.getSearchType() == SerchTypeEnum.COUNTRY.value()) {

            BigDecimal currentTotal = getTotal(request, countryFeeByProjectCurrent);
            BigDecimal pariTotal = getTotal(request, countryFeeByProjectPari);
            BigDecimal ringTotal = getTotal(request, countryFeeByProjectRing);

            resp.setTotal(currentTotal);
            resp.setPariPassu(getRatio(request, currentTotal, pariTotal));
            resp.setRingThan(getRatio(request, currentTotal, ringTotal));

            //第一张图 全国分析
            dto.setStartDate(req.getStartDate());
            dto.setEndDate(req.getEndDate());
            dto.setGatherType(0);
            List<ManageAnalyseVo> flowAmountFeeByProject = reportMasterSaleDataProvider.getFlowAmountFeeByProject(dto);

            Map<String, BigDecimal> ratioMap = new HashMap<>();
            ratioMap = getRatioMap(request, flowAmountFeeByProject, e -> e.getSaleTime() + "_" + e.getTenantName() + "_" + e.getGatherType());

            //取getSaleTime和getCommercialTypeName再次进行汇总
            Map<String, BigDecimal> dayRatioMap = averageByKeyFields(ratioMap, 0, 2);

            Map<String, BiConsumer<MultiAnalyseManageRespItem, BigDecimal>> valueSetters = new HashMap<>();
            valueSetters.put("餐饮", MultiAnalyseManageRespItem::setCaterValue);
            valueSetters.put("零售", MultiAnalyseManageRespItem::setRetailValue);
            valueSetters.put("娱乐服务", MultiAnalyseManageRespItem::setEntertainValue);

//        Map<String, BiConsumer<MultiAnalyseManageRespItem, BigDecimal>> percentSetters = new HashMap<>();
//        percentSetters.put("餐饮", MultiAnalyseManageRespItem::setCaterPercent);
//        percentSetters.put("零售", MultiAnalyseManageRespItem::setRetailPercent);
//        percentSetters.put("娱乐服务", MultiAnalyseManageRespItem::setEntertainPercent);

            Map<String, MultiAnalyseManageRespItem> respItemMap = new HashMap<>();

            // === 第一步：构建值 ===
            for (Map.Entry<String, BigDecimal> entry : dayRatioMap.entrySet()) {
                String key = entry.getKey();  // "2025-07-07_餐饮"
                BigDecimal ratio = entry.getValue();

                if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
                    ratio = BigDecimalUtil.multiply(ratio, BigDecimal.valueOf(100));
                }

                String[] parts = key.split("_", 2);
                String date = parts[0];
                String commercialType = parts[1];

                MultiAnalyseManageRespItem item = respItemMap.computeIfAbsent(date, d -> {
                    MultiAnalyseManageRespItem newItem = new MultiAnalyseManageRespItem();
                    newItem.setName(d);
                    return newItem;
                });

                // 设置 value
                valueSetters.get(commercialType).accept(item, ratio);

            }

            for (Map.Entry<String, MultiAnalyseManageRespItem> entry : respItemMap.entrySet()) {
                String date = entry.getKey();
                MultiAnalyseManageRespItem item = entry.getValue();

                //处理一下为空的情况 不展示null 而是展示0
                if (item.getCaterValue() == null){
                    item.setCaterValue(BigDecimal.ZERO);
                }
                if (item.getRetailValue() == null){
                    item.setRetailValue(BigDecimal.ZERO);
                }
                if (item.getEntertainValue() == null){
                    item.setEntertainValue(BigDecimal.ZERO);
                }

                BigDecimal total = BigDecimal.ZERO;
                total = BigDecimalUtil.add(total,item.getCaterValue());
                total = BigDecimalUtil.add(total,item.getRetailValue());
                total = BigDecimalUtil.add(total,item.getEntertainValue());

                item.setCaterPercent(BigDecimalUtil.multiply(BigDecimalUtil.divide(item.getCaterValue(), total), BigDecimal.valueOf(100)));
                item.setRetailPercent(BigDecimalUtil.multiply(BigDecimalUtil.divide(item.getRetailValue(), total), BigDecimal.valueOf(100)));
                item.setEntertainPercent(BigDecimalUtil.multiply(BigDecimalUtil.divide(item.getEntertainValue(), total), BigDecimal.valueOf(100)));
            }
            List<MultiAnalyseManageRespItem> respItemList = respItemMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue).toList();
            resp.setCountryAnalyseList(respItemList);

            respItemMap = new HashMap<>();

            //第二部分 各城市分析
            ratioMap = getRatioMap(request, flowAmountFeeByProject, e -> e.getCity() + "_" + e.getTenantName() + "_" + e.getGatherType());
            //取getSaleTime和getCommercialTypeName再次进行汇总
            Map<String, BigDecimal> cityRatioMap = averageByKeyFields(ratioMap, 0, 2);
            // === 第一步：构建值 ===
            for (Map.Entry<String, BigDecimal> entry : cityRatioMap.entrySet()) {
                String key = entry.getKey();  // "城市_餐饮"
                BigDecimal ratio = entry.getValue() == null ? BigDecimal.ZERO : entry.getValue();
                if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
                    ratio = BigDecimalUtil.multiply(ratio, BigDecimal.valueOf(100));
                }
                String[] parts = key.split("_", 2);
                String city = parts[0];
                String commercialType = parts[1];

                MultiAnalyseManageRespItem item = respItemMap.computeIfAbsent(city, d -> {
                    MultiAnalyseManageRespItem newItem = new MultiAnalyseManageRespItem();
                    newItem.setName(d);
                    return newItem;
                });

                // 设置 value
                valueSetters.get(commercialType).accept(item, ratio);

            }

            for (Map.Entry<String, MultiAnalyseManageRespItem> entry : respItemMap.entrySet()) {
                String city = entry.getKey();
                MultiAnalyseManageRespItem item = entry.getValue();

                //处理一下为空的情况 不展示null 而是展示0
                if (item.getCaterValue() == null){
                    item.setCaterValue(BigDecimal.ZERO);
                }
                if (item.getRetailValue() == null){
                    item.setRetailValue(BigDecimal.ZERO);
                }
                if (item.getEntertainValue() == null){
                    item.setEntertainValue(BigDecimal.ZERO);
                }
                BigDecimal total = BigDecimal.ZERO;
                total = BigDecimalUtil.add(total,item.getCaterValue());
                total = BigDecimalUtil.add(total,item.getRetailValue());
                total = BigDecimalUtil.add(total,item.getEntertainValue());

                item.setCaterPercent(BigDecimalUtil.multiply(BigDecimalUtil.divide(item.getCaterValue(), total, 4), BigDecimal.valueOf(100)));
                item.setRetailPercent(BigDecimalUtil.multiply(BigDecimalUtil.divide(item.getRetailValue(), total, 4), BigDecimal.valueOf(100)));
                item.setEntertainPercent(BigDecimalUtil.multiply(BigDecimalUtil.divide(item.getEntertainValue(), total, 4), BigDecimal.valueOf(100)));
            }
            List<MultiAnalyseManageRespItem> cityItemList = respItemMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue).toList();
            resp.setCountryCityAnalyseList(cityItemList);

        }else { // 城市级别的汇总
            Map<String, BigDecimal> currentRatioMap = getRatioMap(request, countryFeeByProjectCurrent, ManageAnalyseVo::getTenantName);
            Map<String, BigDecimal> pairRatioMap = getRatioMap(request, countryFeeByProjectPari, ManageAnalyseVo::getTenantName);
            Map<String, BigDecimal> ringRatioMap = getRatioMap(request, countryFeeByProjectRing, ManageAnalyseVo::getTenantName);

            //项目分析
            List<ManageDataAnalyseItem> cityProjectAnalyseList = new ArrayList<>();
            for (String key : currentRatioMap.keySet()) {
                ManageDataAnalyseItem item = new ManageDataAnalyseItem();
                item.setDateStr(key);
                BigDecimal currentRatio = currentRatioMap.get(key);
                BigDecimal pairRatio = pairRatioMap.get(key);
                BigDecimal ringRatio = ringRatioMap.get(key);

                if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
                    currentRatio = BigDecimalUtil.multiply(currentRatio, BigDecimal.valueOf(100));
                    pairRatio = BigDecimalUtil.multiply(pairRatio, BigDecimal.valueOf(100));
                    ringRatio = BigDecimalUtil.multiply(ringRatio, BigDecimal.valueOf(100));
                }

                item.setValue(currentRatio == null ? BigDecimal.ZERO : currentRatio);
                item.setPariPassuValue(pairRatio == null ? BigDecimal.ZERO : pairRatio);
                item.setRingThanValue(ringRatio == null ? BigDecimal.ZERO : ringRatio);

                item.setPariPassuPercent(getRatio(request, currentRatio, pairRatio));
                item.setRingThanPercent(getRatio(request, currentRatio, ringRatio));

                cityProjectAnalyseList.add(item);
            }
            resp.setCityProjectAnalyseList(cityProjectAnalyseList);

            Map<String, BigDecimal> ratioMap = new HashMap<>();

            ratioMap = getRatioMap(request, countryFeeByProjectCurrent, e -> e.getSaleTime() + "_" + e.getTenantName());
            Map<String, BigDecimal> currentDayMap = averageByKeyFields(ratioMap, 0);

            ratioMap = getRatioMap(request, countryFeeByProjectPari, e -> e.getSaleTime() + "_" + e.getTenantName());
            Map<String, BigDecimal> pariDayMap = averageByKeyFields(ratioMap, 0);

            ratioMap = getRatioMap(request, countryFeeByProjectRing, e -> e.getSaleTime() + "_" + e.getTenantName());
            Map<String, BigDecimal> ringDayMap = averageByKeyFields(ratioMap, 0);

            //城市分析
            List<ManageDataAnalyseItem> cityAnalyseList = new ArrayList<>();
            for (String key : currentDayMap.keySet()) {
                ManageDataAnalyseItem item = new ManageDataAnalyseItem();
                item.setDateStr(key);
                BigDecimal currentRatio = currentDayMap.get(key);
                BigDecimal pairRatio = pariDayMap.get(key);
                BigDecimal ringRatio = ringDayMap.get(key);

                if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
                    currentRatio = BigDecimalUtil.multiply(currentRatio, BigDecimal.valueOf(100));
                    pairRatio = BigDecimalUtil.multiply(pairRatio, BigDecimal.valueOf(100));
                    ringRatio = BigDecimalUtil.multiply(ringRatio, BigDecimal.valueOf(100));
                }

                item.setValue(currentRatio == null ? BigDecimal.ZERO : currentRatio);
                item.setPariPassuValue(pairRatio == null ? BigDecimal.ZERO : pairRatio);
                item.setRingThanValue(ringRatio == null ? BigDecimal.ZERO : ringRatio);

                item.setPariPassuPercent(getRatio(request, currentRatio, pairRatio));
                item.setRingThanPercent(getRatio(request, currentRatio, ringRatio));

                cityAnalyseList.add(item);
            }
            resp.setCityAnalyseList(cityAnalyseList);

        }

        return resp;
    }

    public MultiAnalyseManageResp manageDetail(MultiAnalyseManageReq request) {

        MultiAnalyseReq req = BeanUtils.copyProperties(request, MultiAnalyseReq.class);
        //表头统计部分
        MultiAnalyseManageResp resp = new MultiAnalyseManageResp();

        //同比
        MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
        //环比
        MultiAnalyseReq ringDiff = RatioCalculateUtil.getRingDiff(req);

        //查询项目数据
        List<EntProjectListVo> projectListVos = new ArrayList<>();
        EntProjectListDto entProjectDto = new EntProjectListDto();
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            //查询全国下的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            if (CollectionUtils.isEmpty(req.getCity())) throw new ServiceException("查询城市维度时，城市无数据");
            entProjectDto.setCityList(req.getCity());
            //查询城市下面的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else {
            if (CollectionUtils.isEmpty(req.getTenantId())) throw new ServiceException("查询项目维度时，项目无数据");
            entProjectDto.setIdList(req.getTenantId());
            projectListVos = entProjectProvider.list(entProjectDto);
        }

        List<Long> projectIds = projectListVos.stream().map(EntProjectListVo::getId).toList();

        ReportManageMultiAnalyseListDto dto = new ReportManageMultiAnalyseListDto();
        dto.setTenantIdList(projectIds);
        dto.setStartDate(req.getStartDate());
        dto.setEndDate(req.getEndDate());
        dto.setTimeType(req.getTimeType());
        dto.setIndicatorType(request.getIndicatorType());
        dto.setGatherType(request.getGatherType());

        List<ManageAnalyseVo> flowAmountFeeByProject = reportMasterSaleDataProvider.getFlowAmountFeeByProject(dto);

        dto.setStartDate(pariReq.getStartDate());
        dto.setEndDate(pariReq.getEndDate());
        List<ManageAnalyseVo> flowAmountFeeByProjectPari = reportMasterSaleDataProvider.getFlowAmountFeeByProject(dto);

        dto.setStartDate(ringDiff.getStartDate());
        dto.setEndDate(ringDiff.getEndDate());
        List<ManageAnalyseVo> flowAmountFeeByProjectRing = reportMasterSaleDataProvider.getFlowAmountFeeByProject(dto);


        Map<String, MultiAnalyseManageDetail> detailMap = new HashMap<>();

        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            calculateDetailCountry(request, flowAmountFeeByProject, detailMap, 0);
            calculateDetailCountry(request, flowAmountFeeByProjectPari, detailMap, 1);
            calculateDetailCountry(request, flowAmountFeeByProjectRing, detailMap, 2);
        }else {
            calculateDetailCity(request, flowAmountFeeByProject, detailMap, 0);
            calculateDetailCity(request, flowAmountFeeByProjectPari, detailMap, 1);
            calculateDetailCity(request, flowAmountFeeByProjectRing, detailMap, 2);
        }
        List<MultiAnalyseManageDetail> detailList = detailMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    MultiAnalyseManageDetail detail = entry.getValue();
                    if (detail.getRingThan() == null) {
                        detail.setRingThan(BigDecimal.ZERO);
                    }
                    if (detail.getPariPassu() == null) {
                        detail.setPariPassu(BigDecimal.ZERO);
                    }
                    if (detail.getTotal() == null) {
                        detail.setTotal(BigDecimal.ZERO);
                    }
                    return detail;
                })
                .toList();
        resp.setDetailList(detailList);

        return resp;
    }


    public void expManageDetail(MultiAnalyseManageReq request) {
        List<MultiAnalyseManageDetail> detailList = manageDetail(request).getDetailList();
        List<List<String>> head = new ArrayList<>();

        if (request.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            head.add(Collections.singletonList("城市"));
        }else {
            head.add(Collections.singletonList("项目"));
        }
        switch (request.getGatherType()){
            case 0 -> head.add(Collections.singletonList("业态"));
            case 1 -> head.add(Collections.singletonList("一级品类"));
            case 2 -> head.add(Collections.singletonList("二级品类"));
        }
        switch (request.getIndicatorType()){
            case 0 -> {
                head.add(Collections.singletonList("租售比"));
                head.add(Collections.singletonList("租售比同比"));
                head.add(Collections.singletonList("租售比环比"));
            }
            case 1 -> {
                head.add(Collections.singletonList("销售转化率"));
                head.add(Collections.singletonList("销售转化率同比"));
                head.add(Collections.singletonList("销售转化率环比"));
            }
            case 2 -> {
                head.add(Collections.singletonList("销售坪效"));
                head.add(Collections.singletonList("销售坪效同比"));
                head.add(Collections.singletonList("销售坪效环比"));
            }
        }

        List<Map<String, Object>> dataList = new ArrayList<>();
        for (MultiAnalyseManageDetail detail : detailList) {
            Map<String, Object> tempMap = new LinkedHashMap<>();
            if (request.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
                tempMap.put("城市", detail.getCity());
            }else {
                tempMap.put("项目", detail.getTenantName());
            }
            switch (request.getGatherType()){
                case 0 -> tempMap.put("业态", detail.getCommercialTypeName());
                case 1 -> tempMap.put("一级品类", detail.getCategoryName());
                case 2 -> tempMap.put("二级品类", detail.getCommercialTwo());
            }
            switch (request.getIndicatorType()){
                case 0 -> {
                    tempMap.put("租售比", detail.getTotal());
                    tempMap.put("租售比同比", detail.getPariPassu());
                    tempMap.put("租售比环比", detail.getRingThan());
                }
                case 1 -> {
                    tempMap.put("销售转化率", detail.getTotal());
                    tempMap.put("销售转化率同比", detail.getPariPassu());
                    tempMap.put("销售转化率环比", detail.getRingThan());
                }
                case 2 -> {
                    tempMap.put("销售坪效", detail.getTotal());
                    tempMap.put("销售坪效同比", detail.getPariPassu());
                    tempMap.put("销售坪效环比", detail.getRingThan());
                }
            }
            dataList.add(tempMap);
        }

        try {
            HttpServletResponse response = ApiUtils.getResponse();
            response.setContentType("application/octet-stream");
            String endCodeFileName = URLEncoder.encode("经营多维数据分析导出.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
            response.setContentType("application/octet-stream");

            EasyExcel.write(response.getOutputStream())
                    .head(head)
                    .sheet("经营多维数据分析导出")
                    .doWrite(dataList);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 按 key 分组后，对两个字段的比值进行求和并取平均
     *
     * @param dataList 原始数据列表
     * @param keyFunc 分组键提取器（如 e -> e.getTenantId()）
     * @param numeratorFunc 分子字段提取器（如 e -> e.getTheoryRent()）
     * @param denominatorFunc 分母字段提取器（如 e -> e.getTotalAmount()）
     * @return Map<分组键, 平均比值>
     */
    private <T, K> Map<K, BigDecimal> averageRatioByGroup(
            List<T> dataList,
            Function<T, K> keyFunc,
            Function<T, BigDecimal> numeratorFunc,
            Function<T, BigDecimal> denominatorFunc
    ) {
        return dataList.stream()
                .filter(e -> {
                    BigDecimal denominator = denominatorFunc.apply(e);
                    return denominator != null && denominator.compareTo(BigDecimal.ZERO) != 0;
                })
                .collect(Collectors.groupingBy(
                        keyFunc,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    BigDecimal sum = list.stream()
                                            .map(e -> BigDecimalUtil.divide(numeratorFunc.apply(e), denominatorFunc.apply(e)))
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    int count = list.size();
                                    return count == 0 ? BigDecimal.ZERO :
                                            sum.divide(BigDecimal.valueOf(count), 4, RoundingMode.HALF_UP);
                                }
                        )
                ));
    }

    /**
     * 按 key 的某些字段下标进行分组，对 value 求和
     * @param rawMap 原始 Map（key 为下划线拼接字符串）
     * @param keepIndices 要保留的字段下标（如 [0, 2] 表示保留第1和第3段）
     * @return 分组后的求和值 Map
     */
    private Map<String, BigDecimal> sumByKeyFields(
            Map<String, BigDecimal> rawMap,
            int... keepIndices
    ) {
        return rawMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        entry -> {
                            String[] parts = entry.getKey().split("_");
                            return Arrays.stream(keepIndices)
                                    .mapToObj(i -> i < parts.length ? parts[i] : "")
                                    .collect(Collectors.joining("_"));
                        },
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                Map.Entry::getValue,
                                BigDecimal::add
                        )
                ));
    }

    /**
     * 按 key 的某些字段下标进行分组，对 value 求平均
     * @param rawMap 原始 Map（key 为下划线拼接字符串）
     * @param keepIndices 要保留的字段下标（如 [0, 2] 表示保留第1和第3段）
     * @return 分组后的平均值 Map
     */
    private Map<String, BigDecimal> averageByKeyFields(
            Map<String, BigDecimal> rawMap,
            int... keepIndices
    ) {
        return rawMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        entry -> {
                            String[] parts = entry.getKey().split("_");
                            return Arrays.stream(keepIndices)
                                    .mapToObj(i -> i < parts.length ? parts[i] : "")
                                    .collect(Collectors.joining("_"));
                        },
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                entryList -> {
                                    BigDecimal sum = entryList.stream()
                                            .map(Map.Entry::getValue)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    int count = entryList.size();
                                    return count == 0 ? BigDecimal.ZERO :
                                            sum.divide(BigDecimal.valueOf(count), 4, RoundingMode.HALF_UP);
                                }
                        )
                ));
    }


    private Map<String, BigDecimal> getRatioMap(MultiAnalyseManageReq request, List<ManageAnalyseVo> countryFeeByProjectCurrent, Function<ManageAnalyseVo, String> keyFunc){
        Map<String, BigDecimal> ratioMap = new HashMap<>();
        ratioMap = switch (request.getIndicatorType()) {
            case 0 ->
                //租售比
                    averageRatioByGroup(
                            countryFeeByProjectCurrent,
                            keyFunc,
                            ManageAnalyseVo::getTheoryRent,
                            ManageAnalyseVo::getTotalAmount
                    );
            case 1 ->
                //销售转化率
                    averageRatioByGroup(
                            countryFeeByProjectCurrent,
                            keyFunc,
                            ManageAnalyseVo::getTotalOrder,
                            ManageAnalyseVo::getEnterCount
                    );
            case 2 ->
                //销售坪效
                    averageRatioByGroup(
                            countryFeeByProjectCurrent,
                            keyFunc,
                            ManageAnalyseVo::getTotalAmount,
                            ManageAnalyseVo::getTotalRoomArea
                    );
            default -> ratioMap;
        };
        return ratioMap;
    }

    /**
     * 获取明细汇总值
     */
    private void calculateDetailCity(MultiAnalyseManageReq request, List<ManageAnalyseVo> flowAmountFeeByProject,Map<String, MultiAnalyseManageDetail> detailMap, int type){
        Map<String, BigDecimal> ratioMap = new HashMap<>();
        ratioMap = switch (request.getIndicatorType()) {
            case 0 -> averageRatioByGroup(
                    flowAmountFeeByProject,
                    e -> e.getTenantName() + "_" + e.getGatherType(),
                    ManageAnalyseVo::getTheoryRent,
                    ManageAnalyseVo::getTotalAmount
            );
            case 1 -> averageRatioByGroup(
                    flowAmountFeeByProject,
                    e -> e.getTenantName() + "_" + e.getGatherType(),
                    ManageAnalyseVo::getTotalOrder,
                    ManageAnalyseVo::getEnterCount
            );
            case 2 -> averageRatioByGroup(
                    flowAmountFeeByProject,
                    e -> e.getTenantName() + "_" + e.getGatherType(),
                    ManageAnalyseVo::getTotalAmount,
                    ManageAnalyseVo::getTotalRoomArea
            );
            default -> ratioMap;
        };

        // === 第一步：构建值 ===
        for (Map.Entry<String, BigDecimal> entry : ratioMap.entrySet()) {
            String key = entry.getKey();  // "项目_餐饮"
            BigDecimal ratio = entry.getValue();

            if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
                ratio = BigDecimalUtil.multiply(ratio, BigDecimal.valueOf(100));
            }

            String[] parts = key.split("_", 2);
            String tenantName = parts[0];
            String obj = parts[1];
            MultiAnalyseManageDetail detail = detailMap.computeIfAbsent(tenantName+"_"+obj,c -> new MultiAnalyseManageDetail());
            detail.setTenantName(tenantName);
            switch (request.getGatherType()) {
                case 0 -> detail.setCommercialTypeName(obj);
                case 1 -> detail.setCategoryName(obj);
                case 2 -> detail.setCommercialTwo(obj);
            }
            if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
                switch (type){
                    case 0 -> detail.setTotal(ratio);
                    case 1 -> detail.setPariPassu(BigDecimalUtil.subtract(detail.getTotal(),ratio));
                    case 2 -> detail.setRingThan(BigDecimalUtil.subtract(detail.getTotal(),ratio));
                }
            }else {
                switch (type){
                    case 0 -> detail.setTotal(ratio);
                    case 1 -> detail.setPariPassu(RatioCalculateUtil.calcPariAndRingDiff(detail.getTotal(), ratio));
                    case 2 -> detail.setRingThan(RatioCalculateUtil.calcPariAndRingDiff(detail.getTotal(), ratio));
                }
            }

        }
    }

    /**
     * 获取明细汇总值
     */
    private void calculateDetailCountry(MultiAnalyseManageReq request, List<ManageAnalyseVo> flowAmountFeeByProject,Map<String, MultiAnalyseManageDetail> detailMap, int type){
        Map<String, BigDecimal> ratioMap = new HashMap<>();
        ratioMap = switch (request.getIndicatorType()) {
            case 0 -> averageRatioByGroup(
                    flowAmountFeeByProject,
                    e -> e.getCity() + "_" + e.getTenantName() + "_" + e.getGatherType(),
                    ManageAnalyseVo::getTheoryRent,
                    ManageAnalyseVo::getTotalAmount
            );
            case 1 -> averageRatioByGroup(
                    flowAmountFeeByProject,
                    e -> e.getCity() + "_" + e.getTenantName() + "_" + e.getGatherType(),
                    ManageAnalyseVo::getTotalOrder,
                    ManageAnalyseVo::getEnterCount
            );
            case 2 -> averageRatioByGroup(
                    flowAmountFeeByProject,
                    e -> e.getCity() + "_" + e.getTenantName() + "_" + e.getGatherType(),
                    ManageAnalyseVo::getTotalAmount,
                    ManageAnalyseVo::getTotalRoomArea
            );
            default -> ratioMap;
        };

        //再次进行汇总
        Map<String, BigDecimal> cityRatioMap = averageByKeyFields(ratioMap, 0, 2);
        // === 第一步：构建值 ===
        for (Map.Entry<String, BigDecimal> entry : cityRatioMap.entrySet()) {
            String key = entry.getKey();  // "城市_餐饮"
            BigDecimal ratio = entry.getValue();

            if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
                ratio = BigDecimalUtil.multiply(ratio, BigDecimal.valueOf(100));
            }

            String[] parts = key.split("_", 2);
            String city = parts[0];
            String obj = parts[1];
            MultiAnalyseManageDetail detail = detailMap.computeIfAbsent(city+"_"+obj,c -> new MultiAnalyseManageDetail());
            detail.setCity(city);
            switch (request.getGatherType()) {
                case 0 -> detail.setCommercialTypeName(obj);
                case 1 -> detail.setCategoryName(obj);
                case 2 -> detail.setCommercialTwo(obj);
            }
            if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
                switch (type){
                    case 0 -> detail.setTotal(ratio);
                    case 1 -> detail.setPariPassu(BigDecimalUtil.subtract(detail.getTotal(),ratio));
                    case 2 -> detail.setRingThan(BigDecimalUtil.subtract(detail.getTotal(),ratio));
                }
            }else {
                switch (type){
                    case 0 -> detail.setTotal(ratio);
                    case 1 -> detail.setPariPassu(RatioCalculateUtil.calcPariAndRingDiff(detail.getTotal(), ratio));
                    case 2 -> detail.setRingThan(RatioCalculateUtil.calcPariAndRingDiff(detail.getTotal(), ratio));
                }
            }

        }
    }

    /**
     * 计算三种类型的比值均值
     */
    private BigDecimal getTotal(MultiAnalyseManageReq request, List<ManageAnalyseVo> countryFeeByProject) {
        // 存放所有 a/b 的结果
        List<BigDecimal> ratios = switch (request.getIndicatorType()) {
            case 0 -> countryFeeByProject.stream()
                    .map(vo -> BigDecimalUtil.multiply(BigDecimalUtil.divide(vo.getTheoryRent(), vo.getTotalAmount(), 4),BigDecimal.valueOf(100)))
                    .toList();
            case 1 -> countryFeeByProject.stream()
                    .map(vo -> BigDecimalUtil.multiply(BigDecimalUtil.divide(vo.getTotalOrder(), vo.getEnterCount(), 4),BigDecimal.valueOf(100)))
                    .toList();
            case 2 -> countryFeeByProject.stream()
                    .map(vo -> BigDecimalUtil.divide(vo.getTotalAmount(), vo.getTotalRoomArea(), 4))
                    .toList();
            default -> List.of();
        };

        if (ratios.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal sum = ratios.stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        return BigDecimalUtil.divide(sum, BigDecimal.valueOf(ratios.size()), 4);
    }

    private BigDecimal getRatio(MultiAnalyseManageReq request, BigDecimal ratioA, BigDecimal ratioB) {
        if (request.getIndicatorType() == 0 || request.getIndicatorType() == 1) {
            return BigDecimalUtil.subtract(ratioA, ratioB);
        }else {
            return BigDecimalUtil.multiply(BigDecimalUtil.divide(ratioA, ratioB),BigDecimal.valueOf(100));
        }
    }
}
