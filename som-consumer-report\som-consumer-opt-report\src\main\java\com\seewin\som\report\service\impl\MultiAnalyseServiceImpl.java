package com.seewin.som.report.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONObject;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.som.commerce.provider.CommercialCategoryProvider;
import com.seewin.som.commerce.req.CommercialCategoryListDto;
import com.seewin.som.commerce.resp.CommercialCategoryListVo;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.req.EntProjectListDto;
import com.seewin.som.ent.resp.EntProjectListVo;
import com.seewin.som.report.enums.SerchTypeEnum;
import com.seewin.som.report.provider.*;
import com.seewin.som.report.req.ReportMultiAnalyseListDto;
import com.seewin.som.report.req.ReportMultiDataDto;
import com.seewin.som.report.req.ReportOperateDayAnalyseDto;
import com.seewin.som.report.resp.ReportExitStatisListVo;
import com.seewin.som.report.resp.ReportMultiAnalyseVo;
import com.seewin.som.report.resp.ReportOperateDayAnalyseListVo;
import com.seewin.som.report.resp.ReportOperateDayListVo;
import com.seewin.som.report.service.MultiAnalyseService;
import com.seewin.som.report.utils.BigDecimalUtil;
import com.seewin.som.report.utils.RatioCalculateUtil;
import com.seewin.som.report.vo.req.MultiAnalyseReq;
import com.seewin.som.report.vo.resp.*;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class MultiAnalyseServiceImpl implements MultiAnalyseService {

    private final static Logger logger = LoggerFactory.getLogger(MultiAnalyseServiceImpl.class);

    @DubboReference(providedBy = "som-report-mgt")
    private ReportOperateDayProvider reportOperateDayProvider;

    @DubboReference(providedBy = "som-report-mgt")
    private ReportExitStatisProvider reportExitStatisProvider;

    @DubboReference(providedBy = "som-report-mgt")
    private ReportMasterSaleDataProvider reportMasterSaleDataProvider;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommercialCategoryProvider commercialCategoryProvider;

    @Override
    public MultiAnalyseResp salesAnalysis(MultiAnalyseReq req) {
        MultiAnalyseResp resp = new MultiAnalyseResp();
        List<EntProjectListVo> tenantIdList = getTenantIdList(req);
        Map<String, String> tenantNameCityMap = tenantIdList.stream().collect(Collectors.toMap(EntProjectListVo::getName, EntProjectListVo::getFcity));

        List<ReportMultiAnalyseVo> currentBaseList = getSaleAnalyseBaseList(req, tenantIdList);
        logger.info("currentBaseList size:{}", currentBaseList.size());

        // 计算相差天数
        int endStartBetween = (int) ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate()) + 1;
        ReportMultiAnalyseListDto queryDto = getQueryDto(req, tenantIdList);
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            queryDto.setTenantIdList(null);
        }
        // 销售总额
        BigDecimal saleTotal = reportMasterSaleDataProvider.getSaleTotalAmount(queryDto);
        resp.setSaleTotal(saleTotal);
        // 日均总销售额
        BigDecimal saleTotalAvg = BigDecimalUtil.divide(saleTotal, BigDecimal.valueOf(endStartBetween));
        resp.setSaleTotalAvg(saleTotalAvg);

        // 销售额按日期分组 统计每日销售额
        List<ReportMultiAnalyseVo> saleAmountDateList = reportMasterSaleDataProvider.getSaleAmountByDate(queryDto);

        // 销售额分布
        List<ReportMultiAnalyseVo> saleDistributeList = reportMasterSaleDataProvider.getSaleDistributeList(queryDto);
        if (!CollectionUtils.isEmpty(saleDistributeList)){
            for (ReportMultiAnalyseVo multiAnalyseVo : saleDistributeList) {
                String cityName = tenantNameCityMap.get(multiAnalyseVo.getTenantName());
                multiAnalyseVo.setCity(cityName);
            }

            Map<String, List<ReportMultiAnalyseVo>> saleDistributeMap = new HashMap<>();
            // 销售额分布按城市或者项目进行分组
            if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
                saleDistributeMap = saleDistributeList.stream().collect(Collectors.groupingBy(ReportMultiAnalyseVo::getCity));
            } else {
                saleDistributeMap = saleDistributeList.stream().collect(Collectors.groupingBy(ReportMultiAnalyseVo::getTenantName));
            }
            // 销售额分布按城市或者项目进行分组统计业态比例
            List<MultiAnalyseRespAmountItem> cityDistributeListList = getSaleAnalyseBaseList(saleDistributeMap, saleTotal);
            resp.setSaleDistributeList(cityDistributeListList);

            // 销售额占比
            List<MultiAnalyseRespChart> salePercentList = getSalePercentList(saleDistributeList);
            resp.setSalePercentList(salePercentList);
        }

        // 销售额分析
        List<ReportMultiAnalyseVo> saleAnalyseList = reportMasterSaleDataProvider.getSaleAnalyseList(queryDto);
        if (!CollectionUtils.isEmpty(saleAnalyseList)){
            // 销售额分析按日期进行分组
            Map<String, List<ReportMultiAnalyseVo>> dateMap = new HashMap<>();
            if (req.getTimeType() != null && req.getTimeType() == 0) {
                dateMap = saleAnalyseList.stream().collect(Collectors.groupingBy(ReportMultiAnalyseVo::getYearMonthDay));
            } else {
                dateMap = saleAnalyseList.stream().collect(Collectors.groupingBy(ReportMultiAnalyseVo::getYearMonth));
            }
            List<MultiAnalyseRespAmountItem> saleAmountAnalyseList = getSaleAnalyseBaseList(dateMap, saleTotal);
            resp.setSaleAnalyseList(saleAmountAnalyseList);
        }

        // 商铺数分布
        List<MultiAnalyseRespItem> result = new ArrayList<>();
        roomDistributeList(req, result, currentBaseList, tenantIdList);
        resp.setRoomDistributeList(result);

        // 销售分布
        if (!CollectionUtils.isEmpty(currentBaseList) && !CollectionUtils.isEmpty(saleAmountDateList)) {
            setSaleAmountInfo(req, resp, currentBaseList, saleAmountDateList);
        }

        // 赋值默认值
        setAmountDefaultInfo(req, resp, tenantIdList);

        return resp;
    }


    @Override
    public MultiRentAnalyseResp rentAnalysis(MultiAnalyseReq req) {
        MultiRentAnalyseResp resp = new MultiRentAnalyseResp();

        // 组装查询Dto
        ReportOperateDayAnalyseDto queryDto = new ReportOperateDayAnalyseDto();
        queryDto.setStartDate(req.getStartDate());
        queryDto.setEndDate(req.getEndDate());
        queryDto.setSearchType(req.getSearchType());
        queryDto.setGatherType(req.getGatherType());

        // 先只做全国的
        // 判断要走哪种计算
        Integer target = req.getTarget();
        Integer searchType = req.getSearchType();
        if(0 == searchType){ // 全国
            if(0 == target || 1 == target){// 出租率 、 开业率
                // 查出所有的数据
                List<ReportOperateDayAnalyseListVo> dataList = (0 == target)? reportOperateDayProvider.getAllData(queryDto): reportOperateDayProvider.getOpenAllData(queryDto);
                // 出租率
                double num = calculateAvgRentalRate(dataList,req.getStartDate(),req.getEndDate());
                logger.info("全国出租率：{}", num);

                // 出租率同比
                MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
                queryDto.setStartDate(pariReq.getStartDate());
                queryDto.setEndDate(pariReq.getEndDate());
                // 查出所有的数据
                double pariNum = 0.0;
                double yoyRate = 0.0;
                List<ReportOperateDayAnalyseListVo> pariDataList = (0 == target) ? reportOperateDayProvider.getAllData(queryDto) : reportOperateDayProvider.getOpenAllData(queryDto);
                if(pariDataList != null && pariDataList.size() > 0){
                    // 出租率
                    pariNum = calculateAvgRentalRate(pariDataList,req.getStartDate(),req.getEndDate());
                    if(pariNum != 0.0){
                        yoyRate = Math.round((num - pariNum) / pariNum * 100 * 100.0) / 100.0;
                    }
                }
                logger.info("全国出租率（同比）：{}", pariNum);

                // 出租率环比
                MultiAnalyseReq ringDiffReq = RatioCalculateUtil.getRingDiff(req);
                queryDto.setStartDate(ringDiffReq.getStartDate());
                queryDto.setEndDate(ringDiffReq.getEndDate());
                // 查出所有的数据
                double ringDiffNum = 0.0;
                double qoqRate = 0.0;
                List<ReportOperateDayAnalyseListVo> ringDiffDataList = (0 == target) ? reportOperateDayProvider.getAllData(queryDto) : reportOperateDayProvider.getOpenAllData(queryDto);
                if(ringDiffDataList != null && ringDiffDataList.size() > 0){
                    ringDiffNum = calculateAvgRentalRate(ringDiffDataList,req.getStartDate(),req.getEndDate());
                    if(ringDiffNum != 0.0) {  // 修正：使用环比基准值
                        qoqRate = Math.round((num - ringDiffNum) / ringDiffNum * 100 * 100.0) / 100.0;
                    }
                }
                logger.info("全国出租率（环比）：{}", ringDiffNum);


                resp.setRate(BigDecimal.valueOf(num));
                resp.setRateYoy(BigDecimal.valueOf(yoyRate));
                resp.setRateQoq(BigDecimal.valueOf(qoqRate));

                // 计算出租率分析
                List<MultiRentAnalyseRespItem>  calculateDailyAvgRentalRateItemList = calculateDailyAvgRentalRateByCommercialType(dataList, req.getStartDate(), req.getEndDate());
                resp.setNationalTargetList(calculateDailyAvgRentalRateItemList);

                List<MultiRentAnalyseRespItem>  calculateCityAvgRentalRateItemList = calculateCityAvgRentalRateByCommercialType(dataList, req.getStartDate(), req.getEndDate());
                resp.setNationalCityTargetList(calculateCityAvgRentalRateItemList);

            }else if(2 == target){// 撤场面积
                // 1. 获取当前周期撤场数据
                List<ReportExitStatisListVo> dataList = reportExitStatisProvider.getAllData(queryDto);

                MultiRentAnalyseResp multiRentAnalyseResp = calculateExitAreaRate(dataList,queryDto,req);
                resp.setRate(multiRentAnalyseResp.getRate());
                resp.setRateYoy(multiRentAnalyseResp.getRateYoy());
                resp.setRateQoq(multiRentAnalyseResp.getRateQoq());
                resp.setNationalTargetList(multiRentAnalyseResp.getNationalTargetList());
                resp.setNationalCityTargetList(multiRentAnalyseResp.getNationalCityTargetList());
            }
        }else{ // 城市
            List<String> cities = req.getCity();
            // TODO  记得把这个去掉  ， 然后sql那里的城市也去掉
            List<EntProjectListVo> tenantIdList = getTenantIdList(req);
            Map<String, String> tenantNameCityMap = tenantIdList.stream().collect(Collectors.toMap(EntProjectListVo::getName, EntProjectListVo::getFcity));
//            Map<String, String> tenantNameCityMap = new HashMap<>();
//            tenantNameCityMap.put("深圳北站","深圳市");
//            tenantNameCityMap.put("深圳湾公园","深圳市");
            // 2. 筛选目标城市的项目名称
            Set<String> targetTenantNames = tenantNameCityMap.entrySet().stream()
                    .filter(e -> cities.contains(e.getValue()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
            queryDto.setTenantNames(new ArrayList<>(targetTenantNames));

            if(0 == target || 1 == target){// 出租率 、 开业率

                // 3. 计算横坐标为城市的图形数据
                List<ReportOperateDayAnalyseListVo> dataList = (0 == target) ? reportOperateDayProvider.getAllData(queryDto) : reportOperateDayProvider.getOpenAllData(queryDto);
                Map<String, Double> cityAvgRentRateMap = calculateCityAvgRentalRate(dataList, req.getStartDate(), req.getEndDate(), tenantNameCityMap);
                System.out.println(cityAvgRentRateMap);

                MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
                queryDto.setStartDate(pariReq.getStartDate());
                queryDto.setEndDate(pariReq.getEndDate());
                List<ReportOperateDayAnalyseListVo> pariDataList = (0 == target) ? reportOperateDayProvider.getAllData(queryDto) : reportOperateDayProvider.getOpenAllData(queryDto);
                Map<String, Double> cityAvgRentRatePariMap = calculateCityAvgRentalRate(pariDataList, req.getStartDate(), req.getEndDate(), tenantNameCityMap);

                MultiAnalyseReq ringDiffReq = RatioCalculateUtil.getRingDiff(req);
                queryDto.setStartDate(ringDiffReq.getStartDate());
                queryDto.setEndDate(ringDiffReq.getEndDate());
                List<ReportOperateDayAnalyseListVo> ringDiffDataList = (0 == target) ? reportOperateDayProvider.getAllData(queryDto) : reportOperateDayProvider.getOpenAllData(queryDto);
                Map<String, Double> cityAvgRentRateRingDiffMap = calculateCityAvgRentalRate(ringDiffDataList, req.getStartDate(), req.getEndDate(), tenantNameCityMap);

                // 生成MultiRentAnalyseCityRespItem数据
                List<MultiRentAnalyseCityRespItem> cityTargetList = new ArrayList<>();
                for (String city : cityAvgRentRateMap.keySet()) {
                    MultiRentAnalyseCityRespItem item = new MultiRentAnalyseCityRespItem();
                    item.setName(city);
                    item.setValue(cityAvgRentRateMap.get(city).toString());

                    Double currentPariValue = cityAvgRentRatePariMap.get(city);
                    if (currentPariValue != null && currentPariValue != 0) {
                        item.setPariPassuPercent((cityAvgRentRateMap.get(city) - currentPariValue) / currentPariValue * 100);
                        item.setPariPassuValue(currentPariValue);
                    }

                    Double currentRingValue = cityAvgRentRateRingDiffMap.get(city);
                    if (currentRingValue != null && currentRingValue != 0) {
                        item.setRingThanPercent((cityAvgRentRateMap.get(city) - currentRingValue) / currentRingValue * 100);
                        item.setRingThanValue(currentRingValue);
                    }

                    cityTargetList.add(item);
                }
                resp.setCityTargetList(cityTargetList);



                // 由此开始  老实了  把基础数据查出来后再用代码过滤太复杂了  改用sql查
                // 计算横坐标为日期的图形数据
                queryDto.setStartDate(req.getStartDate());
                queryDto.setEndDate(req.getEndDate());
                List<Map<String, Double>> cityAllDataList = (0 == target) ? reportOperateDayProvider.getCityAllData(queryDto) : reportOperateDayProvider.getCityOpenAllData(queryDto);


                queryDto.setStartDate(pariReq.getStartDate());
                queryDto.setEndDate(pariReq.getEndDate());
                List<Map<String, Double>> cityAllPariDataList = (0 == target) ? reportOperateDayProvider.getCityAllData(queryDto) : reportOperateDayProvider.getCityOpenAllData(queryDto);

                queryDto.setStartDate(ringDiffReq.getStartDate());
                queryDto.setEndDate(ringDiffReq.getEndDate());
                List<Map<String, Double>> cityAllRingDiffDataList = (0 == target) ? reportOperateDayProvider.getCityAllData(queryDto) : reportOperateDayProvider.getCityOpenAllData(queryDto);

                List<MultiRentAnalyseCityRespItem> cityDateTargetList = calculateAndMergeDateRates(cityAllDataList, cityAllPariDataList, cityAllRingDiffDataList, req);
                resp.setCityDateTargetList(cityDateTargetList);

            }else if(2 == target){// 撤场面积

                // 计算横坐标为城市的图形数据
                queryDto.setStartDate(req.getStartDate());
                queryDto.setEndDate(req.getEndDate());
                List<Map<String, Double>> cityAllDataList = reportExitStatisProvider.getCityAllData(queryDto) ;
                // 在这儿查出横坐标为日期的图形数据
                List<Map<String, Double>> cityDateAllDataList = reportExitStatisProvider.getCityDateAllData(queryDto) ;

                MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
                queryDto.setStartDate(pariReq.getStartDate());
                queryDto.setEndDate(pariReq.getEndDate());
                List<Map<String, Double>> cityAllPariDataList = reportExitStatisProvider.getCityAllData(queryDto);
                // 在这儿查出横坐标为日期的图形数据
                List<Map<String, Double>> cityDateAllPariDataList = reportExitStatisProvider.getCityDateAllData(queryDto);

                MultiAnalyseReq ringDiffReq = RatioCalculateUtil.getRingDiff(req);
                queryDto.setStartDate(ringDiffReq.getStartDate());
                queryDto.setEndDate(ringDiffReq.getEndDate());
                List<Map<String, Double>> cityAllRingDiffDataList = reportExitStatisProvider.getCityAllData(queryDto);
                // 在这儿查出横坐标为日期的图形数据
                List<Map<String, Double>> cityDateAllRingDiffDataList = reportExitStatisProvider.getCityDateAllData(queryDto);

                List<MultiRentAnalyseCityRespItem> cityTargetList = calculateAndMergeComparisonRates(cityAllDataList, cityAllPariDataList, cityAllRingDiffDataList);
                resp.setCityTargetList(cityTargetList);

                // 计算横坐标为日期的图形数据
                List<MultiRentAnalyseCityRespItem> cityDateTargetList = calculateAndMergeDateRates(cityDateAllDataList, cityDateAllPariDataList, cityDateAllRingDiffDataList, req);
                resp.setCityDateTargetList(cityDateTargetList);


            }

        }



        return resp;
    }

    @Override
    public MultiAnalyseResp salesDetail(MultiAnalyseReq req) {
        MultiAnalyseResp resp = new MultiAnalyseResp();
        List<EntProjectListVo> tenantIdList = getTenantIdList(req);
        List<CommercialCategoryListVo> commercialCategoryList = getCommercialCategoryList(req);

        //当前数据
        List<MultiAnalyseRespDetail> currentItems = new ArrayList<>();
        List<MultiAnalyseRespDetail> ringDiffItems = new ArrayList<>();
        List<MultiAnalyseRespDetail> pariItems = new ArrayList<>();

        // 创建计数器
        CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(3);
        ThreadUtil.execAsync(() -> {
            try {
                ReportMultiAnalyseListDto queryDto = getQueryDto(req,tenantIdList);
                List<ReportMultiAnalyseVo> saleDetailByGatherType = reportMasterSaleDataProvider.getSaleDetailByGatherType(queryDto);
                List<ReportMultiAnalyseVo> currentBaseList = getSaleAnalyseBaseList(req, tenantIdList);
                salesDetailsByCommercialType(saleDetailByGatherType,req, currentBaseList, currentItems, tenantIdList, commercialCategoryList);
            } catch (Exception e) {
                logger.error("获取当前数据范围多维数据各指标数据异常==》", e);
            } finally {
                countDownLatch.countDown();
            }
        });
        ThreadUtil.execAsync(() -> {
            try {
                //环比数据
                MultiAnalyseReq ringDiffReq = RatioCalculateUtil.getRingDiff(req);
                ReportMultiAnalyseListDto queryDto = getQueryDto(ringDiffReq,tenantIdList);
                List<ReportMultiAnalyseVo> reportMultiAnalyseVos = selectResultList(queryDto);
                convertResultList(reportMultiAnalyseVos,ringDiffItems);
            } catch (Exception e) {
                logger.error("获取环比时间范围多维数据各指标数据异常==》", e);
            } finally {
                countDownLatch.countDown();
            }
        });
        ThreadUtil.execAsync(() -> {
            try {
                //同比数据
                MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
                ReportMultiAnalyseListDto queryDto = getQueryDto(pariReq,tenantIdList);
                List<ReportMultiAnalyseVo> reportMultiAnalyseVos = selectResultList(queryDto);
                convertResultList(reportMultiAnalyseVos,pariItems);
            } catch (Exception e) {
                logger.error("获取同比时间范围多维数据各指标数据异常==》", e);
            } finally {
                countDownLatch.countDown();
            }
        });
        try {
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("计数器执行异常", e);
        }
        //计算同环比
        getRingAndPariData(req, currentItems, ringDiffItems, pariItems);
        resp.setSaleDetailList(currentItems);

        System.out.println(JSONObject.toJSONString(resp));
        return resp;
    }


    @Override
    public MultiRentAnalyseResp rentDetail(MultiAnalyseReq req) {
        MultiRentAnalyseResp resp = new MultiRentAnalyseResp();
        // 转换为Detail类型
        List<MultiRentAnalyseRespDetail> detailList = getRentDetail(req);
        resp.setRentDetailList(detailList);
        return resp;
    }


    public List<MultiRentAnalyseRespDetail> getRentDetail(MultiAnalyseReq req) {
        Integer target = req.getTarget();
        Integer searchType = req.getSearchType();
        // 组装查询Dto
        ReportOperateDayAnalyseDto queryDto = new ReportOperateDayAnalyseDto();
        queryDto.setStartDate(req.getStartDate());
        queryDto.setEndDate(req.getEndDate());
        queryDto.setSearchType(req.getSearchType());
        queryDto.setGatherType(req.getGatherType());
        List<MultiRentAnalyseRespDetail> detailList = new ArrayList<>();


        if(1 == searchType){
            List<String> cities = req.getCity();
            // TODO  记得把这个去掉  ， 然后sql那里的城市也去掉
            List<EntProjectListVo> tenantIdList = getTenantIdList(req);
            Map<String, String> tenantNameCityMap = tenantIdList.stream().collect(Collectors.toMap(EntProjectListVo::getName, EntProjectListVo::getFcity));
//            Map<String, String> tenantNameCityMap = new HashMap<>();
//            tenantNameCityMap.put("深圳北站","深圳市");
//            tenantNameCityMap.put("深圳湾公园","深圳市");
            // 2. 筛选目标城市的项目名称
            Set<String> targetTenantNames = tenantNameCityMap.entrySet().stream()
                    .filter(e -> cities.contains(e.getValue()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
            queryDto.setTenantNames(new ArrayList<>(targetTenantNames));
        }

        if(0 == target || 1 == target) {// 出租率 、 开业率
            // 主区间数据
            List<ReportOperateDayAnalyseListVo> dataList = (0 == target)? reportOperateDayProvider.getAllData(queryDto): reportOperateDayProvider.getOpenAllData(queryDto);
            // 同比区间
            MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
            queryDto.setStartDate(pariReq.getStartDate());
            queryDto.setEndDate(pariReq.getEndDate());
            List<ReportOperateDayAnalyseListVo> pariDataList = (0 == target)? reportOperateDayProvider.getAllData(queryDto): reportOperateDayProvider.getOpenAllData(queryDto);
            // 环比区间
            MultiAnalyseReq ringDiffReq = RatioCalculateUtil.getRingDiff(req);
            queryDto.setStartDate(ringDiffReq.getStartDate());
            queryDto.setEndDate(ringDiffReq.getEndDate());
            List<ReportOperateDayAnalyseListVo> ringDiffDataList = (0 == target)? reportOperateDayProvider.getAllData(queryDto): reportOperateDayProvider.getOpenAllData(queryDto);
            // 统计
            int gather = req.getGatherType() == null ? 0 : req.getGatherType();
            List<MultiRentAnalyseRespDetail> itemList = calculateCityRentalRateByGather(
                    dataList, pariDataList, ringDiffDataList,
                    req.getStartDate(), req.getEndDate(), gather
            );
            // 转换为Detail类型
            detailList = BeanUtils.copyProperties(itemList, MultiRentAnalyseRespDetail.class);
        }else if(2 == target) {// 撤场面积
            //List<EntProjectListVo> tenantIdList = getTenantIdList(req);
            //Map<String, String> tenantNameCityMap = tenantIdList.stream().collect(Collectors.toMap(EntProjectListVo::getName, EntProjectListVo::getFcity));
            Map<String, String> tenantNameCityMap = new HashMap<>();
            tenantNameCityMap.put("深圳北站","深圳市");
            tenantNameCityMap.put("深圳湾公园","深圳市");
            // 1. 获取当前周期撤场数据
            List<Map<String, String>> cityAllDataList = reportExitStatisProvider.selectExitStatisByGroup(queryDto,req.getGatherType());

            // 同比区间
            MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
            queryDto.setStartDate(pariReq.getStartDate());
            queryDto.setEndDate(pariReq.getEndDate());
            List<Map<String, String>> cityAllPariDataList = reportExitStatisProvider.selectExitStatisByGroup(queryDto,req.getGatherType());
            // 环比区间
            MultiAnalyseReq ringDiffReq = RatioCalculateUtil.getRingDiff(req);
            queryDto.setStartDate(ringDiffReq.getStartDate());
            queryDto.setEndDate(ringDiffReq.getEndDate());
            List<Map<String, String>> cityAllRingDiffDataList = reportExitStatisProvider.selectExitStatisByGroup(queryDto,req.getGatherType());


            List<MultiRentAnalyseRespDetail> itemList = processExitStatisData(cityAllDataList, cityAllPariDataList, cityAllRingDiffDataList, req.getGatherType(), tenantNameCityMap);
            detailList = BeanUtils.copyProperties(itemList, MultiRentAnalyseRespDetail.class);
        }

        return detailList;
    }


    @Override
    public void expRentDetail(MultiAnalyseReq req) {
        Integer target = req.getTarget();
        Integer searchType = req.getSearchType();

        List<MultiRentAnalyseRespDetail> detailList = getRentDetail(req);
        if(0 == target  ) {// 出租率
            try {
                HttpServletResponse response = ApiUtils.getResponse();
                response.setContentType("application/octet-stream");
                String endCodeFileName = URLEncoder.encode("出租率多维数据分析导出.xlsx", "UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
                response.setContentType("application/octet-stream");
                EasyExcel.write(response.getOutputStream(), MultiRentAnalyseDetail.class).sheet("出租率多维数据分析导出").doWrite(detailList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else if(1 == target) {// 开业率
            try {
                HttpServletResponse response = ApiUtils.getResponse();
                response.setContentType("application/octet-stream");
                String endCodeFileName = URLEncoder.encode("开业率多维数据分析导出.xlsx", "UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
                response.setContentType("application/octet-stream");
                EasyExcel.write(response.getOutputStream(), MultiOpenRentAnalyseDetail.class).sheet("开业率多维数据分析导出").doWrite(detailList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else if(2 == target){// 撤场面积
            try {
                HttpServletResponse response = ApiUtils.getResponse();
                response.setContentType("application/octet-stream");
                String endCodeFileName = URLEncoder.encode("撤场面积多维数据分析导出.xlsx", "UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
                response.setContentType("application/octet-stream");
                EasyExcel.write(response.getOutputStream(), MultiExitRentAnalyseDetail.class).sheet("撤场面积多维数据分析导出").doWrite(detailList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }


    /**
     * 根据查询参数 展示城市/项目
     * @param req
     * @param result
     * @param currentBaseList
     * @param tenantIdList
     * @return
     */
    private  List<MultiAnalyseRespItem> roomDistributeList(MultiAnalyseReq req,List<MultiAnalyseRespItem>result,List<ReportMultiAnalyseVo> currentBaseList,List<EntProjectListVo>tenantIdList){
        List<String>getGroupList=null;
        Map<String, Integer> storeIdCountMap=null;
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            getGroupList=tenantIdList.stream().map(EntProjectListVo::getFcity).distinct().collect(Collectors.toList());
            // 根据城市和业态组合去重，再根据 storeid 去重计数
            storeIdCountMap= currentBaseList.stream()
                    .collect(Collectors.groupingBy(
                            vo -> vo.getCity() + "&" + vo.getCommercialTypeName(),
                            Collectors.collectingAndThen(
                                    Collectors.mapping(ReportMultiAnalyseVo::getRoomId, Collectors.toSet()),
                                    set -> set.isEmpty() ? 0 : set.size()
                            )
                    ));
        } else{
            getGroupList=tenantIdList.stream().map(EntProjectListVo::getName).distinct().collect(Collectors.toList());
            storeIdCountMap= currentBaseList.stream()
                    .collect(Collectors.groupingBy(
                            vo -> vo.getTenantName() + "&" + vo.getCommercialTypeName(),
                            Collectors.collectingAndThen(
                                    Collectors.mapping(ReportMultiAnalyseVo::getRoomId, Collectors.toSet()),
                                    set -> set.isEmpty() ? 0 : set.size()
                            )
                    ));
        }
        Integer count =0;
        for (String group : getGroupList) {
            Integer cater = storeIdCountMap.get(group + "&餐饮")==null?0:storeIdCountMap.get(group + "&餐饮");
            Integer retail = storeIdCountMap.get(group + "&零售")==null?0: storeIdCountMap.get(group + "&零售");
            Integer entertainValue = storeIdCountMap.get(group + "&娱乐服务")==null?0:storeIdCountMap.get(group + "&娱乐服务");
            count=count+cater+retail+entertainValue;
        }
        BigDecimal percent=BigDecimal.ZERO;
        for (int i = 0; i < getGroupList.size(); i++) {
            String group=getGroupList.get(i);
            MultiAnalyseRespItem item=new MultiAnalyseRespItem();
            item.setName(group);
            Integer cater = storeIdCountMap.get(group + "&餐饮")==null?0:storeIdCountMap.get(group + "&餐饮");
            Integer retail = storeIdCountMap.get(group + "&零售")==null?0: storeIdCountMap.get(group + "&零售");
            Integer entertainValue = storeIdCountMap.get(group + "&娱乐服务")==null?0:storeIdCountMap.get(group + "&娱乐服务");
            item.setCaterValue(cater);
            item.setRetailValue(retail);
            item.setEntertainValue(entertainValue);
            BigDecimal caterPercent = BigDecimalUtil.multiply(BigDecimalUtil.divide(BigDecimal.valueOf(cater), BigDecimal.valueOf(count), 4), BigDecimal.valueOf(100));
            item.setCaterPercent(caterPercent);
            BigDecimal retailPercent = BigDecimalUtil.multiply(BigDecimalUtil.divide(BigDecimal.valueOf(retail), BigDecimal.valueOf(count), 4), BigDecimal.valueOf(100));
            item.setRetailPercent(retailPercent);
            percent=BigDecimalUtil.add(retailPercent,BigDecimalUtil.add(percent,caterPercent));
            if(i==getGroupList.size()-1){
                item.setEntertainPercent(BigDecimalUtil.subtract(BigDecimal.valueOf(100),percent));
            }else{
                BigDecimal entertainPercent = BigDecimalUtil.multiply(BigDecimalUtil.divide(BigDecimal.valueOf(entertainValue), BigDecimal.valueOf(count), 4), BigDecimal.valueOf(100));
                percent=BigDecimalUtil.add(percent,entertainPercent);
                item.setEntertainPercent(entertainPercent);
            }

            result.add(item);
        }
        return result;
    }
    /**
     * 获取查询条件的项目列表
     * @param req
     * @return
     */
    private List<EntProjectListVo> getTenantIdList(MultiAnalyseReq req){
        EntProjectListDto entProjectDto = new EntProjectListDto();
        List<EntProjectListVo> projectListVos= new ArrayList<>();
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            //查询全国下的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            if (CollectionUtils.isEmpty(req.getCity())) throw new ServiceException("查询城市维度时，城市无数据");
            entProjectDto.setCityList(req.getCity());
            //查询城市下面的项目数据
            projectListVos = entProjectProvider.list(entProjectDto);
        } else {
            if (CollectionUtils.isEmpty(req.getTenantId())) throw new ServiceException("查询项目维度时，项目无数据");
            entProjectDto.setIdList(req.getTenantId());
            projectListVos = entProjectProvider.list(entProjectDto);
        }
        return projectListVos;
    }

    /**
     * 查询业态品类数据
     * @param req 不传就查询业态数据
     * @return
     */
    private  List<CommercialCategoryListVo>  getCommercialCategoryList(MultiAnalyseReq req){
        if(req==null){
            return getCommercialCategoryListVo(0);
        }
        Integer gatherType = req.getGatherType();
        List<CommercialCategoryListVo> commercialCategoryListVo=null;
        if(gatherType==0){
            //全部业态
            commercialCategoryListVo = getCommercialCategoryListVo(0);
        } else if (gatherType==1) {
            commercialCategoryListVo = getCommercialCategoryListVo(1);
        }else{
            commercialCategoryListVo = getCommercialCategoryListVo(2);
        }
        return commercialCategoryListVo;
    }

    /**
     * 根据查询条件获取销售基础数据。
     * @param req
     * @return
     */
    private List<ReportMultiAnalyseVo> getSaleAnalyseBaseList(MultiAnalyseReq req, List<EntProjectListVo> projectListVos) {
        ReportMultiAnalyseListDto queryDto = getQueryDto(req, projectListVos);
        List<ReportMultiAnalyseVo> analyseBaseList = reportOperateDayProvider.multiAnalyseList(queryDto);

//        //组装获取指定日期的销售数据
//        ReportMasterSaleDataListDto dto = new ReportMasterSaleDataListDto();
//        dto.setTenantIdList(queryDto.getTenantIdList());
//        dto.setStartDate(req.getStartDate());
//        dto.setEndDate(req.getEndDate());
//        List<ReportMasterSaleDataListVo> saleList = reportMasterSaleDataProvider.list(dto);
//        //通过项目ID+objCode判断唯一数据。
//        Map<String, ReportMasterSaleDataListVo> saleMapByObjCode = saleList.stream()
//                .collect(Collectors.toMap(vo -> vo.getTenantId() + "&" + vo.getObjCode() + "&" + vo.getSaleTime(), t -> t, (existingValue, newValue) -> existingValue));

        //项目城市数据。
        Map<Long, String> tenantCityMap = projectListVos.stream().collect(Collectors.toMap(EntProjectListVo::getId, EntProjectListVo::getFcity));

        for (ReportMultiAnalyseVo multiAnalyseVo : analyseBaseList) {
            multiAnalyseVo.setYearMonthDay(multiAnalyseVo.getCurrentDate().toString());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            multiAnalyseVo.setYearMonth(multiAnalyseVo.getCurrentDate().format(formatter));

            //String key= multiAnalyseVo.getTenantId() + "&" + multiAnalyseVo.getFCode() + "&" + multiAnalyseVo.getCurrentDate();

            multiAnalyseVo.setCity(tenantCityMap.get(multiAnalyseVo.getTenantId()));
            multiAnalyseVo.setSalePerUnitArea(BigDecimalUtil.divide(multiAnalyseVo.getTotalAmount(), BigDecimal.valueOf(multiAnalyseVo.getRoomArea())));
            //ReportMasterSaleDataListVo saleVo = saleMapByObjCode.get(key);
            /*if(saleVo==null){
                multiAnalyseVo.setTotalAmount(BigDecimal.ZERO);
                multiAnalyseVo.setTotalOrder(0);
                multiAnalyseVo.setStoreAmount(BigDecimal.ZERO);
                multiAnalyseVo.setStoreOrder(0);
                multiAnalyseVo.setTakeawayAmount(BigDecimal.ZERO);
                multiAnalyseVo.setTakeawayOrder(0);
                multiAnalyseVo.setSalePerUnitArea(BigDecimal.ZERO);
            }else {
                multiAnalyseVo.setTotalAmount(saleVo.getTotalAmount());
                multiAnalyseVo.setTotalOrder(saleVo.getTotalOrder());
                multiAnalyseVo.setStoreAmount(saleVo.getStoreAmount());
                multiAnalyseVo.setStoreOrder(saleVo.getStoreOrder());
                multiAnalyseVo.setTakeawayAmount(saleVo.getTakeawayAmount());
                multiAnalyseVo.setTakeawayOrder(saleVo.getTakeawayOrder());
            }*/

        }
        return analyseBaseList;
    }
    private ReportMultiAnalyseListDto getQueryDto(MultiAnalyseReq req, List<EntProjectListVo> projectListVos) {
        ReportMultiAnalyseListDto queryDto = new ReportMultiAnalyseListDto();
        queryDto.setStartDate(req.getStartDate());
        queryDto.setEndDate(req.getEndDate());
        queryDto.setSearchType(req.getSearchType());
        queryDto.setGatherType(req.getGatherType());
        //项目城市数据。
        Map<Long, String> tenantCityMap = projectListVos.stream().collect(Collectors.toMap(EntProjectListVo::getId, EntProjectListVo::getFcity));
        //查询城市下所有项目
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            //查询全国下的项目数据
            queryDto.setTenantIdList(null);
        } else if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            if (CollectionUtils.isEmpty(req.getCity())) throw new ServiceException("查询城市维度时，城市无数据");
            //查询城市下面的项目数据
            queryDto.setTenantIdList(projectListVos.stream().map(EntProjectListVo::getId).toList());
        } else {
            if (CollectionUtils.isEmpty(req.getTenantId())) throw new ServiceException("查询项目维度时，项目无数据");
            queryDto.setTenantIdList(req.getTenantId());
        }
        return queryDto;
    }


    /**
     * 业态销售额明细
     * @param req gatherType 0-按业态汇总 1-按一级品类汇总 2-按二级品类汇总
     * @param currentList 当前数据
     * @return
     */

    private List<MultiAnalyseRespDetail> salesDetailsByCommercialType(List<ReportMultiAnalyseVo>calcCurrentList,MultiAnalyseReq req, List<ReportMultiAnalyseVo>currentList,List<MultiAnalyseRespDetail>resultList,List<EntProjectListVo> projectAllList, List<CommercialCategoryListVo> commercialCategoryListVo ){
        Integer searchType=req.getSearchType();
        //组装业态数据
        List<String>getGroupList=null;

        if (searchType == SerchTypeEnum.COUNTRY.value()) {
            //全国展示城市数据
            getGroupList=projectAllList.stream().map(EntProjectListVo::getFcity).distinct().collect(Collectors.toList());
            //根据城市下的汇总方式分组。
            getCountryAnalyseList(calcCurrentList,req,getGroupList, projectAllList, resultList, currentList, commercialCategoryListVo);
        } else if (searchType == SerchTypeEnum.CITY.value()) {
            getGroupList=projectAllList.stream().map(EntProjectListVo::getName).distinct().collect(Collectors.toList());
            getCityAnalyseList(calcCurrentList,req,getGroupList, projectAllList, resultList, currentList, commercialCategoryListVo);
        } else {
            getTenantAnalyseList(calcCurrentList,req,resultList, currentList);
        }
        return resultList;
    }

    /**
     *
     * @param slevel 0 业态 1 一级品类 2 二级品类
     * @return
     */
    private List<CommercialCategoryListVo>getCommercialCategoryListVo(Integer slevel){
        CommercialCategoryListDto dto = new CommercialCategoryListDto();
        dto.setSlevel(slevel);
        return commercialCategoryProvider.list(dto);
    }

    /**
     * 根据全国 获取城市下的各维度汇总数据
     * @param req 汇总类型
     * @param getGroupList 分组列表
     * @param projectAllList 项目列表
     * @param resultList 结果集
     * @param currentList 当前数据
     * @param commercialCategoryListVo 汇总类型对应的分类数据
     */

    private void getCountryAnalyseList(List<ReportMultiAnalyseVo>calcCurrentList,MultiAnalyseReq req,List<String>getGroupList, List<EntProjectListVo> projectAllList,List<MultiAnalyseRespDetail>resultList,List<ReportMultiAnalyseVo>currentList,List<CommercialCategoryListVo> commercialCategoryListVo){
        Integer gatherType=req.getGatherType();
        Map<String, ReportMultiAnalyseVo> saleTotalMap = calcCurrentList.stream().collect(Collectors.toMap((vo) -> {
            if(gatherType==0){
                return vo.getCity()+"&"+vo.getCommercialTypeCode();
            }else if(gatherType==1){
                return vo.getCity()+"&"+vo.getCategoryId();
            }else{
                return vo.getCity()+"&"+vo.getCommercialTwoId();
            }
        }, t -> t, (existingValue, newValue) -> existingValue));
        for (String group : getGroupList) {
            List<Long> projectList = projectAllList.stream().filter(vo -> vo.getFcity().equals(group)).map(EntProjectListVo::getId).collect(Collectors.toList());
            List<ReportMultiAnalyseVo> currentGroupList = currentList.stream().filter(v -> projectList.contains(v.getTenantId())).collect(Collectors.toList());
            Map<Long, ReportMultiAnalyseVo> cityReportMap =currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .collect(Collectors.groupingBy(
                            vo -> {
                                if(gatherType==0){
                                    return vo.getCommercialTypeCode();
                                }else if(gatherType==1){
                                    return vo.getCategoryId();
                                }else{
                                    return vo.getCommercialTwoId();
                                }
                            },
                            Collectors.reducing(
                                    new ReportMultiAnalyseVo(),
                                    (vo1, vo2) -> {
                                        ReportMultiAnalyseVo result = new ReportMultiAnalyseVo();
                                        result.setTotalAmount(BigDecimalUtil.add(vo1.getTotalAmount(), vo2.getTotalAmount()));
                                        result.setTakeawayAmount(BigDecimalUtil.add(vo1.getTakeawayAmount(), vo2.getTakeawayAmount()));
                                        result.setStoreAmount(BigDecimalUtil.add(vo1.getStoreAmount(), vo2.getStoreAmount()));
                                        result.setSalePerUnitArea(BigDecimalUtil.add(vo1.getSalePerUnitArea(), vo2.getSalePerUnitArea()));
                                        return result;
                                    }
                            )
                    ));
            //计算某分类的所有天数和
            Set<String> dateByGatherType = currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .map(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() + "&" + vo.getCurrentDate();
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() + "&" + vo.getCurrentDate();
                        } else {
                            return vo.getCommercialTwoId() + "&" + vo.getCurrentDate();
                        }
                    }).collect(Collectors.toSet());
            // 计算每日某分类的铺位数
            Map<String, Long> roomNumByGatherType = currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .collect(Collectors.groupingBy(
                            vo -> {
                                if (gatherType == 0) {
                                    return vo.getCommercialTypeCode() + "&" + vo.getCurrentDate();
                                } else if (gatherType == 1) {
                                    return vo.getCategoryId() + "&" + vo.getCurrentDate();
                                } else {
                                    return vo.getCommercialTwoId() + "&" + vo.getCurrentDate();
                                }
                            }, Collectors.counting()
                    ));
            // 计算每日某分类的销售总额
            Map<String, BigDecimal> totalAmountByGatherType = currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .collect(Collectors.groupingBy(
                            vo -> {
                                if (gatherType == 0) {
                                    return vo.getCommercialTypeCode() + "&" + vo.getCurrentDate();
                                } else if (gatherType == 1) {
                                    return vo.getCategoryId() + "&" + vo.getCurrentDate();
                                } else {
                                    return vo.getCommercialTwoId() + "&" + vo.getCurrentDate();
                                }
                            }, Collectors.reducing(BigDecimal.ZERO, ReportMultiAnalyseVo::getTotalAmount, BigDecimal::add)
                    ));
            // 计算每日某分类的总面积
            Map<String, Double> totalAreaByGatherType = currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .collect(Collectors.groupingBy(
                            vo -> {
                                if (gatherType == 0) {
                                    return vo.getCommercialTypeCode() + "&" + vo.getCurrentDate();
                                } else if (gatherType == 1) {
                                    return vo.getCategoryId() + "&" + vo.getCurrentDate();
                                } else {
                                    return vo.getCommercialTwoId() + "&" + vo.getCurrentDate();
                                }
                            }, Collectors.summingDouble(ReportMultiAnalyseVo::getRoomArea)
                    ));
            for (CommercialCategoryListVo categoryListVo : commercialCategoryListVo) {
                MultiAnalyseRespDetail result = new MultiAnalyseRespDetail();
                resultList.add(result);
                result.setCity(group);
                String parentFname = categoryListVo.getParentFname();
                String[] split = parentFname.split("/");
                if(split.length==3){
                    result.setCommercialTypeName(split[0]);
                    result.setCategoryName(split[1]);
                    result.setSecondCategoryName(split[2]);
                }
                if(split.length==2){
                    result.setCommercialTypeName(split[0]);
                    result.setCategoryName(split[1]);
                }
                if(split.length==1){
                    result.setCommercialTypeName(split[0]);
                }
                Long key=categoryListVo.getId();
                ReportMultiAnalyseVo reportMultiAnalyseVo = cityReportMap.get(key);
                if (reportMultiAnalyseVo != null) {


                    BigDecimal salePerUnitArealAvg = BigDecimal.ZERO;
                    BigDecimal saleTotalAvg = BigDecimal.ZERO;
                    int dayNum = 0;
                    for (String dateKey : dateByGatherType) {
                        //计算日均销售额 铺位数取同业态下的数据。
                        String[] dateSplit = dateKey.split("&");
                        if (dateSplit.length > 1 && StringUtils.equals(String.valueOf(categoryListVo.getId()), dateSplit[0])) {
                            dateKey = categoryListVo.getId() + "&" + dateSplit[1];
                            BigDecimal totalAmount = totalAmountByGatherType.get(dateKey);
                            Long roomNum = roomNumByGatherType.get(dateKey);
                            saleTotalAvg = BigDecimalUtil.add(saleTotalAvg, BigDecimalUtil.divide(totalAmount, BigDecimal.valueOf(roomNum)));
                            Double totalArea = totalAreaByGatherType.get(dateKey);
                            salePerUnitArealAvg = BigDecimalUtil.add(salePerUnitArealAvg, BigDecimalUtil.divide(totalAmount, BigDecimal.valueOf(totalArea)));
                            dayNum++;
                        }
                    }
                    //计算出日均销售坪效占比
                    result.setSaleSquareAvg(BigDecimalUtil.divide(salePerUnitArealAvg, BigDecimal.valueOf(dayNum)));
                    //计算每铺日均销售额
                    result.setSaleTotalAvgRoom(BigDecimalUtil.divide(saleTotalAvg, BigDecimal.valueOf(dayNum)));
                }
                ReportMultiAnalyseVo saleTotalVo = saleTotalMap.get(group+"&"+key);
                if(saleTotalVo!=null){
                    //计算出线下销售额占比
                    BigDecimal percent = BigDecimalUtil.multiply(BigDecimalUtil.divide(saleTotalVo.getStoreAmount(), saleTotalVo.getTotalAmount()),BigDecimal.valueOf(100));
                    result.setTotalAmount(saleTotalVo.getTotalAmount());
                    result.setTakeawayAmount(saleTotalVo.getTakeawayAmount());
                    result.setStoreAmount(saleTotalVo.getStoreAmount());
                    result.setStoreAmountPercent(percent);
                    result.setTakeawayAmountPercent(BigDecimalUtil.subtract(BigDecimal.valueOf(100),percent));
                }
            }
        }
    }
    /**
     * 计算同环比数据并赋值
     */
    private void getRingAndPariData(MultiAnalyseReq req,List<MultiAnalyseRespDetail> currentItems,List<MultiAnalyseRespDetail> ringDiffItems, List<MultiAnalyseRespDetail> pariItems ){
        Map<String, MultiAnalyseRespDetail> ringDiffMap =null;
        Map<String, MultiAnalyseRespDetail> pariMap =null;
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            if(req.getGatherType() == 0){
                ringDiffMap=ringDiffItems.stream().collect(Collectors.toMap((t) -> t.getCity() + "&" + t.getCommercialTypeName(), (t) -> t, (existingValue, newValue) -> existingValue));
                pariMap = pariItems.stream().collect(Collectors.toMap((t) -> t.getCity() + "&" + t.getCommercialTypeName(), (t) -> t, (existingValue, newValue) -> existingValue));
            }
            else if(req.getGatherType() == 1){
                ringDiffMap = ringDiffItems.stream().collect(Collectors.toMap((t) -> t.getCity() + "&" + t.getCategoryName(), (t) -> t, (existingValue, newValue) -> existingValue));
                pariMap = pariItems.stream().collect(Collectors.toMap((t) -> t.getCity() + "&" + t.getCategoryName(), (t) -> t, (existingValue, newValue) -> existingValue));
            }else{
                ringDiffMap = ringDiffItems.stream().collect(Collectors.toMap((t) -> t.getCity() + "&" + t.getSecondCategoryName(), (t) -> t, (existingValue, newValue) -> existingValue));
                pariMap = pariItems.stream().collect(Collectors.toMap((t) -> t.getCity() + "&" + t.getSecondCategoryName(), (t) -> t, (existingValue, newValue) -> existingValue));
            }

        } else if (req.getSearchType() == SerchTypeEnum.CITY.value()) {
            if(req.getGatherType() == 0){
                ringDiffMap=ringDiffItems.stream().collect(Collectors.toMap((t) -> t.getTenantName() + "&" + t.getCommercialTypeName(), (t) -> t, (existingValue, newValue) -> existingValue));
                pariMap = pariItems.stream().collect(Collectors.toMap((t) -> t.getTenantName() + "&" + t.getCommercialTypeName(), (t) -> t, (existingValue, newValue) -> existingValue));
            }
            else if(req.getGatherType() == 1){
                ringDiffMap = ringDiffItems.stream().collect(Collectors.toMap((t) -> t.getTenantName() + "&" + t.getCategoryName(), (t) -> t, (existingValue, newValue) -> existingValue));
                pariMap = pariItems.stream().collect(Collectors.toMap((t) -> t.getTenantName() + "&" + t.getCategoryName(), (t) -> t, (existingValue, newValue) -> existingValue));
            }else{
                ringDiffMap = ringDiffItems.stream().collect(Collectors.toMap((t) -> t.getTenantName() + "&" + t.getSecondCategoryName(), (t) -> t, (existingValue, newValue) -> existingValue));
                pariMap = pariItems.stream().collect(Collectors.toMap((t) -> t.getTenantName() + "&" + t.getSecondCategoryName(), (t) -> t, (existingValue, newValue) -> existingValue));
            }
        } else {
            ringDiffMap=ringDiffItems.stream().collect(Collectors.toMap((t) -> t.getTenantName()+"&"+t.getStoreId(), (t) -> t, (existingValue, newValue) -> existingValue));
            pariMap = pariItems.stream().collect(Collectors.toMap((t) -> t.getTenantName()+"&"+t.getStoreId(), (t) -> t, (existingValue, newValue) -> existingValue));
        }
        for (MultiAnalyseRespDetail currentItem : currentItems) {
            String key="";
            String prefix="";
            if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
                prefix=currentItem.getCity();
            }else if (req.getSearchType() == SerchTypeEnum.CITY.value()){
                prefix=currentItem.getTenantName();
            }else{
                key=currentItem.getTenantName()+"&"+currentItem.getStoreId();
            }
            if(req.getSearchType() != SerchTypeEnum.TENANT.value()){
                if(req.getGatherType() == 0){
                    key=prefix+"&"+currentItem.getCommercialTypeName();
                }else if (req.getGatherType() == 1){
                    key=prefix+"&"+currentItem.getCategoryName();
                }else{
                    key=prefix+"&"+currentItem.getSecondCategoryName();
                }
            }
            MultiAnalyseRespDetail ringDiffDetail = ringDiffMap.get(key);
            if(ringDiffDetail==null){
                ringDiffDetail=new MultiAnalyseRespDetail();
                ringDiffDetail.setTotalAmount(BigDecimal.ZERO);
                ringDiffDetail.setTakeawayAmount(BigDecimal.ZERO);
                ringDiffDetail.setStoreAmount(BigDecimal.ZERO);
            }
            MultiAnalyseRespDetail pariDetail = pariMap.get(key);
            if(pariDetail==null){
                pariDetail=new MultiAnalyseRespDetail();
                pariDetail.setTotalAmount(BigDecimal.ZERO);
                pariDetail.setTakeawayAmount(BigDecimal.ZERO);
                pariDetail.setStoreAmount(BigDecimal.ZERO);
            }
            currentItem.setTotalAmountRingThan(RatioCalculateUtil.calcPariAndRingDiff(currentItem.getTotalAmount(),ringDiffDetail.getTotalAmount()));
            currentItem.setTotalAmountPariPassu(RatioCalculateUtil.calcPariAndRingDiff(currentItem.getTotalAmount(),pariDetail.getTotalAmount()));

            currentItem.setTakeawayAmountRingThan(RatioCalculateUtil.calcPariAndRingDiff(currentItem.getTakeawayAmount(),ringDiffDetail.getTakeawayAmount()));
            currentItem.setTakeawayAmountPariPassu(RatioCalculateUtil.calcPariAndRingDiff(currentItem.getTakeawayAmount(),pariDetail.getTakeawayAmount()));

            currentItem.setStoreAmountRingThan(RatioCalculateUtil.calcPariAndRingDiff(currentItem.getStoreAmount(),ringDiffDetail.getStoreAmount()));
            currentItem.setStoreAmountPariPassu(RatioCalculateUtil.calcPariAndRingDiff(currentItem.getStoreAmount(),pariDetail.getStoreAmount()));

        }
    }

    private void getCityAnalyseList(List<ReportMultiAnalyseVo>calcCurrentList,MultiAnalyseReq req,List<String>getGroupList, List<EntProjectListVo> projectAllList,List<MultiAnalyseRespDetail>resultList,List<ReportMultiAnalyseVo>currentList,List<CommercialCategoryListVo> commercialCategoryListVo){
        Integer gatherType=req.getGatherType();
        Map<String, ReportMultiAnalyseVo> saleTotalMap = calcCurrentList.stream().collect(Collectors.toMap((vo) -> {
            if(gatherType==0){
                return vo.getTenantName()+"&"+vo.getCommercialTypeCode();
            }else if(gatherType==1){
                return vo.getTenantName()+"&"+vo.getCategoryId();
            }else{
                return vo.getTenantName()+"&"+vo.getCommercialTwoId();
            }
        }, t -> t, (existingValue, newValue) -> existingValue));
        for (String group : getGroupList) {
            List<Long> projectList = projectAllList.stream().filter(vo -> vo.getName().equals(group)).map(EntProjectListVo::getId).collect(Collectors.toList());
            List<ReportMultiAnalyseVo> currentGroupList = currentList.stream().filter(v -> projectList.contains(v.getTenantId())).collect(Collectors.toList());
            Map<Long, ReportMultiAnalyseVo> cityReportMap =currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .collect(Collectors.groupingBy(
                            vo -> {
                                if(gatherType==0){
                                    return vo.getCommercialTypeCode();
                                }else if(gatherType==1){
                                    return vo.getCategoryId();
                                }else{
                                    return vo.getCommercialTwoId();
                                }
                            },
                            Collectors.reducing(
                                    new ReportMultiAnalyseVo(),
                                    (vo1, vo2) -> {
                                        ReportMultiAnalyseVo result = new ReportMultiAnalyseVo();
                                        result.setTotalAmount(BigDecimalUtil.add(vo1.getTotalAmount(), vo2.getTotalAmount()));
                                        result.setTakeawayAmount(BigDecimalUtil.add(vo1.getTakeawayAmount(), vo2.getTakeawayAmount()));
                                        result.setStoreAmount(BigDecimalUtil.add(vo1.getStoreAmount(), vo2.getStoreAmount()));
                                        result.setSalePerUnitArea(BigDecimalUtil.add(vo1.getSalePerUnitArea(), vo2.getSalePerUnitArea()));
                                        result.setNumberOfDays(vo1.getNumberOfDays()+1);
                                        return result;
                                    }
                            )
                    ));
            //计算某分类的所有天数和
            Set<String> dateByGatherType = currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .map(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() + "&" + vo.getCurrentDate();
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() + "&" + vo.getCurrentDate();
                        } else {
                            return vo.getCommercialTwoId() + "&" + vo.getCurrentDate();
                        }
                    }).collect(Collectors.toSet());
            // 计算每日某分类的铺位数
            Map<String, Long> roomNumByGatherType = currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .collect(Collectors.groupingBy(
                            vo -> {
                                if (gatherType == 0) {
                                    return vo.getCommercialTypeCode() + "&" + vo.getCurrentDate();
                                } else if (gatherType == 1) {
                                    return vo.getCategoryId() + "&" + vo.getCurrentDate();
                                } else {
                                    return vo.getCommercialTwoId() + "&" + vo.getCurrentDate();
                                }
                            }, Collectors.counting()
                    ));
            // 计算每日某分类的销售总额
            Map<String, BigDecimal> totalAmountByGatherType = currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .collect(Collectors.groupingBy(
                            vo -> {
                                if (gatherType == 0) {
                                    return vo.getCommercialTypeCode() + "&" + vo.getCurrentDate();
                                } else if (gatherType == 1) {
                                    return vo.getCategoryId() + "&" + vo.getCurrentDate();
                                } else {
                                    return vo.getCommercialTwoId() + "&" + vo.getCurrentDate();
                                }
                            }, Collectors.reducing(BigDecimal.ZERO, ReportMultiAnalyseVo::getTotalAmount, BigDecimal::add)
                    ));
            // 计算每日某分类的总面积
            Map<String, Double> totalAreaByGatherType = currentGroupList.stream().filter(vo -> {
                        if (gatherType == 0) {
                            return vo.getCommercialTypeCode() != null;
                        } else if (gatherType == 1) {
                            return vo.getCategoryId() != null;
                        } else {
                            return vo.getCommercialTwoId() != null;
                        }
                    })
                    .collect(Collectors.groupingBy(
                            vo -> {
                                if (gatherType == 0) {
                                    return vo.getCommercialTypeCode() + "&" + vo.getCurrentDate();
                                } else if (gatherType == 1) {
                                    return vo.getCategoryId() + "&" + vo.getCurrentDate();
                                } else {
                                    return vo.getCommercialTwoId() + "&" + vo.getCurrentDate();
                                }
                            }, Collectors.summingDouble(ReportMultiAnalyseVo::getRoomArea)
                    ));
            for (CommercialCategoryListVo categoryListVo : commercialCategoryListVo) {
                MultiAnalyseRespDetail result = new MultiAnalyseRespDetail();
                resultList.add(result);
                result.setTenantName(group);
                String parentFname = categoryListVo.getParentFname();
                String[] split = parentFname.split("/");
                if(split.length==3){
                    result.setCommercialTypeName(split[0]);
                    result.setCategoryName(split[1]);
                    result.setSecondCategoryName(split[2]);
                }
                if(split.length==2){
                    result.setCommercialTypeName(split[0]);
                    result.setCategoryName(split[1]);
                }
                if(split.length==1){
                    result.setCommercialTypeName(split[0]);
                }
                Long key=categoryListVo.getId();
                ReportMultiAnalyseVo reportMultiAnalyseVo = cityReportMap.get(key);
                if (reportMultiAnalyseVo != null) {
                    //计算出日均销售坪效占比
                    result.setSaleSquareAvg(BigDecimalUtil.divide(reportMultiAnalyseVo.getSalePerUnitArea(), BigDecimal.valueOf(reportMultiAnalyseVo.getNumberOfDays())));
                    //计算每铺日均销售额
                    BigDecimal salePerUnitArealAvg = BigDecimal.ZERO;
                    BigDecimal saleTotalAvg = BigDecimal.ZERO;
                    int dayNum = 0;
                    for (String dateKey : dateByGatherType) {
                        //计算日均销售额 铺位数取同业态下的数据。
                        String[] dateSplit = dateKey.split("&");
                        if (dateSplit.length > 1 && StringUtils.equals(String.valueOf(categoryListVo.getId()), dateSplit[0])) {
                            dateKey = categoryListVo.getId() + "&" + dateSplit[1];
                            BigDecimal totalAmount = totalAmountByGatherType.get(dateKey);
                            Long roomNum = roomNumByGatherType.get(dateKey);
                            saleTotalAvg = BigDecimalUtil.add(saleTotalAvg, BigDecimalUtil.divide(totalAmount, BigDecimal.valueOf(roomNum)));
                            Double totalArea = totalAreaByGatherType.get(dateKey);
                            salePerUnitArealAvg = BigDecimalUtil.add(salePerUnitArealAvg, BigDecimalUtil.divide(totalAmount, BigDecimal.valueOf(totalArea)));
                            dayNum++;
                        }
                    }
                    result.setSaleSquareAvg(BigDecimalUtil.divide(salePerUnitArealAvg, BigDecimal.valueOf(dayNum)));
                    result.setSaleTotalAvgRoom(BigDecimalUtil.divide(saleTotalAvg, BigDecimal.valueOf(dayNum)));
                }
                ReportMultiAnalyseVo saleTotalVo = saleTotalMap.get(group+"&"+key);
                if(saleTotalVo!=null){
                    //计算出线下销售额占比
                    BigDecimal percent = BigDecimalUtil.multiply(BigDecimalUtil.divide(saleTotalVo.getStoreAmount(), saleTotalVo.getTotalAmount()),BigDecimal.valueOf(100));
                    result.setTotalAmount(saleTotalVo.getTotalAmount());
                    result.setTakeawayAmount(saleTotalVo.getTakeawayAmount());
                    result.setStoreAmount(saleTotalVo.getStoreAmount());
                    result.setStoreAmountPercent(percent);
                    result.setTakeawayAmountPercent(BigDecimalUtil.subtract(BigDecimal.valueOf(100),percent));
                }
            }
        }
    }

    /**
     * 获取项目级数据
     * @param req
     * @param resultList
     * @param currentList
     */
    private void getTenantAnalyseList(List<ReportMultiAnalyseVo>calcCurrentList,MultiAnalyseReq req,List<MultiAnalyseRespDetail>resultList,List<ReportMultiAnalyseVo>currentList){
        List<ReportMultiAnalyseVo> currentGroupList = currentList;
        Map<String, ReportMultiAnalyseVo> cityReportMap =calcCurrentList.stream().collect(Collectors.toMap((vo) -> {
            return  vo.getTenantName()+"&"+vo.getStoreId();
        }, t -> t));
        //计算某分类的所有天数和
        Set<String> dateByGatherType = currentGroupList.stream().map(vo -> vo.getTenantName()+"&"+vo.getStoreId() + "&" + vo.getCurrentDate()).collect(Collectors.toSet());
        // 计算每日每个项目下的铺位数
        Map<String, Long> roomNumByGatherType = currentGroupList.stream()
                .collect(Collectors.groupingBy(vo -> vo.getTenantName()+"&"+vo.getStoreId()+"&" + vo.getCurrentDate(), Collectors.counting()));
        // 计算每日某分类的销售总额
        Map<String, BigDecimal> totalAmountByGatherType = currentGroupList.stream()
                .collect(Collectors.groupingBy(
                        vo ->  vo.getTenantName()+"&"+vo.getStoreId()+ "&" + vo.getCurrentDate(), Collectors.reducing(BigDecimal.ZERO, ReportMultiAnalyseVo::getTotalAmount, BigDecimal::add)
                ));
        // 计算每日某分类的总面积
        Map<String, Double> totalAreaByGatherType = currentGroupList.stream().collect(Collectors.groupingBy(
                vo -> {
                    return   vo.getTenantName()+"&"+vo.getStoreId()+ "&" + vo.getCurrentDate();
                }, Collectors.summingDouble(ReportMultiAnalyseVo::getRoomArea)
        ));
        for (String key : cityReportMap.keySet()) {
            ReportMultiAnalyseVo reportMultiAnalyseVo = cityReportMap.get(key);
            MultiAnalyseRespDetail result = BeanUtils.copyProperties(reportMultiAnalyseVo, MultiAnalyseRespDetail.class);
            result.setSecondCategoryName(reportMultiAnalyseVo.getCommercialTwo());
            resultList.add(result);
            String[] split = key.split("&");
            if(split.length>1){
                String[] storeIdSplit = split[1].split("\\.");
                if(storeIdSplit.length>0){
                    result.setRoomName(storeIdSplit[0]);
                }
            }
            //计算出线下销售额占比
            BigDecimal percent = BigDecimalUtil.multiply(BigDecimalUtil.divide(reportMultiAnalyseVo.getStoreAmount(), reportMultiAnalyseVo.getTotalAmount()),BigDecimal.valueOf(100));
            result.setTotalAmount(reportMultiAnalyseVo.getTotalAmount());
            result.setTakeawayAmount(reportMultiAnalyseVo.getTakeawayAmount());
            result.setStoreAmount(reportMultiAnalyseVo.getStoreAmount());
            result.setStoreAmountPercent(percent);
            result.setTakeawayAmountPercent(BigDecimalUtil.subtract(BigDecimal.valueOf(100),percent));
            //计算每铺日均销售额
            BigDecimal salePerUnitArealAvg = BigDecimal.ZERO;
            BigDecimal saleTotalAvg = BigDecimal.ZERO;
            int dayNum = 0;
            for (String dateKey : dateByGatherType) {
                //计算日均销售额
                if(dateKey.contains(key)){
                    BigDecimal totalAmount = totalAmountByGatherType.get(dateKey);
                    Long roomNum = roomNumByGatherType.get(dateKey);
                    saleTotalAvg = BigDecimalUtil.add(saleTotalAvg,BigDecimalUtil.divide(totalAmount, BigDecimal.valueOf(roomNum)));
                    Double totalArea = totalAreaByGatherType.get(dateKey);
                    salePerUnitArealAvg = BigDecimalUtil.add(salePerUnitArealAvg, BigDecimalUtil.divide(totalAmount, BigDecimal.valueOf(totalArea)));
                    dayNum++;
                }
            }
            //计算出日均销售坪效占比
            result.setSaleSquareAvg(BigDecimalUtil.divide(salePerUnitArealAvg, BigDecimal.valueOf(dayNum)));
            //计算每铺日均销售额
            result.setSaleTotalAvgRoom(BigDecimalUtil.divide(saleTotalAvg, BigDecimal.valueOf(dayNum)));
        }
        Collections.sort(resultList, Comparator.comparing(MultiAnalyseRespDetail::getStoreId));
    }

    private void setSaleAmountInfo(MultiAnalyseReq req, MultiAnalyseResp resp, List<ReportMultiAnalyseVo> saleAnalyseBaseList, List<ReportMultiAnalyseVo> saleAmountDateList) {
        // 查询总天数
        int dateNum = (int) ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate()) + 1;

        List<BigDecimal> saleAvgRoomList = getSaleAvgRoomList(saleAnalyseBaseList, saleAmountDateList);

        // 每铺日均销售额
        BigDecimal avgRoomAmount = saleAvgRoomList.get(0);
        BigDecimal saleTotalAvgRoom = BigDecimalUtil.divide(avgRoomAmount, BigDecimal.valueOf(dateNum));
        resp.setSaleTotalAvgRoom(saleTotalAvgRoom);

        // 日销售坪效
        BigDecimal avgSquareAmount = saleAvgRoomList.get(1);
        BigDecimal saleSquareAvg = BigDecimalUtil.divide(avgSquareAmount, BigDecimal.valueOf(dateNum));
        resp.setSaleSquareAvg(saleSquareAvg);
    }

    /**
     * 获取每铺销售总销售额和 和 获取销售总坪效
     */
    private List<BigDecimal> getSaleAvgRoomList(List<ReportMultiAnalyseVo> saleAnalyseBaseList, List<ReportMultiAnalyseVo> saleAmountDateList) {
        List<BigDecimal> avgRoomList = new ArrayList<>();
        BigDecimal saleTotalAvgRoom = BigDecimal.ZERO;
        BigDecimal saleSquareAvgRoom = BigDecimal.ZERO;
        // 按日期分组
        Map<String, ReportMultiAnalyseVo> saleDateMap = saleAmountDateList.stream().collect(Collectors.toMap(ReportMultiAnalyseVo::getYearMonthDay, Function.identity(), (v1, v2) -> v1));
        Map<String, List<ReportMultiAnalyseVo>> dateMap = saleAnalyseBaseList.stream().collect(Collectors.groupingBy(ReportMultiAnalyseVo::getYearMonthDay));
        for (Map.Entry<String, List<ReportMultiAnalyseVo>> dateEntry : dateMap.entrySet()) {
            String dateKey = dateEntry.getKey();
            List<ReportMultiAnalyseVo> dateValue = dateEntry.getValue();
            // 销售总额
            ReportMultiAnalyseVo multiAnalyseVo = saleDateMap.get(dateKey);
            BigDecimal saleAmount = multiAnalyseVo!=null ? multiAnalyseVo.getTotalAmount() : BigDecimal.ZERO;
            // 面积总额
            double areaAmount = dateValue.stream().mapToDouble(ReportMultiAnalyseVo::getRoomArea).sum();
            //long roomNum = dateValue.stream().map(f -> f.getTenantId()+"&"+f.getFCode()).distinct().count();
            // 总铺位数 每天铺位不会重复
            long roomNum = dateValue.size();
            BigDecimal avgRoomAmount = BigDecimalUtil.divide(saleAmount, BigDecimal.valueOf(roomNum));
            BigDecimal avgSquareAmount = BigDecimalUtil.divide(saleAmount, BigDecimal.valueOf(areaAmount));
            saleTotalAvgRoom = saleTotalAvgRoom.add(avgRoomAmount);
            saleSquareAvgRoom = saleSquareAvgRoom.add(avgSquareAmount);
        }
        avgRoomList.add(saleTotalAvgRoom);
        avgRoomList.add(saleSquareAvgRoom);
        return avgRoomList;
    }

    private static List<MultiAnalyseRespChart> getSalePercentList(List<ReportMultiAnalyseVo> saleAnalyseBaseList) {
        List<MultiAnalyseRespChart> salePercentList = new ArrayList<>();
        // 按业态进行分组
        Map<String, List<ReportMultiAnalyseVo>> commercialMap = saleAnalyseBaseList.stream().filter(f -> StrUtil.isNotEmpty(f.getCommercialTypeName())).collect(Collectors.groupingBy(ReportMultiAnalyseVo::getCommercialTypeName));
        for (Map.Entry<String, List<ReportMultiAnalyseVo>> entry : commercialMap.entrySet()) {
            String mapKey = entry.getKey();
            List<ReportMultiAnalyseVo> mapValue = entry.getValue();
            BigDecimal storeSaleAmount = mapValue.stream().map(ReportMultiAnalyseVo::getStoreAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal takeawaySaleAmount = mapValue.stream().map(ReportMultiAnalyseVo::getTakeawayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (mapKey.equals("餐饮")) {
                MultiAnalyseRespChart caterStore = new MultiAnalyseRespChart();
                caterStore.setName("餐饮线下");
                caterStore.setValue(storeSaleAmount);
                MultiAnalyseRespChart caterTakeway = new MultiAnalyseRespChart();
                caterTakeway.setName("餐饮线上");
                caterTakeway.setValue(takeawaySaleAmount);
                salePercentList.add(caterStore);
                salePercentList.add(caterTakeway);
            } else if (mapKey.equals("零售")) {
                MultiAnalyseRespChart retailStore = new MultiAnalyseRespChart();
                retailStore.setName("零售线下");
                retailStore.setValue(storeSaleAmount);
                MultiAnalyseRespChart retailTakeway = new MultiAnalyseRespChart();
                retailTakeway.setName("零售线上");
                retailTakeway.setValue(takeawaySaleAmount);
                salePercentList.add(retailStore);
                salePercentList.add(retailTakeway);
            } else if (mapKey.equals("娱乐服务")) {
                MultiAnalyseRespChart entertainStore = new MultiAnalyseRespChart();
                entertainStore.setName("娱乐服务线下");
                entertainStore.setValue(storeSaleAmount);
                MultiAnalyseRespChart entertainTakeway = new MultiAnalyseRespChart();
                entertainTakeway.setName("娱乐服务线上");
                entertainTakeway.setValue(takeawaySaleAmount);
                salePercentList.add(entertainStore);
                salePercentList.add(entertainTakeway);
            }
        }
        return salePercentList;
    }

    private static List<MultiAnalyseRespAmountItem> getSaleAnalyseBaseList(Map<String, List<ReportMultiAnalyseVo>> dateMap, BigDecimal saleTotal) {
        // BigDecimal caterTotal = BigDecimal.ZERO;
        // BigDecimal retailTotal = BigDecimal.ZERO;
        // BigDecimal entertainTotal = BigDecimal.ZERO;
        // 销售额分析和销售额分布
        List<MultiAnalyseRespAmountItem> analyseList = new ArrayList<>();
        for (Map.Entry<String, List<ReportMultiAnalyseVo>> dateEntry : dateMap.entrySet()) {
            String dateKey = dateEntry.getKey();
            List<ReportMultiAnalyseVo> dateValue = dateEntry.getValue();
            MultiAnalyseRespAmountItem respItem = new MultiAnalyseRespAmountItem();
            respItem.setName(dateKey);
            // 按业态进行分组
            Map<String, List<ReportMultiAnalyseVo>> commercialMap = dateValue.stream().filter(f -> StrUtil.isNotEmpty(f.getCommercialTypeName())).collect(Collectors.groupingBy(ReportMultiAnalyseVo::getCommercialTypeName));
            for (Map.Entry<String, List<ReportMultiAnalyseVo>> entry : commercialMap.entrySet()) {
                String mapKey = entry.getKey();
                List<ReportMultiAnalyseVo> mapValue = entry.getValue();
                // 业态销售额总和
                BigDecimal commercialSaleAmount = mapValue.stream().map(ReportMultiAnalyseVo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (mapKey.equals("餐饮")){
                    respItem.setCaterValue(commercialSaleAmount);
                    // caterTotal = caterTotal.add(commercialSaleAmount);
                }else if (mapKey.equals("零售")){
                    respItem.setRetailValue(commercialSaleAmount);
                    // retailTotal = retailTotal.add(commercialSaleAmount);
                }else if (mapKey.equals("娱乐服务")){
                    respItem.setEntertainValue(commercialSaleAmount);
                    // entertainTotal = entertainTotal.add(commercialSaleAmount);
                }
            }
            analyseList.add(respItem);
        }
        // 销售额分析和销售额分布遍历计算比例
        for (MultiAnalyseRespAmountItem amountItem : analyseList) {
            BigDecimal caterValue = amountItem.getCaterValue();
            BigDecimal retailValue = amountItem.getRetailValue();
            BigDecimal entertainValue = amountItem.getEntertainValue();
            BigDecimal caterPercent = BigDecimalUtil.multiply(BigDecimalUtil.divide(caterValue, saleTotal,4),BigDecimal.valueOf(100));
            BigDecimal retailPercent = BigDecimalUtil.multiply(BigDecimalUtil.divide(retailValue, saleTotal,4),BigDecimal.valueOf(100));
            BigDecimal entertainPercent = BigDecimalUtil.multiply(BigDecimalUtil.divide(entertainValue, saleTotal,4),BigDecimal.valueOf(100));
            amountItem.setCaterPercent(caterPercent);
            amountItem.setRetailPercent(retailPercent);
            amountItem.setEntertainPercent(entertainPercent);
        }
        return analyseList;
    }

    private void setAmountDefaultInfo(MultiAnalyseReq req, MultiAnalyseResp resp, List<EntProjectListVo> tenantList) {
        LocalDate startDate = req.getStartDate();
        LocalDate endDate = req.getEndDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        // 销售额分布按城市给默认值
        List<MultiAnalyseRespAmountItem> cityResultList = new ArrayList<>();

        Map<String, MultiAnalyseRespAmountItem> cityItemMap = new HashMap<>();
        List<MultiAnalyseRespAmountItem> saleDistributeList = resp.getSaleDistributeList();
        if (!CollectionUtils.isEmpty(saleDistributeList)) {
            cityItemMap = saleDistributeList.stream().collect(Collectors.toMap(MultiAnalyseRespAmountItem::getName, s -> s));
        }

        List<String> cityList;
        if (req.getSearchType() == SerchTypeEnum.COUNTRY.value()) {
            cityList = tenantList.stream().map(EntProjectListVo::getFcity).distinct().collect(Collectors.toList());
        }else {
            cityList = tenantList.stream().map(EntProjectListVo::getName).distinct().collect(Collectors.toList());
        }
        for (String city : cityList) {
            MultiAnalyseRespAmountItem respAmount = cityItemMap.get(city);
            if (respAmount != null) {
                cityResultList.add(respAmount);
            } else {
                MultiAnalyseRespAmountItem amountItem = new MultiAnalyseRespAmountItem();
                amountItem.setName(city);
                cityResultList.add(amountItem);
            }
        }

        resp.setSaleDistributeList(cityResultList);

        // 销售额占比给默认值
        List<MultiAnalyseRespChart> salePercentResultList = new ArrayList<>();

        Map<String, MultiAnalyseRespChart> percentItemMap = new HashMap<>();
        List<MultiAnalyseRespChart> salePercentList = resp.getSalePercentList();
        if (!CollectionUtils.isEmpty(salePercentList)) {
            percentItemMap = salePercentList.stream().collect(Collectors.toMap(MultiAnalyseRespChart::getName, s -> s));
        }

        // 获取业态列表
        List<String> busTypeList = new ArrayList<>();
        busTypeList.add("餐饮线上");
        busTypeList.add("餐饮线下");
        busTypeList.add("零售线上");
        busTypeList.add("零售线下");
        busTypeList.add("娱乐服务线上");
        busTypeList.add("娱乐服务线下");
        for (String busType : busTypeList) {
            MultiAnalyseRespChart respAmount = percentItemMap.get(busType);
            if (respAmount != null) {
                salePercentResultList.add(respAmount);
            } else {
                MultiAnalyseRespChart amountItem = new MultiAnalyseRespChart();
                amountItem.setName(busType);
                salePercentResultList.add(amountItem);
            }
        }
        resp.setSalePercentList(salePercentResultList);

        // 销售额分析按时间给默认值
        List<MultiAnalyseRespAmountItem> analyseResultList = new ArrayList<>();

        Map<String, MultiAnalyseRespAmountItem> analyseItemMap = new HashMap<>();
        List<MultiAnalyseRespAmountItem> saleAnalyseList = resp.getSaleAnalyseList();
        if (!CollectionUtils.isEmpty(saleAnalyseList)) {
            analyseItemMap = saleAnalyseList.stream().collect(Collectors.toMap(MultiAnalyseRespAmountItem::getName, s -> s));
        }

        // 获取日期列表
        List<String> dateList = new ArrayList<>();
        if (req.getTimeType()!=null && req.getTimeType()==0){
            dateList.add(startDate.toString());
            LocalDate tempDate = startDate.plusDays(1);
            while (tempDate.isBefore(endDate) || tempDate.isEqual(endDate)) {
                dateList.add(tempDate.toString());
                tempDate = tempDate.plusDays(1);
            }
        }else {
            dateList.add(startDate.format(formatter));
            LocalDate tempDate = startDate.plusMonths(1);
            while (tempDate.isBefore(endDate) || tempDate.isEqual(endDate)) {
                dateList.add(tempDate.format(formatter));
                tempDate = tempDate.plusMonths(1);
            }
        }

        for (String dateStr : dateList) {
            MultiAnalyseRespAmountItem respAmount = analyseItemMap.get(dateStr);
            if (respAmount != null) {
                analyseResultList.add(respAmount);
            } else {
                MultiAnalyseRespAmountItem amountItem = new MultiAnalyseRespAmountItem();
                amountItem.setName(dateStr);
                analyseResultList.add(amountItem);
            }
        }
        resp.setSaleAnalyseList(analyseResultList);
    }


    private List<MultiAnalyseRespDetail> convertResultList(List<ReportMultiAnalyseVo>selectResultList, List<MultiAnalyseRespDetail> resultList) {
        resultList.addAll(BeanUtils.copyProperties(selectResultList, MultiAnalyseRespDetail.class));
        return resultList;
    }
    private List<ReportMultiAnalyseVo> selectResultList(ReportMultiAnalyseListDto queryDto) {
        return reportMasterSaleDataProvider.getSaleDetailByGatherType(queryDto);
    }



    public double calculateAvgRentalRate(List<ReportOperateDayAnalyseListVo> dataList, LocalDate startDate, LocalDate endDate) {
        if (dataList == null || dataList.isEmpty()) {
            return 0.0;
        }

        // 1. 生成日期范围内的所有日期
        List<LocalDate> dateRange = Stream.iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .collect(Collectors.toList());

        // 2. 获取每个项目的总面积（取首次出现的值）
        Map<String, Double> projectAreaMap = dataList.stream()
                .collect(Collectors.toMap(
                        ReportOperateDayAnalyseListVo::getTenantName,
                        ReportOperateDayAnalyseListVo::getActualArea,
                        (existing, replacement) -> existing // 保留首次出现的值
                ));

        // 3. 按项目分组，并使用年、月、日计算每个项目每天的roomArea总和
        Map<String, Map<LocalDate, Double>> projectDailyRoomAreaMap = dataList.stream()
                .collect(Collectors.groupingBy(
                        ReportOperateDayAnalyseListVo::getTenantName,
                        Collectors.groupingBy(
                                vo -> LocalDate.of(vo.getYear(), vo.getMonth(), vo.getDay()),
                                Collectors.summingDouble(ReportOperateDayAnalyseListVo::getRoomArea)
                        )
                ));

        // 4. 计算每个项目的平均出租率
        double totalAvgRate = projectDailyRoomAreaMap.entrySet().stream()
                .mapToDouble(entry -> {
                    String tenantName = entry.getKey();
                    Map<LocalDate, Double> dailyRoomAreas = entry.getValue();
                    Double projectArea = projectAreaMap.get(tenantName);

                    // 跳过无效项目
                    if (projectArea == null || projectArea == 0) {
                        return Double.NaN; // 标记无效值
                    }

                    // 计算该项目在日期范围内的平均出租率
                    return dateRange.stream()
                            .mapToDouble(date -> {
                                // 获取该日期的房间面积总和，若无数据则为0
                                Double dailyRoomArea = dailyRoomAreas.getOrDefault(date, 0.0);
                                return dailyRoomArea / projectArea;
                            })
                            .average()
                            .orElse(Double.NaN);
                })
                .filter(Double::isFinite) // 过滤掉NaN和无穷大
                .average()
                .orElse(0.0);
        double result = Math.round(totalAvgRate * 100 * 100.0) / 100.0;
        return result;
    }


    public List<MultiRentAnalyseRespItem> calculateDailyAvgRentalRateByCommercialType(List<ReportOperateDayAnalyseListVo> dataList,LocalDate startDate,LocalDate endDate) {
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }

        List<MultiRentAnalyseRespItem> resultList = new ArrayList<>();


        // 1. 生成日期范围内的所有日期
        List<LocalDate> dateRange = Stream.iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .collect(Collectors.toList());

        // 2. 获取每个项目的总面积（取首次出现的值）
        Map<String, Double> projectAreaMap = dataList.stream()
                .collect(Collectors.toMap(
                        ReportOperateDayAnalyseListVo::getTenantName,
                        ReportOperateDayAnalyseListVo::getActualArea,
                        (existing, replacement) -> existing));

        // 3. 按项目分组，再按业态分组，最后按日期分组计算每个项目每天每个业态的roomArea总和
        Map<String, Map<String, Map<LocalDate, Double>>> projectCommercialDailyMap = dataList.stream()
                .collect(Collectors.groupingBy(
                        ReportOperateDayAnalyseListVo::getTenantName,
                        Collectors.groupingBy(
                                ReportOperateDayAnalyseListVo::getCommercialTypeName,
                                Collectors.groupingBy(
                                        vo -> LocalDate.of(vo.getYear(), vo.getMonth(), vo.getDay()),
                                        Collectors.summingDouble(ReportOperateDayAnalyseListVo::getRoomArea)
                                )
                        )
                ));

        // 4. 收集所有业态类型
        Set<String> commercialTypes = dataList.stream()
                .map(ReportOperateDayAnalyseListVo::getCommercialTypeName)
                .collect(Collectors.toSet());

        // 5. 计算每天每个业态的平均出租率
        Map<LocalDate, Map<String, Double>> result = new LinkedHashMap<>();

        for (LocalDate date : dateRange) {
            Map<String, List<Double>> dailyRatesByCommercialType = new HashMap<>();

            // 初始化每种业态的列表
            commercialTypes.forEach(type -> dailyRatesByCommercialType.put(type, new ArrayList<>()));

            // 收集当天所有项目中每种业态的出租率
            for (Map.Entry<String, Map<String, Map<LocalDate, Double>>> projectEntry :
                    projectCommercialDailyMap.entrySet()) {

                String tenantName = projectEntry.getKey();
                Double projectArea = projectAreaMap.get(tenantName);

                // 跳过无效项目
                if (projectArea == null || projectArea == 0) {
                    continue;
                }

                Map<String, Map<LocalDate, Double>> commercialMap = projectEntry.getValue();

                for (String commercialType : commercialTypes) {
                    Map<LocalDate, Double> dailyAreas = commercialMap.get(commercialType);

                    if (dailyAreas != null && dailyAreas.containsKey(date)) {
                        double roomArea = dailyAreas.get(date);
                        double rate = roomArea / projectArea;
                        dailyRatesByCommercialType.get(commercialType).add(rate);
                    }
                }
            }

            // 计算当天每种业态的平均出租率
            Map<String, Double> dailyAvgRates = new LinkedHashMap<>();
            for (Map.Entry<String, List<Double>> entry : dailyRatesByCommercialType.entrySet()) {
                String commercialType = entry.getKey();
                List<Double> rates = entry.getValue();

                double avgRate = rates.isEmpty() ? 0.0 :
                        rates.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

                // 转换为百分比并保留两位小数
                double percentage = Math.round(avgRate * 100 * 100.0) / 100.0;
                dailyAvgRates.put(commercialType, percentage);
            }

            MultiRentAnalyseRespItem multiRentAnalyseRespItem = new MultiRentAnalyseRespItem();
            multiRentAnalyseRespItem.setName(date.toString());
            multiRentAnalyseRespItem.setCaterPercent(BigDecimal.valueOf(dailyAvgRates.get("餐饮")));
            multiRentAnalyseRespItem.setRetailPercent(BigDecimal.valueOf(dailyAvgRates.get("零售")));
            multiRentAnalyseRespItem.setEntertainPercent(BigDecimal.valueOf(dailyAvgRates.get("娱乐服务")));

            resultList.add(multiRentAnalyseRespItem);
        }

        return resultList;
    }



    public List<MultiRentAnalyseRespItem> calculateCityAvgRentalRateByCommercialType(List<ReportOperateDayAnalyseListVo> dataList, LocalDate startDate, LocalDate endDate) {

        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. 生成日期范围内的所有日期
        List<LocalDate> dateRange = Stream.iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .collect(Collectors.toList());

        // 2. 获取每个项目的总面积（取首次出现的值）和城市信息
        Map<String, Double> projectAreaMap = new HashMap<>();
        Map<String, String> projectCityMap = new HashMap<>();
        dataList.forEach(vo -> {
            projectAreaMap.putIfAbsent(vo.getTenantName(), vo.getActualArea());
            projectCityMap.putIfAbsent(vo.getTenantName(), vo.getCity());
        });

        // 3. 按城市分组，再按项目分组，再按业态分组，最后按日期分组计算roomArea总和
        Map<String, Map<String, Map<String, Map<LocalDate, Double>>>> cityProjectCommercialDailyMap =
                dataList.stream()
                        .collect(Collectors.groupingBy(ReportOperateDayAnalyseListVo::getCity,
                                Collectors.groupingBy(ReportOperateDayAnalyseListVo::getTenantName,
                                        Collectors.groupingBy(ReportOperateDayAnalyseListVo::getCommercialTypeName,
                                                Collectors.groupingBy(vo -> LocalDate.of(vo.getYear(), vo.getMonth(), vo.getDay()),
                                                        Collectors.summingDouble(ReportOperateDayAnalyseListVo::getRoomArea)
                                                )
                                        )
                                )
                        ));

        // 4. 准备结果列表
        List<MultiRentAnalyseRespItem> resultList = new ArrayList<>();

        // 5. 计算每个城市每种业态的平均出租率
        for (Map.Entry<String, Map<String, Map<String, Map<LocalDate, Double>>>> cityEntry :
                cityProjectCommercialDailyMap.entrySet()) {

            String city = cityEntry.getKey();
            Map<String, Map<String, Map<LocalDate, Double>>> projectCommercialMap = cityEntry.getValue();

            // 存储该城市所有项目中每种业态的每日出租率
            Map<String, List<Double>> cityCommercialRates = new HashMap<>();
            cityCommercialRates.put("餐饮", new ArrayList<>());
            cityCommercialRates.put("零售", new ArrayList<>());
            cityCommercialRates.put("娱乐服务", new ArrayList<>());

            // 遍历该城市下的所有项目
            for (Map.Entry<String, Map<String, Map<LocalDate, Double>>> projectEntry :
                    projectCommercialMap.entrySet()) {

                String tenantName = projectEntry.getKey();
                Double projectArea = projectAreaMap.get(tenantName);

                // 跳过无效项目
                if (projectArea == null || projectArea == 0) {
                    continue;
                }

                Map<String, Map<LocalDate, Double>> commercialMap = projectEntry.getValue();

                // 计算该项目每种业态在时间范围内的平均出租率
                for (String commercialType : Arrays.asList("餐饮", "零售", "娱乐服务")) {
                    Map<LocalDate, Double> dailyAreas = commercialMap.get(commercialType);
                    if (dailyAreas == null) {
                        continue;
                    }

                    // 计算该业态在时间范围内的平均出租率
                    double totalRate = dateRange.stream()
                            .mapToDouble(date -> {
                                Double roomArea = dailyAreas.getOrDefault(date, 0.0);
                                return roomArea / projectArea;
                            })
                            .sum();

                    double avgRate = totalRate / dateRange.size();
                    cityCommercialRates.get(commercialType).add(avgRate);
                }
            }

            // 创建结果对象
            MultiRentAnalyseRespItem cityResp = new MultiRentAnalyseRespItem();
            cityResp.setName(city); // 使用城市名称作为name

            // 计算餐饮业态平均值
            double caterAvg = cityCommercialRates.get("餐饮").stream()
                    .mapToDouble(Double::doubleValue)
                    .average()
                    .orElse(0.0);
            cityResp.setCaterPercent(BigDecimal.valueOf(Math.round(caterAvg * 100 * 100.0) / 100.0));

            // 计算零售业态平均值
            double retailAvg = cityCommercialRates.get("零售").stream()
                    .mapToDouble(Double::doubleValue)
                    .average()
                    .orElse(0.0);
            cityResp.setRetailPercent(BigDecimal.valueOf(Math.round(retailAvg * 100 * 100.0) / 100.0));

            // 计算娱乐服务业态平均值
            double entertainAvg = cityCommercialRates.get("娱乐服务").stream()
                    .mapToDouble(Double::doubleValue)
                    .average()
                    .orElse(0.0);
            cityResp.setEntertainPercent(BigDecimal.valueOf(Math.round(entertainAvg * 100 * 100.0) / 100.0));

            resultList.add(cityResp);
        }

        return resultList;
    }

    /**
     * 通用城市分组出租率统计，支持业态/一级品类/二级品类灵活分组，并输出同比、环比
     * @param dataList 主区间数据
     * @param pariDataList 同比区间数据
     * @param ringDiffDataList 环比区间数据
     * @param startDate 主区间开始
     * @param endDate 主区间结束
     * @param gather 0-业态 1-一级品类 2-二级品类
     * @return List<MultiRentAnalyseRespItem>
     */
    public List<MultiRentAnalyseRespDetail> calculateCityRentalRateByGather(List<ReportOperateDayAnalyseListVo> dataList,
                                                                         List<ReportOperateDayAnalyseListVo> pariDataList,
                                                                         List<ReportOperateDayAnalyseListVo> ringDiffDataList,
                                                                         LocalDate startDate, LocalDate endDate,
                                                                         int gather) {
        // 1. 生成日期范围
        List<LocalDate> dateRange = Stream.iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .collect(Collectors.toList());

        // 2. 获取每个项目的总面积（首次出现）和城市
        Map<String, Double> projectAreaMap = new HashMap<>();
        Map<String, String> projectCityMap = new HashMap<>();
        dataList.forEach(vo -> {
            projectAreaMap.putIfAbsent(vo.getTenantName(), vo.getActualArea());
            projectCityMap.putIfAbsent(vo.getTenantName(), vo.getCity());
        });

        // 3. 主区间：城市-分组key-出租率
        Map<String, Map<String, List<Double>>> cityTypeRateMap = buildCityTypeRateMap(dataList, dateRange, gather);
        // 4. 同比区间
        Map<String, Map<String, List<Double>>> cityTypeRateMapPari = buildCityTypeRateMap(pariDataList, dateRange, gather);
        // 5. 环比区间
        Map<String, Map<String, List<Double>>> cityTypeRateMapRing = buildCityTypeRateMap(ringDiffDataList, dateRange, gather);

        // 6. 汇总所有城市和分组key
        Set<String> allCities = new HashSet<>();
        Set<String> allTypeKeys = new HashSet<>();
        allCities.addAll(cityTypeRateMap.keySet());
        allCities.addAll(cityTypeRateMapPari.keySet());
        allCities.addAll(cityTypeRateMapRing.keySet());
        cityTypeRateMap.values().forEach(m -> allTypeKeys.addAll(m.keySet()));
        cityTypeRateMapPari.values().forEach(m -> allTypeKeys.addAll(m.keySet()));
        cityTypeRateMapRing.values().forEach(m -> allTypeKeys.addAll(m.keySet()));

        List<MultiRentAnalyseRespDetail> resultList = new ArrayList<>();
        for (String city : allCities) {
            Map<String, List<Double>> typeRateMap = cityTypeRateMap.getOrDefault(city, Collections.emptyMap());
            Map<String, List<Double>> typeRateMapPari = cityTypeRateMapPari.getOrDefault(city, Collections.emptyMap());
            Map<String, List<Double>> typeRateMapRing = cityTypeRateMapRing.getOrDefault(city, Collections.emptyMap());
            for (String typeKey : allTypeKeys) {
                // 主区间均值
                double avg = typeRateMap.getOrDefault(typeKey, Collections.emptyList()).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                // 同比均值
                double avgPari = typeRateMapPari.getOrDefault(typeKey, Collections.emptyList()).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                // 环比均值
                double avgRing = typeRateMapRing.getOrDefault(typeKey, Collections.emptyList()).stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                // 百分比并保留两位小数
                double percent = Math.round(avg * 100 * 100.0) / 100.0;
                double percentPari = Math.round(avgPari * 100 * 100.0) / 100.0;
                double percentRing = Math.round(avgRing * 100 * 100.0) / 100.0;
                // 同比、环比
                double yoy = (percentPari == 0.0) ? 0.0 : Math.round((percent - percentPari) / percentPari * 100 * 100.0) / 100.0;
                double qoq = (percentRing == 0.0) ? 0.0 : Math.round((percent - percentRing) / percentRing * 100 * 100.0) / 100.0;
                // 拆分typeKey
                String[] keys = typeKey.split("\\|");
                MultiRentAnalyseRespDetail item = new MultiRentAnalyseRespDetail();
                item.setCity(city);
                if (gather == 0) {
                    item.setCommercialTypeName(keys.length > 0 ? keys[0] : null);
                } else if (gather == 1) {
                    item.setCommercialTypeName(keys.length > 0 ? keys[0] : null);
                    item.setCategoryName(keys.length > 1 ? keys[1] : null);
                } else if (gather == 2) {
                    item.setCommercialTypeName(keys.length > 0 ? keys[0] : null);
                    item.setCategoryName(keys.length > 1 ? keys[1] : null);
                    item.setSecondCategoryName(keys.length > 2 ? keys[2] : null);
                }
                item.setRate(BigDecimal.valueOf(percent));
                item.setRateYoy(BigDecimal.valueOf(yoy));
                item.setRateQoq(BigDecimal.valueOf(qoq));
                resultList.add(item);
            }
        }
        return resultList;
    }

    // 辅助方法：构建城市-分组key-出租率列表
    private Map<String, Map<String, List<Double>>> buildCityTypeRateMap(List<ReportOperateDayAnalyseListVo> dataList, List<LocalDate> dateRange, int gather) {
        if (dataList == null || dataList.isEmpty()) return Collections.emptyMap();
        // 项目总面积
        Map<String, Double> projectAreaMap = new HashMap<>();
        Map<String, String> projectCityMap = new HashMap<>();
        dataList.forEach(vo -> {
            projectAreaMap.putIfAbsent(vo.getTenantName(), vo.getActualArea());
            projectCityMap.putIfAbsent(vo.getTenantName(), vo.getCity());
        });
        // 项目-分组key-每天roomArea
        Map<String, Map<String, Map<LocalDate, Double>>> projectTypeDayArea = new HashMap<>();
        for (ReportOperateDayAnalyseListVo vo : dataList) {
            String project = vo.getTenantName();
            String typeKey = getTypeKey(vo, gather);
            LocalDate date = LocalDate.of(vo.getYear(), vo.getMonth(), vo.getDay());
            projectTypeDayArea
                .computeIfAbsent(project, k -> new HashMap<>())
                .computeIfAbsent(typeKey, k -> new HashMap<>())
                .merge(date, vo.getRoomArea(), Double::sum);
        }
        // 项目-分组key-均值
        Map<String, Map<String, Double>> projectTypeAvgRate = new HashMap<>();
        for (String project : projectTypeDayArea.keySet()) {
            Double projectArea = projectAreaMap.get(project);
            if (projectArea == null || projectArea == 0) continue;
            Map<String, Map<LocalDate, Double>> typeDayMap = projectTypeDayArea.get(project);
            for (String typeKey : typeDayMap.keySet()) {
                Map<LocalDate, Double> dayMap = typeDayMap.get(typeKey);
                double avg = dateRange.stream()
                        .mapToDouble(date -> dayMap.getOrDefault(date, 0.0) / projectArea)
                        .average().orElse(0.0);
                projectTypeAvgRate.computeIfAbsent(project, k -> new HashMap<>()).put(typeKey, avg);
            }
        }
        // 城市-分组key-所有项目均值
        Map<String, Map<String, List<Double>>> cityTypeRateMap = new HashMap<>();
        for (String project : projectTypeAvgRate.keySet()) {
            String city = projectCityMap.get(project);
            Map<String, Double> typeAvgMap = projectTypeAvgRate.get(project);
            for (String typeKey : typeAvgMap.keySet()) {
                double avg = typeAvgMap.get(typeKey);
                cityTypeRateMap.computeIfAbsent(city, k -> new HashMap<>())
                        .computeIfAbsent(typeKey, k -> new ArrayList<>())
                        .add(avg);
            }
        }
        return cityTypeRateMap;
    }

    // 分组key生成
    private String getTypeKey(ReportOperateDayAnalyseListVo vo, int gather) {
        if (gather == 0) {
            return vo.getCommercialTypeName() == null ? "" : vo.getCommercialTypeName();
        } else if (gather == 1) {
            return (vo.getCommercialTypeName() == null ? "" : vo.getCommercialTypeName()) + "|" + (vo.getCategoryName() == null ? "" : vo.getCategoryName());
        } else {
            return (vo.getCommercialTypeName() == null ? "" : vo.getCommercialTypeName()) + "|" + (vo.getCategoryName() == null ? "" : vo.getCategoryName()) + "|" + (vo.getCommercialTwo() == null ? "" : vo.getCommercialTwo());
        }
    }





    private MultiRentAnalyseResp calculateExitAreaRate(List<ReportExitStatisListVo> dataList,ReportOperateDayAnalyseDto queryDto,MultiAnalyseReq req) {
        MultiRentAnalyseResp resp = new MultiRentAnalyseResp();
        // 2. 计算当前周期撤场总面积
        double totalExitArea = dataList.stream()
                .mapToDouble(ReportExitStatisListVo::getActualArea)
                .sum();
        logger.info("全国撤场总面积：{}", totalExitArea);

        // 3. 计算同比
        double yoyRate = 0.0;
        MultiAnalyseReq pariReq = RatioCalculateUtil.getPariDate(req);
        queryDto.setStartDate(pariReq.getStartDate());
        queryDto.setEndDate(pariReq.getEndDate());
        List<ReportExitStatisListVo> yoyDataList = reportExitStatisProvider.getAllData(queryDto);
        if (!yoyDataList.isEmpty()) {
            double yoyTotal = yoyDataList.stream()
                    .mapToDouble(ReportExitStatisListVo::getActualArea)
                    .sum();
            if (yoyTotal != 0) {
                yoyRate = Math.round((totalExitArea - yoyTotal) / yoyTotal * 100 * 100.0) / 100.0;
            }
            logger.info("去年同期撤场面积：{}", yoyTotal);
        }

        // 4. 计算环比
        double qoqRate = 0.0;
        MultiAnalyseReq ringDiffReq = RatioCalculateUtil.getRingDiff(req);
        queryDto.setStartDate(ringDiffReq.getStartDate());
        queryDto.setEndDate(ringDiffReq.getEndDate());
        List<ReportExitStatisListVo> qoqDataList = reportExitStatisProvider.getAllData(queryDto);
        if (!qoqDataList.isEmpty()) {
            double qoqTotal = qoqDataList.stream()
                    .mapToDouble(ReportExitStatisListVo::getActualArea)
                    .sum();
            if (qoqTotal != 0) {
                qoqRate = Math.round((totalExitArea - qoqTotal) / qoqTotal * 100 * 100.0) / 100.0;
            }
            logger.info("上期撤场面积：{}", qoqTotal);
        }

        // 5. 设置基础响应数据
        resp.setRate(BigDecimal.valueOf(totalExitArea));
        resp.setRateYoy(BigDecimal.valueOf(yoyRate));
        resp.setRateQoq(BigDecimal.valueOf(qoqRate));

        // 6. 按业态分组计算每日撤场面积（使用MultiRentAnalyseRespItem）
        List<MultiRentAnalyseRespItem> dailyExitList = dataList.stream()
                .collect(Collectors.groupingBy(
                        ReportExitStatisListVo::getExitDate,  // 直接使用exitDate字段
                        Collectors.groupingBy(
                                ReportExitStatisListVo::getAdaptiveFormat,
                                Collectors.summingDouble(ReportExitStatisListVo::getActualArea)
                        )
                ))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey())  // 按日期排序
                .map(entry -> {
                    MultiRentAnalyseRespItem item = new MultiRentAnalyseRespItem();
                    item.setName(entry.getKey().toString());  // 使用exitDate作为名称

                    // 设置数值（面积）
                    item.setCaterValue((int)Math.round(entry.getValue().getOrDefault("餐饮", 0.0)));
                    item.setRetailValue((int)Math.round(entry.getValue().getOrDefault("零售", 0.0)));
                    item.setEntertainValue((int)Math.round(entry.getValue().getOrDefault("娱乐服务", 0.0)));

                    // 计算比例（当前日期的业态面积占比）
                    double dailyTotal = entry.getValue().values().stream().mapToDouble(Double::doubleValue).sum();
                    if (dailyTotal > 0) {
                        item.setCaterPercent(BigDecimal.valueOf(entry.getValue().getOrDefault("餐饮", 0.0) / dailyTotal * 100)
                                .setScale(2, RoundingMode.HALF_UP));
                        item.setRetailPercent(BigDecimal.valueOf(entry.getValue().getOrDefault("零售", 0.0) / dailyTotal * 100)
                                .setScale(2, RoundingMode.HALF_UP));
                        item.setEntertainPercent(BigDecimal.valueOf(entry.getValue().getOrDefault("娱乐服务", 0.0) / dailyTotal * 100)
                                .setScale(2, RoundingMode.HALF_UP));
                    } else {
                        item.setCaterPercent(BigDecimal.ZERO);
                        item.setRetailPercent(BigDecimal.ZERO);
                        item.setEntertainPercent(BigDecimal.ZERO);
                    }
                    return item;
                })
                .collect(Collectors.toList());
        resp.setNationalTargetList(dailyExitList);

        // 7. 按城市+业态分组计算撤场面积（使用MultiRentAnalyseRespItem）
        List<EntProjectListVo> tenantIdList = getTenantIdList(req);
        Map<String, String> tenantNameCityMap = tenantIdList.stream()
                .collect(Collectors.toMap(EntProjectListVo::getName, EntProjectListVo::getFcity));

        List<MultiRentAnalyseRespItem> cityExitList = dataList.stream()
                .filter(vo -> tenantNameCityMap.containsKey(vo.getTenantName()))
                .collect(Collectors.groupingBy(
                        vo -> tenantNameCityMap.get(vo.getTenantName()),
                        Collectors.groupingBy(
                                ReportExitStatisListVo::getAdaptiveFormat,
                                Collectors.summingDouble(ReportExitStatisListVo::getActualArea)
                        )
                ))
                .entrySet().stream()
                .map(entry -> {
                    MultiRentAnalyseRespItem item = new MultiRentAnalyseRespItem();
                    item.setName(entry.getKey());

                    // 设置数值（面积）
                    item.setCaterValue((int)Math.round(entry.getValue().getOrDefault("餐饮", 0.0)));
                    item.setRetailValue((int)Math.round(entry.getValue().getOrDefault("零售", 0.0)));
                    item.setEntertainValue((int)Math.round(entry.getValue().getOrDefault("娱乐服务", 0.0)));

                    // 计算比例（当前城市的业态面积占比）
                    double cityTotal = entry.getValue().values().stream().mapToDouble(Double::doubleValue).sum();
                    if (cityTotal > 0) {
                        item.setCaterPercent(BigDecimal.valueOf(entry.getValue().getOrDefault("餐饮", 0.0) / cityTotal * 100)
                                .setScale(2, RoundingMode.HALF_UP));
                        item.setRetailPercent(BigDecimal.valueOf(entry.getValue().getOrDefault("零售", 0.0) / cityTotal * 100)
                                .setScale(2, RoundingMode.HALF_UP));
                        item.setEntertainPercent(BigDecimal.valueOf(entry.getValue().getOrDefault("娱乐服务", 0.0) / cityTotal * 100)
                                .setScale(2, RoundingMode.HALF_UP));
                    } else {
                        item.setCaterPercent(BigDecimal.ZERO);
                        item.setRetailPercent(BigDecimal.ZERO);
                        item.setEntertainPercent(BigDecimal.ZERO);
                    }
                    return item;
                })
                .collect(Collectors.toList());
        resp.setNationalCityTargetList(cityExitList);
        return resp;
    }


    public Map<String, Double> calculateCityAvgRentalRate(List<ReportOperateDayAnalyseListVo> dataList,LocalDate startDate,
                                                          LocalDate endDate,Map<String, String> tenantNameCityMap) {

        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyMap();
        }

        // 1. 生成日期范围内的所有日期
        List<LocalDate> dateRange = Stream.iterate(startDate, date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(startDate, endDate) + 1)
                .collect(Collectors.toList());

        // 2. 获取每个项目的总面积（取首次出现的值）
        Map<String, Double> projectAreaMap = dataList.stream()
                .collect(Collectors.toMap(
                        ReportOperateDayAnalyseListVo::getTenantName,
                        ReportOperateDayAnalyseListVo::getActualArea,
                        (existing, replacement) -> existing));

        // 3. 按项目分组，计算每个项目每天的roomArea总和
        Map<String, Map<LocalDate, Double>> projectDailyRoomAreaMap = dataList.stream()
                .collect(Collectors.groupingBy(
                        ReportOperateDayAnalyseListVo::getTenantName,
                        Collectors.groupingBy(
                                vo -> LocalDate.of(vo.getYear(), vo.getMonth(), vo.getDay()),
                                Collectors.summingDouble(ReportOperateDayAnalyseListVo::getRoomArea)
                        )
                ));

        // 4. 计算每个项目的平均出租率
        Map<String, Double> projectAvgRates = projectDailyRoomAreaMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            String tenantName = entry.getKey();
                            Map<LocalDate, Double> dailyRoomAreas = entry.getValue();
                            Double projectArea = projectAreaMap.get(tenantName);

                            if (projectArea == null || projectArea == 0) {
                                return Double.NaN;
                            }

                            return dateRange.stream()
                                    .mapToDouble(date -> {
                                        Double dailyRoomArea = dailyRoomAreas.getOrDefault(date, 0.0);
                                        return dailyRoomArea / projectArea;
                                    })
                                    .average()
                                    .orElse(Double.NaN);
                        }
                ));

        // 5. 按城市分组计算平均出租率
        Map<String, Double> cityAvgRates = projectAvgRates.entrySet().stream()
                .filter(entry -> !Double.isNaN(entry.getValue())) // 过滤无效项目
                .collect(Collectors.groupingBy(
                        entry -> tenantNameCityMap.get(entry.getKey()),
                        Collectors.collectingAndThen(
                                Collectors.mapping(Map.Entry::getValue, Collectors.toList()),
                                rates -> {
                                    if (rates.isEmpty()) return Double.NaN;
                                    return rates.stream()
                                            .mapToDouble(Double::doubleValue)
                                            .average()
                                            .orElse(Double.NaN);
                                }
                        )
                ));

        // 6. 处理结果（过滤NaN并格式化）
        return cityAvgRates.entrySet().stream()
                .filter(entry -> !Double.isNaN(entry.getValue()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> Math.round(entry.getValue() * 100 * 100.0) / 100.0
                ));
    }



    /**
     * 计算并合并同比环比数据
     * @param currentDataList 当前周期数据（包含date和rentRate）
     * @param yoyDataList 同比数据（去年同期的date和rentRate）
     * @param qoqDataList 环比数据（上个周期的date和rentRate）
     * @return 合并后的数据列表（包含rentRate、yoyRate和qoqRate）
     */
    private List<MultiRentAnalyseCityRespItem> calculateAndMergeDateRates(
            List<Map<String, Double>> currentDataList,
            List<Map<String, Double>> yoyDataList,
            List<Map<String, Double>> qoqDataList,
            MultiAnalyseReq req) {

        // 1. 将同比数据按日期建立映射
        Map<String, Double> yoyRateMap = yoyDataList.stream()
                .collect(Collectors.toMap(
                        map -> String.valueOf(map.get("date")),
                        map -> map.get("value"),
                        (v1, v2) -> v1));

        // 2. 将环比数据按日期建立映射
        Map<String, Double> qoqRateMap = qoqDataList.stream()
                .collect(Collectors.toMap(
                        map -> String.valueOf(map.get("date")),
                        map -> map.get("value"),
                        (v1, v2) -> v1));

        // 3. 计算周期天数用于环比计算
        long daysBetween = ChronoUnit.DAYS.between(req.getStartDate(), req.getEndDate()) + 1;

        // 4. 转换并计算数据
        return currentDataList.stream()
                .map(currentData -> {
                    MultiRentAnalyseCityRespItem item = new MultiRentAnalyseCityRespItem();
                    String currentDateStr = String.valueOf(currentData.get("date"));
                    LocalDate currentDate = LocalDate.parse(currentDateStr);
                    double currentRate = currentData.get("value");

                    // 设置基础数据
                    item.setName(currentDateStr);
                    item.setValue(String.format("%.2f", currentRate));

                    // 计算同比数据（去年同一天）
                    String yoyDateStr = currentDate.minusYears(1).toString();
                    double yoyRate = yoyRateMap.getOrDefault(yoyDateStr, 0.0);
                    if (yoyRate != 0) {
                        double yoyPercent = (currentRate - yoyRate) / yoyRate * 100;
                        item.setPariPassuValue(round(yoyRate, 2));
                        item.setPariPassuPercent(round(yoyPercent, 2));
                    }

                    // 计算环比数据（上个周期同一天）
                    String qoqDateStr = currentDate.minusDays(daysBetween).toString();
                    double qoqRate = qoqRateMap.getOrDefault(qoqDateStr, 0.0);
                    if (qoqRate != 0) {
                        double qoqPercent = (currentRate - qoqRate) / qoqRate * 100;
                        item.setRingThanValue(round(qoqRate, 2));
                        item.setRingThanPercent(round(qoqPercent, 2));
                    }

                    return item;
                })
                .collect(Collectors.toList());
    }

    private List<MultiRentAnalyseCityRespItem> calculateAndMergeComparisonRates(
            List<Map<String, Double>> currentDataList,
            List<Map<String, Double>> yoyDataList,
            List<Map<String, Double>> qoqDataList) {

        // 1. 将同比数据按name建立映射
        Map<String, Double> yoyAreaMap = yoyDataList.stream()
                .collect(Collectors.toMap(
                        map -> String.valueOf(map.get("name")),
                        map -> map.get("area"),
                        (v1, v2) -> v1));

        // 2. 将环比数据按name建立映射
        Map<String, Double> qoqAreaMap = qoqDataList.stream()
                .collect(Collectors.toMap(
                        map -> String.valueOf(map.get("name")),
                        map -> {
                            Object areaObj = map.get("area");
                            if (areaObj instanceof Number) {
                                return ((Number) areaObj).doubleValue();
                            } else if (areaObj instanceof String) {
                                try {
                                    return Double.parseDouble((String) areaObj);
                                } catch (NumberFormatException e) {
                                    return 0.0;
                                }
                            }
                            return 0.0;
                        },
                        (v1, v2) -> v1));

        // 3. 处理当前数据并计算同比环比
        return currentDataList.stream()
                .map(currentData -> {
                    MultiRentAnalyseCityRespItem item = new MultiRentAnalyseCityRespItem();
                    String name = String.valueOf(currentData.get("name"));

                    Object currentAreaObj = currentData.get("area");
                    double currentArea;
                    if (currentAreaObj instanceof Number) {
                        currentArea = ((Number) currentAreaObj).doubleValue();
                    } else if (currentAreaObj instanceof String) {
                        try {
                            currentArea = Double.parseDouble((String) currentAreaObj);
                        } catch (NumberFormatException e) {
                            currentArea = 0.0;
                        }
                    } else {
                        currentArea = 0.0;
                    }

                    // 设置基础数据
                    item.setName(name);
                    item.setValue(String.format("%.2f", currentArea));

                    // 计算同比
                    double yoyArea = yoyAreaMap.getOrDefault(name, 0.0);
                    if (yoyArea != 0) {
                        double yoyPercent = (currentArea - yoyArea) / yoyArea * 100;
                        item.setPariPassuValue(round(yoyArea, 2));
                        item.setPariPassuPercent(round(yoyPercent, 2));
                    } else {
                        item.setPariPassuValue(0.0);
                        item.setPariPassuPercent(0.0);
                    }

                    // 计算环比
                    double qoqArea = qoqAreaMap.getOrDefault(name, 0.0);
                    if (qoqArea != 0) {
                        double qoqPercent = (currentArea - qoqArea) / qoqArea * 100;
                        item.setRingThanValue(round(qoqArea, 2));
                        item.setRingThanPercent(round(qoqPercent, 2));
                    } else {
                        item.setRingThanValue(0.0);
                        item.setRingThanPercent(0.0);
                    }

                    return item;
                })
                .collect(Collectors.toList());
    }



    public List<MultiRentAnalyseRespDetail> processExitStatisData(
            List<Map<String, String>> currentData,
            List<Map<String, String>> pariData,
            List<Map<String, String>> ringDiffData,
            int gatherType,
            Map<String, String> tenantNameCityMap) {

        // 1. 处理当前数据（按城市汇总）
        Map<String, Double> currentCityMap = new HashMap<>();
        for (Map<String, String> item : currentData) {
            String city = tenantNameCityMap.getOrDefault(item.get("tenant_name"), "未知城市");
            String key = buildGroupKey(city, item, gatherType);
            double area = Double.parseDouble(String.valueOf(item.getOrDefault("actual_area", "0")));
            currentCityMap.merge(key, area, Double::sum);
        }

        // 2. 处理同比数据（按城市汇总）
        Map<String, Double> pariCityMap = new HashMap<>();
        for (Map<String, String> item : pariData) {
            String city = tenantNameCityMap.getOrDefault(item.get("tenant_name"), "未知城市");
            String key = buildGroupKey(city, item, gatherType);
            double area = Double.parseDouble(String.valueOf(item.getOrDefault("actual_area", "0")));
            pariCityMap.merge(key, area, Double::sum);
        }

        // 3. 处理环比数据（按城市汇总）
        Map<String, Double> ringDiffCityMap = new HashMap<>();
        for (Map<String, String> item : ringDiffData) {
            String city = tenantNameCityMap.getOrDefault(item.get("tenant_name"), "未知城市");
            String key = buildGroupKey(city, item, gatherType);
            double area = Double.parseDouble(String.valueOf(item.getOrDefault("actual_area", "0")));
            ringDiffCityMap.merge(key, area, Double::sum);
        }

        // 4. 构建最终结果，返回 List<MultiRentAnalyseRespDetail>
        List<MultiRentAnalyseRespDetail> result = new ArrayList<>();
        for (Map.Entry<String, Double> entry : currentCityMap.entrySet()) {
            String[] keys = entry.getKey().split("\\|");
            MultiRentAnalyseRespDetail item = new MultiRentAnalyseRespDetail();

            item.setCity(keys[0]);
            item.setCommercialTypeName(keys[1]);

            if (gatherType >= 1 && keys.length > 2) {
                item.setCategoryName(keys[2]);
            }
            if (gatherType >= 2 && keys.length > 3) {
                item.setSecondCategoryName(keys[3]);
            }

            Double currentArea = entry.getValue();
            Double pariArea = pariCityMap.getOrDefault(entry.getKey(), 0.0);
            Double ringDiffArea = ringDiffCityMap.getOrDefault(entry.getKey(), 0.0);

            item.setRate(BigDecimal.valueOf(currentArea));
            item.setRateYoy(calculateRate(currentArea, pariArea)); // 同比
            item.setRateQoq(calculateRate(currentArea, ringDiffArea)); // 环比

            result.add(item);
        }

        return result;
    }

    // 构建分组key
    private String buildGroupKey(String city, Map<String, String> item, int gatherType) {
        StringBuilder key = new StringBuilder(city);
        key.append("|").append(item.get("adaptive_format"));

        if (gatherType >= 1) {
            key.append("|").append(item.get("category_name"));
        }
        if (gatherType >= 2) {
            key.append("|").append(item.get("commercial_two"));
        }

        return key.toString();
    }

    // 计算比率
    private BigDecimal calculateRate(Double current, Double compare) {
        if (compare == 0.0) {
            return BigDecimal.valueOf(0);
        }
        return BigDecimal.valueOf((current - compare) / compare * 100).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    // 四舍五入辅助方法
    private double round(double value, int scale) {
        return BigDecimal.valueOf(value).setScale(scale, RoundingMode.HALF_UP).doubleValue();
    }
}
