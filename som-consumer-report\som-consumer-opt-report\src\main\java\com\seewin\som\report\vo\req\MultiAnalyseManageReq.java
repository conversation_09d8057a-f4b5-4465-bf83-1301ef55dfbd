package com.seewin.som.report.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
public class MultiAnalyseManageReq implements Serializable {
    /**
     * 查询类型: 0-全国 1-城市
     */
    @Schema(description = "查询类型: 0-全国 1-城市")
    private Integer searchType;
    /**
     * 时间类型: 0-日 1-月
     */
    @Schema(description = "时间类型: 0-日 1-月")
    private Integer timeType;
    /**
     * 指标类型: 0-租售比 1-销售转化率 2-销售坪效
     */
    @Schema(description = "指标类型: 0-租售比 1-销售转化率 2-销售坪效")
    private Integer indicatorType;
    /**
     * 汇总类型: 0-按业态汇总 1-按一级品类汇总 2-按二级品类汇总
     */
    @Schema(description = "汇总类型: 0-按业态汇总 1-按一级品类汇总 2-按二级品类汇总")
    private Integer gatherType;

    @Schema(description = "开始时间，格式yyyy-MM-dd")
    private LocalDate startDate;

    @Schema(description = "结束时间，格式yyyy-MM-dd")
    private LocalDate endDate;

    @Schema(description = "城市")
    private List<String> city;

    @Schema(description = "项目id")
    private List<Long> tenantId;

}
