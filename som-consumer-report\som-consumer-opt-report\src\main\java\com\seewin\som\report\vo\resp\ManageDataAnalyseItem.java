package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class ManageDataAnalyseItem implements Serializable {

    @Schema(description = "时间")
    private String dateStr;

    @Schema(description = "数值")
    private BigDecimal value;

    @Schema(description = "同比率")
    private BigDecimal pariPassuPercent;

    @Schema(description = "同比值")
    private BigDecimal pariPassuValue;

    @Schema(description = "环比率")
    private BigDecimal ringThanPercent;

    @Schema(description = "环比值")
    private BigDecimal ringThanValue;
}
