package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class MultiAnalyseFlowResp implements Serializable {

    @Schema(description = "总客流")
    private BigDecimal flowTotal;

    @Schema(description = "日均总客流")
    private BigDecimal flowTotalAvg;

    @Schema(description = "客流对比")
    private List<MultiAnalyseFlowRespChart> flowCompareList;

    @Schema(description = "客流分析")
    private List<FlowDataAnalyseItem> flowDataAnalyseItemList;

    @Schema(description = "客流明细")
    private List<MultiAnalyseFlowRespDetail> flowDetailList;

}
