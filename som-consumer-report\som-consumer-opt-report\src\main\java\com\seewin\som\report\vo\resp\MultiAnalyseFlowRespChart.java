package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class MultiAnalyseFlowRespChart implements Serializable {

    @Schema(description = "x轴名称")
    private String name;

    @Schema(description = "y轴数值")
    private BigDecimal value = BigDecimal.ZERO;

    @Schema(description = "占比")
    private BigDecimal percent = BigDecimal.ZERO;
}
