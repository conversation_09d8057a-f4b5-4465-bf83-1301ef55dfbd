package com.seewin.som.report.vo.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class MultiAnalyseManageDetail implements Serializable {

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 租户id(项目id)
     */
    @ExcelIgnore
    @Schema(description = "项目id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @ColumnWidth(25)
    @ExcelProperty("项目名称")
    @Schema(description = "项目名称")
    private String tenantName;


    /**
     * 业态
     */
    @ColumnWidth(25)
    @ExcelProperty("业态")
    @Schema(description = "业态")
    private String commercialTypeName;

    /**
     * 一级品类名称
     */
    @ColumnWidth(25)
    @ExcelProperty("一级品类名称")
    @Schema(description = "一级品类名称")
    private String categoryName;

    /**
     * 二级类品名称
     */
    @ColumnWidth(25)
    @ExcelProperty("二级类品名称")
    @Schema(description = "二级类品名称")
    private String commercialTwo;

    /**
     * 租售比
     */
    @ColumnWidth(25)
    @ExcelProperty("租售比")
    @Schema(description = "租售比")
    private BigDecimal total;

    /**
     * 租售比同比
     */
    @ColumnWidth(25)
    @ExcelProperty("同比")
    @NumberFormat("0.00")
    @Schema(description = "同比")
    private BigDecimal pariPassu;

    /**
     * 租售比环比
     */
    @ColumnWidth(25)
    @ExcelProperty("环比")
    @NumberFormat("0.00")
    @Schema(description = "环比")
    private BigDecimal ringThan;

}
