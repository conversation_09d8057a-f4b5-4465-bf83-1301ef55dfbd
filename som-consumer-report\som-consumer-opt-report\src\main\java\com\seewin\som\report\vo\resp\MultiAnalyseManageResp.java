package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class MultiAnalyseManageResp implements Serializable {

    @Schema(description = "全国")
    private BigDecimal total;

    @Schema(description = "同比")
    private BigDecimal pariPassu;

    @Schema(description = "环比")
    private BigDecimal ringThan;

    @Schema(description = "全国分析")
    private List<MultiAnalyseManageRespItem> countryAnalyseList;

    @Schema(description = "全国各城市分析")
    private List<MultiAnalyseManageRespItem> countryCityAnalyseList;

    @Schema(description = "城市各项目分析")
    private List<ManageDataAnalyseItem> cityProjectAnalyseList;

    @Schema(description = "城市分析")
    private List<ManageDataAnalyseItem> cityAnalyseList;

    @Schema(description = "明细")
    private List<MultiAnalyseManageDetail> detailList;

}
