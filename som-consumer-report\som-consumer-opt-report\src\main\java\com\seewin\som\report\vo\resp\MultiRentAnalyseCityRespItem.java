package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MultiRentAnalyseCityRespItem {

        /**
         * x轴名称
         */
        @Schema(description = "x轴名称")
        private String name;

        /**
         * 数值
         */
        @Schema(description = "数值")
        private String value;


        /**
         * 同比率
         */
        @Schema(description = "同比率")
        private Double pariPassuPercent;

        /**
         * 同比率数值
         */
        @Schema(description = "同比率数值")
        private Double pariPassuValue;


        /**
         * 环比率
         */
        @Schema(description = "环比率")
        private Double ringThanPercent;


        /**
         * 环比率数值
         */
        @Schema(description = "环比率数值")
        private Double ringThanValue;
}
