package com.seewin.som.report.vo.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class MultiRentAnalyseDetail implements Serializable {


    /**
     * 城市
     */
    @ColumnWidth(25)
    @ExcelProperty("城市")
    @Schema(description = "城市")
    private String city;


    /**
     * 业态
     */
    @ColumnWidth(25)
    @ExcelProperty("业态")
    @Schema(description = "业态")
    private String commercialTypeName;

    /**
     * 一级品类名称
     */
    @ColumnWidth(25)
    @ExcelProperty("一级品类名称")
    @Schema(description = "一级品类名称")
    private String categoryName;

    /**
     * 二级类品名称
     */
    @ColumnWidth(25)
    @ExcelProperty("二级类品名称")
    @Schema(description = "二级类品名称")
    private String secondCategoryName;

    /**
     * 出租率
     */
    @ColumnWidth(25)
    @ExcelProperty("出租率")
    @Schema(description = "出租率")
    private BigDecimal rate;

    /**
     * 出租率同比
     */
    @ColumnWidth(25)
    @ExcelProperty("出租率同比")
    @Schema(description = "出租率同比")
    private BigDecimal rateYoy;


    /**
     * 出租率环比
     */
    @ColumnWidth(25)
    @ExcelProperty("出租率环比")
    @Schema(description = "出租率环比")
    private BigDecimal rateQoq;


}
