package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class MultiRentAnalyseResp implements Serializable {

    @Schema(description = "比率")
    private BigDecimal rate;

    @Schema(description = "比率同比")
    private BigDecimal rateYoy;

    @Schema(description = "比率环比")
    private BigDecimal rateQoq;

    @Schema(description = "全国指标分析")
    private List<MultiRentAnalyseRespItem> nationalTargetList;

    @Schema(description = "全国各城市指标分析")
    private List<MultiRentAnalyseRespItem> nationalCityTargetList;

    @Schema(description = "各城市指标分析")
    private List<MultiRentAnalyseCityRespItem> cityTargetList;

    @Schema(description = "城市每日指标分析")
    private List<MultiRentAnalyseCityRespItem> cityDateTargetList;

    @Schema(description = "指标明细")
    private List<MultiRentAnalyseRespDetail> rentDetailList;

}
