package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class MultiRentAnalyseRespDetail implements Serializable {

    @Schema(description = "城市")
    private String city;

    @Schema(description = "业态")
    private String commercialTypeName;

    @Schema(description = "一级品类")
    private String categoryName;

    @Schema(description = "二级品类")
    private String secondCategoryName;

    @Schema(description = "比率")
    private BigDecimal rate;

    @Schema(description = "同比")
    private BigDecimal rateYoy;

    @Schema(description = "环比")
    private BigDecimal rateQoq;



}
