package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class MultiRentAnalyseRespItem implements Serializable {

    @Schema(description = "x轴名称")
    private String name;
    /**
     * y轴餐饮数值
     */
    @Schema(description = "y轴餐饮数值")
    private BigDecimal caterValue;
    /**
     * y轴餐饮比例
     */
    @Schema(description = "y轴餐饮比例")
    private BigDecimal caterPercent;
    /**
     * y轴零售数值
     */
    @Schema(description = "y轴零售数值")
    private BigDecimal retailValue;
    /**
     * y轴零售比例
     */
    @Schema(description = "y轴零售比例")
    private BigDecimal retailPercent;
    /**
     * y轴娱乐数值
     */
    @Schema(description = "y轴娱乐数值")
    private BigDecimal entertainValue;
    /**
     * y轴娱乐比例
     */
    @Schema(description = "y轴娱乐比例")
    private BigDecimal entertainPercent;

    /**
     * 数值
     */
    @Schema(description = "数值")
    private BigDecimal value;

}
