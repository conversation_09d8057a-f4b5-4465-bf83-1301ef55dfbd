package com.xxl.job.executor.service.jobhandler;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.seewin.som.commerce.provider.CommerceContractProvider;
import com.seewin.som.commerce.provider.CommercialCategoryProvider;
import com.seewin.som.commerce.req.CommercialCategoryListDto;
import com.seewin.som.commerce.resp.CommerceInvertContractListVo;
import com.seewin.som.commerce.resp.CommercialCategoryListVo;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.resp.EntProjectListVo;
import com.seewin.som.report.provider.*;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.ReportFeeListVo;
import com.seewin.som.report.resp.ReportOperateDayListVo;
import com.seewin.som.report.resp.ReportProfitBusinessOverviewListVo;
import com.seewin.som.report.resp.ReportSaleAndFlowListVo;
import com.seewin.util.bean.BeanUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.executor.service.resp.AnalyseResp;
import com.xxl.job.executor.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class InvertMarketJob {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ChartHttpClient chartHttpClient;
    @DubboReference(providedBy = "som-report-mgt")
    private ReportOperateDayProvider operateDayProvider;


    @DubboReference(providedBy = "som-commerce-mgt")
    private CommercialCategoryProvider categoryProvider;
    @DubboReference(providedBy = "som-report-mgt")
    private ReportInvertMarketProvider invertMarketProvider;


    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractProvider contractProvider;

    @DubboReference(providedBy = "som-report-mgt")
    private ReportProfitBusinessOverviewProvider profitBusinessOverviewProvider;

    @XxlJob("createInvestMarket")
    public void createInvestMarket() {
        log.info("investMarketTest...");
        //获取任务参数  参数格式为时间    todo  修改定时任务获取数据逻辑。 取前一天数据插入。
        String command = XxlJobHelper.getJobParam();
        LocalDate localDate = LocalDate.now();
        try {
            if (StringUtils.isNotBlank(command)) {
                localDate = LocalDate.parse(command);
            }
        } catch (DateTimeParseException ex) {
            log.info("创建市场报告，入参解析异常");
            localDate = LocalDate.now();
        }
        // 判断是否是周一
        if (localDate.getDayOfWeek() == DayOfWeek.MONDAY) {
            // 获取上周的日期范围
            LocalDate[] weekDateRange = DateUtil.getLastWeekDateRange(localDate);
            log.info("当前是周一，执行上周数据统计，日期范围：{} 至 {}", weekDateRange[0], weekDateRange[1]);
            init(weekDateRange[0], weekDateRange[1]);
        }

        // 判断是否是1号
        if (localDate.getDayOfMonth() == 1) {
            // 获取上个月的日期范围
            LocalDate[] monthDateRange = DateUtil.getLastMonthDateRange(localDate);
            log.info("当前是1号，执行上月数据统计，日期范围：{} 至 {}", monthDateRange[0], monthDateRange[1]);
            init(monthDateRange[0], monthDateRange[1]);
        }

        // 如果既不是周一也不是1号，则返回
        if (localDate.getDayOfWeek() != DayOfWeek.MONDAY && localDate.getDayOfMonth() != 1) {
            log.info("当前日期既不是周一也不是1号，不执行任务");
            return;
        }
    }

    private void init(LocalDate startDate, LocalDate endDate) {
        //先删除数据
        ReportInvertMarketListDto delDto = new ReportInvertMarketListDto();
        delDto.setStartDate(startDate);
        delDto.setEndDate(endDate);
        invertMarketProvider.delete(delDto);
        //同比日期
        LocalDate[] yearOnYearDates = DateUtil.getYearOnYearDateRange(startDate, endDate);
        LocalDate yearOnYearStartDate = yearOnYearDates[0];
        LocalDate yearOnYearEndDate = yearOnYearDates[1];
        //环比日期
        LocalDate[] monthOnMonthDates = DateUtil.getMonthOnMonthDateRange(startDate, endDate);
        LocalDate monthOnMonthStartDate = monthOnMonthDates[0];
        LocalDate monthOnMonthEndDate = monthOnMonthDates[1];
        //日期范围
        List<LocalDate> dateRangeList = DateUtil.getDateRangeList(startDate, endDate);
        List<LocalDate> yearOnYearDateRangeList = DateUtil.getDateRangeList(yearOnYearStartDate, yearOnYearEndDate);
        List<LocalDate> monthOnMonthDateRangeList = DateUtil.getDateRangeList(monthOnMonthStartDate, monthOnMonthEndDate);
        //所有合同数据
        List<CommerceInvertContractListVo> invertContractList = contractProvider.getAllInvertContract();
        //业态洞察
        List<CommercialCategoryListVo> commercialTypeCodeList = getCategoryList(0);
        //当前日期数据每个店铺的数据包含在营和空置
        List<ReportOperateDayListVo> operateDayList = getOperateDay(startDate, endDate);
        fillContractInfoToOperateDayList(operateDayList, invertContractList);
        List<AnalyseResp> analyseRespList = businessTypeResult(operateDayList, commercialTypeCodeList, dateRangeList,startDate, endDate);
        //同比数据
        List<ReportOperateDayListVo> yearOnYearOperateDayList = getOperateDay(yearOnYearStartDate, yearOnYearEndDate);
        fillContractInfoToOperateDayList(yearOnYearOperateDayList, invertContractList);
        List<AnalyseResp> yearOnYearAnalyseRespList = businessTypeResult(yearOnYearOperateDayList, commercialTypeCodeList, yearOnYearDateRangeList,yearOnYearStartDate, yearOnYearEndDate);
        //环比数据
        List<ReportOperateDayListVo> monthOnMonthOperateDayList = getOperateDay(monthOnMonthStartDate, monthOnMonthEndDate);
        fillContractInfoToOperateDayList(monthOnMonthOperateDayList, invertContractList);
        List<AnalyseResp> monthOnMonthAnalyseRespList = businessTypeResult(monthOnMonthOperateDayList, commercialTypeCodeList, monthOnMonthDateRangeList,monthOnMonthStartDate, monthOnMonthEndDate);

        // 合并当前数据、同比数据和环比数据
        List<AnalyseResp> mergedAnalyseRespList = mergeAnalyseData(analyseRespList, yearOnYearAnalyseRespList, monthOnMonthAnalyseRespList);
        //品类洞察
        List<CommercialCategoryListVo> categoryList = getCategoryList(1);
        List<AnalyseResp> categoryResultList = categoryResult(operateDayList, categoryList, dateRangeList, startDate, endDate);
        //品类同比数据
        List<AnalyseResp> yearOnYearCategoryResultList = categoryResult(yearOnYearOperateDayList, categoryList, yearOnYearDateRangeList,yearOnYearStartDate,yearOnYearEndDate);
        //品类环比数据
        List<AnalyseResp> monthOnMonthCategoryResultList = categoryResult(monthOnMonthOperateDayList, categoryList, monthOnMonthDateRangeList,monthOnMonthStartDate,monthOnMonthEndDate);

        // 合并品类洞察的当前数据、同比数据和环比数据
        List<AnalyseResp> mergedCategoryResultList = mergeAnalyseData(categoryResultList, yearOnYearCategoryResultList, monthOnMonthCategoryResultList);
        List<ReportInvertMarketAddDto> result = new ArrayList<>();
        // 业态洞察数据入库
        for (AnalyseResp analyseResp : mergedAnalyseRespList) {
            ReportInvertMarketAddDto dto = BeanUtils.copyProperties(analyseResp, ReportInvertMarketAddDto.class);
            result.add(dto);
            dto.setIndicatorType(analyseResp.getIndicatorId());
            dto.setCurrentPeriodData(analyseResp.getCurrentValue());
            dto.setStartDate(startDate);
            dto.setEndDate(endDate);
            dto.setDataType(0);

            // 统计学指标
            List<BigDecimal> list = analyseResp.getList();
            List<AnalyseResp.Item> objList = analyseResp.getObjList();
            dto.setMeanValue(StatisticsUtil.calculateMean(list));
            dto.setTrimmedMean(StatisticsUtil.calculateTrimmedMean(list));
            dto.setMedian(StatisticsUtil.calculateMedian(list));
            dto.setMostFrequentValue(StatisticsUtil.calculateMostFrequentValue(list));
            dto.setStandardDeviation(StatisticsUtil.calculateStandardDeviation(list));
            dto.setVariance(StatisticsUtil.calculateVariance(list));
            dto.setMedianAbsoluteDeviation(StatisticsUtil.calculateMedianAbsoluteDeviation(list));
            dto.setDataRange(StatisticsUtil.calculateDataRange(list));
            dto.setInterquartileRange(StatisticsUtil.calculateInterquartileRange(list));
            dto.setFirstQuartile(StatisticsUtil.calculateFirstQuartile(list));
            dto.setThirdQuartile(StatisticsUtil.calculateThirdQuartile(list));

            // 同比率
            if (analyseResp.getYearOnYearData() != null && analyseResp.getYearOnYearData().compareTo(BigDecimal.ZERO) != 0) {
                dto.setYearOnYearRate(
                        dto.getCurrentPeriodData().subtract(analyseResp.getYearOnYearData())
                                .divide(analyseResp.getYearOnYearData(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"))
                );
            } else {
                dto.setYearOnYearRate(BigDecimal.ZERO);
            }

            // 环比率
            if (analyseResp.getMonthOnMonthData() != null && analyseResp.getMonthOnMonthData().compareTo(BigDecimal.ZERO) != 0) {
                dto.setMonthOnMonthRate(
                        dto.getCurrentPeriodData().subtract(analyseResp.getMonthOnMonthData())
                                .divide(analyseResp.getMonthOnMonthData(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"))
                );
            } else {
                dto.setMonthOnMonthRate(BigDecimal.ZERO);
            }
            //根据list 和指标 生成对应的可视化图表数据
            ChartDataDto chartDataDto=null;
            if(analyseResp.getIndicator()=="一级品类净利润"||analyseResp.getIndicator().contains("招商周期")){
                chartDataDto = convertToChartData(objList, analyseResp.getIndicator());
            }else {
                chartDataDto = convertToChartData(list, dateRangeList, analyseResp.getIndicator());
            }
            ChartHttpClient.ChartData chartData = chartHttpClient.generateChart(chartDataDto);
            try {
                dto.setChartData(objectMapper.writeValueAsString(chartData));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }

        // 品类洞察数据入库
        for (AnalyseResp analyseResp : mergedCategoryResultList) {
            ReportInvertMarketAddDto dto = BeanUtils.copyProperties(analyseResp, ReportInvertMarketAddDto.class);
            result.add(dto);
            dto.setIndicatorType(analyseResp.getIndicatorId());
            dto.setCurrentPeriodData(analyseResp.getCurrentValue());
            dto.setStartDate(startDate);
            dto.setEndDate(endDate);
            dto.setDataType(1);

            // 统计学指标
            List<BigDecimal> list = analyseResp.getList();
            List<AnalyseResp.Item> objList = analyseResp.getObjList();
            dto.setMeanValue(StatisticsUtil.calculateMean(list));
            dto.setTrimmedMean(StatisticsUtil.calculateTrimmedMean(list));
            dto.setMedian(StatisticsUtil.calculateMedian(list));
            dto.setMostFrequentValue(StatisticsUtil.calculateMostFrequentValue(list));
            dto.setStandardDeviation(StatisticsUtil.calculateStandardDeviation(list));
            dto.setVariance(StatisticsUtil.calculateVariance(list));
            dto.setMedianAbsoluteDeviation(StatisticsUtil.calculateMedianAbsoluteDeviation(list));
            dto.setDataRange(StatisticsUtil.calculateDataRange(list));
            dto.setInterquartileRange(StatisticsUtil.calculateInterquartileRange(list));
            dto.setFirstQuartile(StatisticsUtil.calculateFirstQuartile(list));
            dto.setThirdQuartile(StatisticsUtil.calculateThirdQuartile(list));

            // 同比率
            if (analyseResp.getYearOnYearData() != null && analyseResp.getYearOnYearData().compareTo(BigDecimal.ZERO) != 0) {
                dto.setYearOnYearRate(
                        dto.getCurrentPeriodData().subtract(analyseResp.getYearOnYearData())
                                .divide(analyseResp.getYearOnYearData(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"))
                );
            } else {
                dto.setYearOnYearRate(BigDecimal.ZERO);
            }

            // 环比率
            if (analyseResp.getMonthOnMonthData() != null && analyseResp.getMonthOnMonthData().compareTo(BigDecimal.ZERO) != 0) {
                dto.setMonthOnMonthRate(
                        dto.getCurrentPeriodData().subtract(analyseResp.getMonthOnMonthData())
                                .divide(analyseResp.getMonthOnMonthData(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"))
                );
            } else {
                dto.setMonthOnMonthRate(BigDecimal.ZERO);
            }
            //根据list 和指标 生成对应的可视化图表数据
            ChartDataDto chartDataDto = null;
            if (analyseResp.getIndicator() == "一级品类净利润" || analyseResp.getIndicator().contains("招商周期")) {
                chartDataDto = convertToChartData(objList, analyseResp.getIndicator());
            } else {
                chartDataDto = convertToChartData(list, dateRangeList, analyseResp.getIndicator());
            }
            ChartHttpClient.ChartData chartData = chartHttpClient.generateChart(chartDataDto);
            try {
                dto.setChartData(objectMapper.writeValueAsString(chartData));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        // 业态洞察数据入库前，生成AI解读
        generateInterpretationForGroups(result);
        for (ReportInvertMarketAddDto dto : result) {
            invertMarketProvider.add(dto);
        }
    }

    /**
     * 业态占比   每组业态都有占比。
     *
     * @param operateDayList
     * @param commercialTypeCodeList
     * @param dateRangeList
     */
    private List<AnalyseResp> businessTypeResult(List<ReportOperateDayListVo> operateDayList, List<CommercialCategoryListVo> commercialTypeCodeList, List<LocalDate> dateRangeList,LocalDate startDate, LocalDate endDate) {
        List<AnalyseResp> result = new ArrayList<>();
        //在营数据
        List<ReportOperateDayListVo> operateDayListByCamp = operateDayList.stream().filter(entity ->
                entity.getStatus() == 2 && entity.getCommercialTypeCode() != null && entity.getCommercialTypeName() != null).collect(Collectors.toList());
        //获取每日日期对应的指标数据
        //获取所有业态类型
        for (CommercialCategoryListVo commercialTypeCode : commercialTypeCodeList) {
            AnalyseResp itemCamp = createAnalyseResp(0, "业态占比", "某一业态在营商户数/全量在营商户数（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> list = new ArrayList<>();
            itemCamp.setList(list);
            //在营门前客流
            AnalyseResp frontCountCamp = createAnalyseResp(2, "门前客流", "某一业态在营商户门前客流总数之和（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> frontCountList = new ArrayList<>();
            long totalFrontCount = operateDayListByCamp.stream().filter(e -> e.getCommercialTypeCode().equals(commercialTypeCode.getId()) && e.getFrontCount() != null).mapToLong(e -> e.getFrontCount()).sum();
            frontCountCamp.setCurrentValue(BigDecimal.valueOf(totalFrontCount));
            frontCountCamp.setList(frontCountList);
            //进店客流
            AnalyseResp inShopCountCamp = createAnalyseResp(3, "进店客流", "某一业态在营商户进店客流总数之和（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> inShopCountList = new ArrayList<>();
            long inShopFrontCount = operateDayListByCamp.stream().filter(e -> e.getCommercialTypeCode().equals(commercialTypeCode.getId()) && e.getInShopCount() != null).mapToLong(e -> e.getInShopCount()).sum();
            inShopCountCamp.setCurrentValue(BigDecimal.valueOf(inShopFrontCount));
            inShopCountCamp.setList(inShopCountList);

            //门前客流成本
            AnalyseResp frontCountCost = createAnalyseResp(4, "门前客流流量成本", "某一业态在营商户的租管费之和/门前客流总数之和（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> frontCountCostList = new ArrayList<>();
            frontCountCost.setList(frontCountCostList);


            //总销售笔数
            Integer totalOrderSum = operateDayListByCamp.stream().filter(e -> e.getCommercialTypeCode().equals(commercialTypeCode.getId()) && e.getTotalOrder() != null).mapToInt(e -> e.getTotalOrder()).sum();
            //门前客流销售转化率
            AnalyseResp frontCountSalesConvertRate = createAnalyseResp(5, "门前客流销售转化率", "某一业态在营商户的租管费之和/门前客流总数之和（静态数值、同环比趋势）",
                    commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            BigDecimal frontCountSumTemp = BigDecimalUtil.divide(BigDecimal.valueOf(totalFrontCount), BigDecimal.valueOf(1000));
            frontCountSalesConvertRate.setCurrentValue(BigDecimalUtil.divide(BigDecimal.valueOf(totalOrderSum), frontCountSumTemp));
            List<BigDecimal> frontCountSalesConvertRateList = new ArrayList<>();
            frontCountSalesConvertRate.setList(frontCountSalesConvertRateList);

            //业态门店出租率
            AnalyseResp rentRateCamp = createAnalyseResp(14, "业态门店出租率", "某一业态全量在营门店数/门店总数（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> rentRateList = new ArrayList<>();
            rentRateCamp.setList(rentRateList);

            //业态招商周期
            AnalyseResp investCycleCamp = createAnalyseResp(15, "业态招商周期", "某一业态新签合同（合同签约日期-带看日期）（静态数值、季度同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<AnalyseResp.Item> investCycleObjList = new ArrayList<>();
            List<BigDecimal> investCycleList  = new ArrayList<>();
            // 设置招商周期的当前值为平均值
            BigDecimal avgInvestCycle = BigDecimal.ZERO;

            investCycleCamp.setObjList(investCycleObjList);
            investCycleCamp.setList(investCycleList);
            List<ReportOperateDayListVo> operateDaynvestCycleList = operateDayListByCamp.stream().filter(entity -> entity.getVisitDate()!=null&&entity.getCommercialTypeCode().equals(commercialTypeCode.getId()) && DateUtil.isDateInRange(entity.getRentStartDate(), startDate, endDate))
                    .collect(Collectors.toMap(
                            vo -> vo.getTenantId() + "_" + vo.getStoreId(), // 以项目ID+门店ID为key
                            vo -> vo,
                            (existing, replacement) -> existing // 有重复时保留第一个
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            //计算平均值

            if (!operateDaynvestCycleList.isEmpty()) {
                // 计算招商周期的平均值
                BigDecimal totalInvestCycle = operateDaynvestCycleList.stream()
                        .filter(vo -> vo.getInvestCycle() > 0) // 过滤掉无效的招商周期数据
                        .map(vo -> BigDecimal.valueOf(vo.getInvestCycle()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                long validCount = operateDaynvestCycleList.stream()
                        .filter(vo -> vo.getInvestCycle() > 0)
                        .count();

                if (validCount > 0) {
                    avgInvestCycle = totalInvestCycle.divide(BigDecimal.valueOf(validCount), 2, RoundingMode.HALF_UP);
                }

                // 将项目名称+门店作为key，招商周期作为value添加到investCycleObjList中
                for (ReportOperateDayListVo vo : operateDaynvestCycleList) {
                    if (vo.getInvestCycle() > 0) {
                        AnalyseResp.Item item = new AnalyseResp.Item();
                        String key = vo.getTenantName() + "_" + vo.getStoreId();
                        item.setKey(key);
                        item.setValue(BigDecimal.valueOf(vo.getInvestCycle()));
                        investCycleObjList.add(item);
                        investCycleList.add(item.getValue());
                    }
                }
            }
            investCycleCamp.setCurrentValue(avgInvestCycle);
            //品牌直营占比
            AnalyseResp brandDirectSalesCamp = createAnalyseResp(16, "品牌直营占比", "某一业态在营商户的品牌直营店铺总数/该业态在营店铺总数（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> brandDirectSalesList = new ArrayList<>();
            brandDirectSalesCamp.setList(brandDirectSalesList);
            //品牌代理占比
            AnalyseResp brandAgentSalesCamp = createAnalyseResp(17, "品牌代理占比", "某一业态在营商户的品牌代理店铺总数/该业态在营店铺总数（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> brandAgentSalesList = new ArrayList<>();
            brandAgentSalesCamp.setList(brandAgentSalesList);
            //品牌加盟占比
            AnalyseResp brandJoinSalesCamp = createAnalyseResp(18, "品牌加盟占比", "某一业态在营商户的品牌加盟店铺总数/该业态在营店铺总数（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> brandJoinSalesList = new ArrayList<>();
            brandJoinSalesCamp.setList(brandJoinSalesList);
            //个体经营占比
            AnalyseResp individualSalesCamp = createAnalyseResp(19, "个体经营占比", "某一业态在营商户的个体经营店铺总数/该业态在营店铺总数（静态数值、同环比趋势）"
                    , commercialTypeCode.getId(), commercialTypeCode.getCategoryName());
            List<BigDecimal> individualSalesList = new ArrayList<>();
            individualSalesCamp.setList(individualSalesList);
            //某业态的在营
            List<ReportOperateDayListVo> campList = operateDayListByCamp.stream().filter(entity -> entity.getCommercialTypeCode().equals(commercialTypeCode.getId())).collect(Collectors.toList());
            for (int i = 0; i < dateRangeList.size(); i++) {
                //在营业态日期对应的铺位数据。
                LocalDate localDate = dateRangeList.get(i);
                long count = campList.stream().filter(e -> e.getDate().equals(localDate)).count();
                long totalCount = operateDayListByCamp.stream().filter(e -> e.getDate().equals(localDate)).count();
                //业态占比
                BigDecimal currentValue = BigDecimalUtil.divide(BigDecimal.valueOf(count), BigDecimal.valueOf(totalCount));

                //日期对应的门前客流
                long frontCount = campList.stream().filter(e -> e.getDate().equals(localDate) && e.getFrontCount() != null).mapToLong(e -> e.getFrontCount()).sum();
                //日期对应的进店客流
                long inShopCount = campList.stream().filter(e -> e.getDate().equals(localDate) && e.getInShopCount() != null).mapToLong(e -> e.getInShopCount()).sum();
                Integer totalOrder = campList.stream().filter(e -> e.getDate().equals(localDate) && e.getTotalOrder() != null).mapToInt(e -> e.getTotalOrder()).sum();

                //租管费
                BigDecimal theoryRent = campList.stream()
                        .filter(e -> e.getDate().equals(localDate) && e.getTheoryRent() != null)
                        .map(ReportOperateDayListVo::getTheoryRent)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //日期对应所有的铺位数
                long totalCountByAll = operateDayList.stream().filter(e -> e.getDate().equals(localDate)).count();
                //出租率
                BigDecimal rentRate =BigDecimalUtil.divide(BigDecimal.valueOf(count), BigDecimal.valueOf(totalCountByAll));
                //直营占比
                long brandDirectSalesCount=campList.stream().filter(e -> e.getDate().equals(localDate)&&e.getBusinessType()!=null&&e.getBusinessType()==1).count();
                BigDecimal brandDirectSalesRate = BigDecimalUtil.divide(BigDecimal.valueOf(brandDirectSalesCount), BigDecimal.valueOf(count));
                //代理占比
                long brandAgentSalesCount=campList.stream().filter(e -> e.getDate().equals(localDate)&&e.getBusinessType()!=null&&e.getBusinessType()==2).count();
                BigDecimal brandAgentSalesRate = BigDecimalUtil.divide(BigDecimal.valueOf(brandAgentSalesCount), BigDecimal.valueOf(count));
                //加盟占比
                long brandJoinSalesCount=campList.stream().filter(e -> e.getDate().equals(localDate)&&e.getBusinessType()!=null&&e.getBusinessType()==3).count();
                BigDecimal brandJoinSalesRate = BigDecimalUtil.divide(BigDecimal.valueOf(brandJoinSalesCount), BigDecimal.valueOf(count));
                //个体经营占比
                long individualSalesCount=campList.stream().filter(e -> e.getDate().equals(localDate)&&e.getBusinessType()!=null&&e.getBusinessType()==4).count();
                BigDecimal individualSalesRate = BigDecimalUtil.divide(BigDecimal.valueOf(individualSalesCount), BigDecimal.valueOf(count));
                //最后一天的数据作为当前数据

                if (i == dateRangeList.size() - 1) {
                    itemCamp.setCurrentValue(currentValue);
                    //门前客流流量成本
                    frontCountCost.setCurrentValue(BigDecimalUtil.divide(theoryRent, BigDecimal.valueOf(totalFrontCount)));
                    //业态出租率
                    rentRateCamp.setCurrentValue(rentRate);
                    brandDirectSalesCamp.setCurrentValue(brandDirectSalesRate);
                    brandAgentSalesCamp.setCurrentValue(brandAgentSalesRate);
                    brandJoinSalesCamp.setCurrentValue(brandJoinSalesRate);
                    individualSalesCamp.setCurrentValue(individualSalesRate);
                }
                list.add(currentValue);
                frontCountList.add(BigDecimal.valueOf(frontCount));
                inShopCountList.add(BigDecimal.valueOf(inShopCount));
                //用的日均租管费得出的 每日的门前客流流量成本
                //除以1000的门前客流/千人
                BigDecimal frontTemp = BigDecimalUtil.divide(BigDecimal.valueOf(frontCount), BigDecimal.valueOf(1000));
                frontCountCostList.add(BigDecimalUtil.divide(BigDecimalUtil.divide(theoryRent, BigDecimal.valueOf(dateRangeList.size())), frontTemp));
                frontCountSalesConvertRateList.add(BigDecimalUtil.divide(BigDecimal.valueOf(totalOrder), frontTemp));
                rentRateList.add(rentRate);
                brandDirectSalesList.add(brandDirectSalesRate);
                brandAgentSalesList.add(brandAgentSalesRate);
                brandJoinSalesList.add(brandJoinSalesRate);
                individualSalesList.add(individualSalesRate);
            }
            result.add(itemCamp);
            result.add(frontCountCamp);
            result.add(inShopCountCamp);
            result.add(frontCountCost);
            result.add(frontCountSalesConvertRate);
            result.add(rentRateCamp);
            result.add(investCycleCamp);
            result.add(brandDirectSalesCamp);
            result.add(brandAgentSalesCamp);
            result.add(brandJoinSalesCamp);
            result.add(individualSalesCamp);
        }
        return result;
    }

    /**
     * 品类洞察  。
     *
     * @param operateDayList
     * @param categoryList
     * @param dateRangeList
     */
    private List<AnalyseResp> categoryResult(List<ReportOperateDayListVo> operateDayList, List<CommercialCategoryListVo> categoryList, List<LocalDate> dateRangeList,LocalDate startDate, LocalDate endDate) {
        List<AnalyseResp> result = new ArrayList<>();
        //在营数据
        List<ReportOperateDayListVo> operateDayListByCamp = operateDayList.stream().filter(entity ->
                entity.getStatus() == 2 && entity.getCategoryId() != null && entity.getCategoryName() != null).collect(Collectors.toList());
        //获取每日日期对应的指标数据
        //获取所有业态类型
        for (CommercialCategoryListVo category : categoryList) {
            String[] tempArr = category.getParentFname().split("/");
            //总销售额
            BigDecimal totalAmountSum = operateDayListByCamp.stream()
                    .filter(e -> e.getCategoryId().equals(category.getId()) && e.getTotalAmount() != null)
                    .map(ReportOperateDayListVo::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            //业态在营对应的总铺位数
            List<ReportOperateDayListVo> CommercialTypeCodeList = operateDayList.stream().filter(entity ->
                    entity.getStatus() == 2 && entity.getCommercialTypeCode() != null && entity.getCommercialTypeCode().equals(category.getParentId())).collect(Collectors.toList());

            //门前客流
            long totalFrontCount = operateDayListByCamp.stream().filter(e -> e.getCategoryId().equals(category.getId()) && e.getFrontCount() != null).mapToLong(e -> e.getFrontCount()).sum();
            BigDecimal frontCountSumTemp = BigDecimalUtil.divide(BigDecimal.valueOf(totalFrontCount), BigDecimal.valueOf(1000));

            //进店客流
            long inShopFrontCount = operateDayListByCamp.stream().filter(e -> e.getCategoryId().equals(category.getId()) && e.getInShopCount() != null).mapToLong(e -> e.getInShopCount()).sum();

            //总销售笔数
            Integer totalOrderSum = operateDayListByCamp.stream().filter(e -> e.getCategoryId().equals(category.getId()) && e.getTotalOrder() != null).mapToInt(e -> e.getTotalOrder()).sum();

            //一级品类竞争度
            AnalyseResp itemCamp = createAnalyseResp(1, "一级品类竞争度", "某一级品类门店数/餐饮业态门店总数（静态数值、季度同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> list = new ArrayList<>();
            itemCamp.setList(list);

            //一级品类租售比
            AnalyseResp rentCamp = createAnalyseResp(6, "一级品类租售比", "某一级品类在营商户的租管费之和/销售总额之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> rentList = new ArrayList<>();
            rentCamp.setList(rentList);

            //一级品类租管费单价
            AnalyseResp rentPrice = createAnalyseResp(7, "一级品类租管费单价", "某一级品类在营商户租管费之和/实用面积之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> rentPriceList = new ArrayList<>();
            rentPrice.setList(rentPriceList);

            //一级品类销售坪效
            AnalyseResp saleEfficiency = createAnalyseResp(8, "一级品类销售坪效", "某一级品类在营商户销售额之和/实用面积之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> saleEfficiencyList = new ArrayList<>();
            saleEfficiency.setList(saleEfficiencyList);

            //一级品类门前客流销售转化率
            AnalyseResp frontCountSalesConvertRate = createAnalyseResp(9, "一级品类门前客流销售转化率", "某一级品类在营商户的销售笔数之和/门前客流总数之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> frontCountSalesConvertRateList = new ArrayList<>();
            frontCountSalesConvertRate.setList(frontCountSalesConvertRateList);
            frontCountSalesConvertRate.setCurrentValue(BigDecimalUtil.divide(BigDecimal.valueOf(totalOrderSum), frontCountSumTemp));

            //一级品类门前客流
            AnalyseResp frontCountCamp = createAnalyseResp(10, "一级品类门前客流", "某一级品类在营商户门前客流总数之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> frontCountList = new ArrayList<>();
            frontCountCamp.setList(frontCountList);
            frontCountCamp.setCurrentValue(BigDecimal.valueOf(totalFrontCount));

            //一级品类进店客流
            AnalyseResp inShopCountCamp = createAnalyseResp(13, "一级品类进店客流", "某一级品类在营商户进店客流总数之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> inShopCountList = new ArrayList<>();
            inShopCountCamp.setCurrentValue(BigDecimal.valueOf(inShopFrontCount));
            inShopCountCamp.setList(inShopCountList);

            //一级品类门前客流流量成本
            AnalyseResp frontCountCost = createAnalyseResp(11, "一级品类门前客流流量成本", "某一级品类在营商户的租管费之和/门前客流总数之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> frontCountCostList = new ArrayList<>();
            frontCountCost.setList(frontCountCostList);

            //一级品类销售笔数
            AnalyseResp totalOrderCamp = createAnalyseResp(12, "一级品类销售笔数", "某一级品类在营商户销售笔数之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            totalOrderCamp.setCurrentValue(BigDecimal.valueOf(totalOrderSum));
            List<BigDecimal> totalOrderList = new ArrayList<>();
            totalOrderCamp.setList(totalOrderList);
            //一级品类招商周期
            AnalyseResp investCycleCamp = createAnalyseResp(20, "一级品类招商周期", "某一级品类新签合同（合同签约日期-带看日期）（静态数值、季度同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<AnalyseResp.Item> investCycleObjList = new ArrayList<>();
            List<BigDecimal> investCycleList  = new ArrayList<>();
            // 设置招商周期的当前值为平均值
            BigDecimal avgInvestCycle = BigDecimal.ZERO;

            investCycleCamp.setObjList(investCycleObjList);
            investCycleCamp.setList(investCycleList);
            List<ReportOperateDayListVo> operateDaynvestCycleList = operateDayListByCamp.stream().filter(entity -> entity.getVisitDate()!=null&&entity.getCategoryId().equals(category.getId()) && DateUtil.isDateInRange(entity.getRentStartDate(), startDate, endDate))
                    .collect(Collectors.toMap(
                            vo -> vo.getTenantId() + "_" + vo.getStoreId(), // 以项目ID+门店ID为key
                            vo -> vo,
                            (existing, replacement) -> existing // 有重复时保留第一个
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            //计算平均值

            if (!operateDaynvestCycleList.isEmpty()) {
                // 计算招商周期的平均值
                BigDecimal totalInvestCycle = operateDaynvestCycleList.stream()
                        .filter(vo -> vo.getInvestCycle() > 0) // 过滤掉无效的招商周期数据
                        .map(vo -> BigDecimal.valueOf(vo.getInvestCycle()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                long validCount = operateDaynvestCycleList.stream()
                        .filter(vo -> vo.getInvestCycle() > 0)
                        .count();

                if (validCount > 0) {
                    avgInvestCycle = totalInvestCycle.divide(BigDecimal.valueOf(validCount), 2, RoundingMode.HALF_UP);
                }

                // 将项目名称+门店作为key，招商周期作为value添加到investCycleObjList中
                for (ReportOperateDayListVo vo : operateDaynvestCycleList) {
                    if (vo.getInvestCycle() > 0) {
                        AnalyseResp.Item item = new AnalyseResp.Item();
                        String key = vo.getTenantName() + "_" + vo.getStoreId();
                        item.setKey(key);
                        item.setValue(BigDecimal.valueOf(vo.getInvestCycle()));
                        investCycleObjList.add(item);
                        investCycleList.add(item.getValue());
                    }
                }
            }
            investCycleCamp.setCurrentValue(avgInvestCycle);
            //一级品类净利润
            AnalyseResp netProfitCamp = createAnalyseResp(21, "一级品类净利润", "某一级品类在营商户的净利润（从门店盈亏报告中取值）（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<AnalyseResp.Item> netProfitObjList = new ArrayList<>();
            List<BigDecimal> netProfitList  = new ArrayList<>();
            netProfitCamp.setObjList(netProfitObjList);
            netProfitCamp.setList(netProfitList);
            //一级品类的客单价
            AnalyseResp totalPriceCamp = createAnalyseResp(22, "一级品类的客单价", "某品类在营商户月销售额之和/月销售笔数之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> totalPriceList  = new ArrayList<>();
            totalPriceCamp.setList(totalPriceList);
            totalPriceCamp.setCurrentValue(BigDecimalUtil.divide(totalAmountSum,BigDecimal.valueOf(totalOrderSum)));
            //客流流量收益
            AnalyseResp flowRevenueCamp = createAnalyseResp(23, "客流流量收益", "某一级品类在营商户的销售额之和/门前客流总数之和（静态数值、同环比趋势）", category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> flowRevenueList  = new ArrayList<>();
            flowRevenueCamp.setList(flowRevenueList);
            flowRevenueCamp.setCurrentValue(BigDecimalUtil.divide(totalAmountSum,BigDecimal.valueOf(totalFrontCount)));
            //一级品类门店出租率
            AnalyseResp rentRateCamp = createAnalyseResp(24, "一级品类门店出租率", "某品类在营门店数/门店总数（静态数值、同环比趋势）"
                    , category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> rentRateList = new ArrayList<>();
            rentRateCamp.setList(rentRateList);
            //品牌直营占比
            AnalyseResp brandDirectSalesCamp = createAnalyseResp(25, "一级品类品牌直营占比", "某一业态在营商户的品牌直营店铺总数/该业态在营店铺总数（静态数值、同环比趋势）"
                    , category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> brandDirectSalesList = new ArrayList<>();
            brandDirectSalesCamp.setList(brandDirectSalesList);
            //品牌代理占比
            AnalyseResp brandAgentSalesCamp = createAnalyseResp(26, "一级品类品牌代理占比", "某一业态在营商户的品牌代理店铺总数/该业态在营店铺总数（静态数值、同环比趋势）"
                    , category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> brandAgentSalesList = new ArrayList<>();
            brandAgentSalesCamp.setList(brandAgentSalesList);
            //品牌加盟占比
            AnalyseResp brandJoinSalesCamp = createAnalyseResp(27, "一级品类品牌加盟占比", "某一业态在营商户的品牌加盟店铺总数/该业态在营店铺总数（静态数值、同环比趋势）"
                    , category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> brandJoinSalesList = new ArrayList<>();
            brandJoinSalesCamp.setList(brandJoinSalesList);
            //个体经营占比
            AnalyseResp individualSalesCamp = createAnalyseResp(28, "一级品类个体经营占比", "某一业态在营商户的个体经营店铺总数/该业态在营店铺总数（静态数值、同环比趋势）"
                    , category.getParentId(), tempArr[0], category.getId(), category.getCategoryName());
            List<BigDecimal> individualSalesList = new ArrayList<>();
            individualSalesCamp.setList(individualSalesList);
            //某品类的在营
            List<ReportOperateDayListVo> campList = operateDayListByCamp.stream().filter(entity -> entity.getCategoryId().equals(category.getId())).collect(Collectors.toList());
            for (int i = 0; i < dateRangeList.size(); i++) {
                LocalDate localDate = dateRangeList.get(i);

                //日期对应的铺位数据。
                long count = campList.stream().filter(e -> e.getDate().equals(localDate)).count();
                //业态占比
                long totalCount = CommercialTypeCodeList.stream().filter(e -> e.getDate().equals(localDate)).count();
                BigDecimal currentValue = BigDecimalUtil.divide(BigDecimal.valueOf(count), BigDecimal.valueOf(totalCount));


                //日期对应的门前客流
                long frontCount = campList.stream().filter(e -> e.getDate().equals(localDate) && e.getFrontCount() != null).mapToLong(e -> e.getFrontCount()).sum();
                //日期对应的进店客流
                long inShopCount = campList.stream().filter(e -> e.getDate().equals(localDate) && e.getInShopCount() != null).mapToLong(e -> e.getInShopCount()).sum();
                Integer totalOrder = campList.stream().filter(e -> e.getDate().equals(localDate) && e.getTotalOrder() != null).mapToInt(e -> e.getTotalOrder()).sum();

                //租管费
                BigDecimal theoryRent = campList.stream()
                        .filter(e -> e.getDate().equals(localDate) && e.getTheoryRent() != null)
                        .map(ReportOperateDayListVo::getTheoryRent)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //日销售额
                BigDecimal totalAmount = campList.stream()
                        .filter(e -> e.getDate().equals(localDate) && e.getTotalAmount() != null)
                        .map(ReportOperateDayListVo::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //面积
                BigDecimal roomArea = campList.stream()
                        .filter(entity -> entity.getDate().equals(localDate)).map(ReportOperateDayListVo::getRoomArea)
                        .filter(Objects::nonNull)
                        .map(BigDecimal::valueOf) // Double转BigDecimal
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                //出租率
                //日期对应所有的铺位数
                long totalCountByAll = operateDayList.stream().filter(e -> e.getDate().equals(localDate)).count();
                BigDecimal rentRate =BigDecimalUtil.divide(BigDecimal.valueOf(count), BigDecimal.valueOf(totalCountByAll));

                //直营占比
                long brandDirectSalesCount=campList.stream().filter(e -> e.getDate().equals(localDate)&&e.getBusinessType()!=null&&e.getBusinessType()==1).count();
                BigDecimal brandDirectSalesRate = BigDecimalUtil.divide(BigDecimal.valueOf(brandDirectSalesCount), BigDecimal.valueOf(count));
                //代理占比
                long brandAgentSalesCount=campList.stream().filter(e -> e.getDate().equals(localDate)&&e.getBusinessType()!=null&&e.getBusinessType()==2).count();
                BigDecimal brandAgentSalesRate = BigDecimalUtil.divide(BigDecimal.valueOf(brandAgentSalesCount), BigDecimal.valueOf(count));
                //加盟占比
                long brandJoinSalesCount=campList.stream().filter(e -> e.getDate().equals(localDate)&&e.getBusinessType()!=null&&e.getBusinessType()==3).count();
                BigDecimal brandJoinSalesRate = BigDecimalUtil.divide(BigDecimal.valueOf(brandJoinSalesCount), BigDecimal.valueOf(count));
                //个体经营占比
                long individualSalesCount=campList.stream().filter(e -> e.getDate().equals(localDate)&&e.getBusinessType()!=null&&e.getBusinessType()==4).count();
                BigDecimal individualSalesRate = BigDecimalUtil.divide(BigDecimal.valueOf(individualSalesCount), BigDecimal.valueOf(count));

                //最后一天的数据作为当前数据
                if (i == dateRangeList.size() - 1) {
                    itemCamp.setCurrentValue(BigDecimalUtil.divide(BigDecimal.valueOf(campList.size()), BigDecimal.valueOf(CommercialTypeCodeList.size())));
                    rentCamp.setCurrentValue(BigDecimalUtil.divide(theoryRent, totalAmountSum));
                    rentPrice.setCurrentValue(BigDecimalUtil.divide(theoryRent, roomArea));
                    saleEfficiency.setCurrentValue(BigDecimalUtil.divide(totalAmountSum, roomArea));
                    //门前客流流量成本
                    frontCountCost.setCurrentValue(BigDecimalUtil.divide(theoryRent, BigDecimal.valueOf(totalFrontCount)));
                    //一级品类净利润
                    List<ReportOperateDayListVo> netProfitTempList = campList.stream().filter(e -> e.getDate().equals(localDate)).collect(Collectors.toList());
                    BigDecimal netProfitSum =BigDecimal.ZERO;
                    for (ReportOperateDayListVo temp : netProfitTempList) {
                        if(temp==null||temp.getNetProfit()==null) {
                            continue;
                        }
                        netProfitSum=BigDecimalUtil.add(netProfitSum,temp.getNetProfit());
                        netProfitList.add(temp.getNetProfit());
                        AnalyseResp.Item item = new AnalyseResp.Item();
                        item.setKey(temp.getTenantName() + "_" + temp.getStoreId());
                        item.setValue(temp.getNetProfit());
                        netProfitObjList.add(item);
                    }
                    netProfitCamp.setCurrentValue(netProfitSum);
                    rentRateCamp.setCurrentValue(rentRate);
                    brandDirectSalesCamp.setCurrentValue(brandDirectSalesRate);
                    brandAgentSalesCamp.setCurrentValue(brandAgentSalesRate);
                    brandJoinSalesCamp.setCurrentValue(brandJoinSalesRate);
                    individualSalesCamp.setCurrentValue(individualSalesRate);
                }
                list.add(currentValue);
                rentList.add(BigDecimalUtil.divide(BigDecimalUtil.divide(theoryRent, BigDecimal.valueOf(dateRangeList.size())), totalAmount));
                rentPriceList.add(BigDecimalUtil.divide(theoryRent, roomArea));
                saleEfficiencyList.add(BigDecimalUtil.divide(totalAmount, roomArea));
                //除以1000的门前客流/千人
                BigDecimal frontTemp = BigDecimalUtil.divide(BigDecimal.valueOf(frontCount), BigDecimal.valueOf(1000));
                frontCountSalesConvertRateList.add(BigDecimalUtil.divide(BigDecimal.valueOf(totalOrder), frontTemp));
                frontCountList.add(BigDecimal.valueOf(frontCount));
                inShopCountList.add(BigDecimal.valueOf(inShopCount));
                frontCountCostList.add(BigDecimalUtil.divide(BigDecimalUtil.divide(theoryRent, BigDecimal.valueOf(dateRangeList.size())), frontTemp));
                totalOrderList.add(BigDecimal.valueOf(totalOrder));
                totalPriceList.add(BigDecimalUtil.divide(totalAmount, BigDecimal.valueOf(totalOrder)));
                flowRevenueList.add(BigDecimalUtil.divide(totalAmount, BigDecimal.valueOf(frontCount)));
                rentRateList.add(rentRate);
                brandDirectSalesList.add(brandDirectSalesRate);
                brandAgentSalesList.add(brandAgentSalesRate);
                brandJoinSalesList.add(brandJoinSalesRate);
                individualSalesList.add(individualSalesRate);
            }
            result.add(itemCamp);
            result.add(rentCamp);
            result.add(rentPrice);
            result.add(saleEfficiency);
            result.add(frontCountSalesConvertRate);
            result.add(frontCountCamp);
            result.add(inShopCountCamp);
            result.add(frontCountCost);
            result.add(totalOrderCamp);
            result.add(investCycleCamp);
            result.add(netProfitCamp);
            result.add(totalPriceCamp);
            result.add(rentRateCamp);
            result.add(brandDirectSalesCamp);
            result.add(brandAgentSalesCamp);
            result.add(brandJoinSalesCamp);
            result.add(individualSalesCamp);
            result.add(flowRevenueCamp);
        }
        return result;
    }

    //每天的数据。使用时需区分门店状态是在营还是空置。
    private List<ReportOperateDayListVo> getOperateDay(LocalDate startDate, LocalDate endDate) {
        ReportOperateDayListDto queryDto = new ReportOperateDayListDto();
        queryDto.setStartDate(startDate);
        queryDto.setEndDate(endDate);
        List<ReportOperateDayListVo> list = operateDayProvider.getOperateDayAndFlowList(queryDto);
        //查询门店成本的净利润
        ReportProfitBusinessOverviewListDto profitDto=new ReportProfitBusinessOverviewListDto();
        profitDto.setStartDate(startDate);
        profitDto.setEndDate(endDate);
        List<ReportProfitBusinessOverviewListVo> profitList = profitBusinessOverviewProvider.list(profitDto);
        // 构建Map加速查找
        Map<String, ReportProfitBusinessOverviewListVo> profitMap = new HashMap<>();
        for (ReportProfitBusinessOverviewListVo profit : profitList) {
            String key = profit.getTenantId() + "_" + profit.getStoreId();
            profitMap.put(key, profit);
        }
        for (ReportOperateDayListVo entity : list) {
            entity.setDate(LocalDate.of(entity.getYear(), entity.getMonth(), entity.getDay()));
            if(entity.getTenantId()!=null && StringUtils.isNotEmpty(entity.getStoreId())){
                String key = entity.getTenantId() + "_" + entity.getStoreId();
                ReportProfitBusinessOverviewListVo profit = profitMap.get(key);
                if (profit != null) {
                    // 添加利润数据
                    entity.setNetProfit(profit.getNetProfit());
                }
            }
        }
        return list;
    }


    //获取所有业态或者品类的数据
    private List<CommercialCategoryListVo> getCategoryList(int level) {
        CommercialCategoryListDto queryDto = new CommercialCategoryListDto();
        queryDto.setSlevel(level);
        return categoryProvider.list(queryDto);
    }

    private List<AnalyseResp> mergeAnalyseData(List<AnalyseResp> currentData, List<AnalyseResp> yearOnYearData, List<AnalyseResp> monthOnMonthData) {
        // 创建Map来存储同比和环比数据，便于快速查找
        Map<String, AnalyseResp> yearOnYearMap = yearOnYearData.stream()
                .collect(Collectors.toMap(
                        data -> generateKey(data.getIndicatorId(), data.getCommercialTypeCode(), data.getCategoryId()),
                        data -> data,
                        (existing, replacement) -> existing
                ));

        Map<String, AnalyseResp> monthOnMonthMap = monthOnMonthData.stream()
                .collect(Collectors.toMap(
                        data -> generateKey(data.getIndicatorId(), data.getCommercialTypeCode(), data.getCategoryId()),
                        data -> data,
                        (existing, replacement) -> existing
                ));

        // 遍历当前数据，将同比和环比数据赋值给对应的字段
        for (AnalyseResp current : currentData) {
            String key = generateKey(current.getIndicatorId(), current.getCommercialTypeCode(), current.getCategoryId());

            // 查找对应的同比数据
            AnalyseResp yearOnYear = yearOnYearMap.get(key);
            if (yearOnYear != null) {
                current.setYearOnYearData(yearOnYear.getCurrentValue());
            }

            // 查找对应的环比数据
            AnalyseResp monthOnMonth = monthOnMonthMap.get(key);
            if (monthOnMonth != null) {
                current.setMonthOnMonthData(monthOnMonth.getCurrentValue());
            }
        }

        return currentData;
    }

    /**
     * 生成用于匹配的key
     *
     * @param indicatorId        指标ID
     * @param commercialTypeCode 业态ID
     * @param categoryId         品类ID
     * @return 匹配key
     */
    private String generateKey(Integer indicatorId, Long commercialTypeCode, Long categoryId) {
        return indicatorId + "_" + commercialTypeCode + "_" + (categoryId != null ? categoryId : "null");
    }

    /**
     * 对结果list按业态+品类分组，组内所有指标拼接统计描述，调用大模型生成解读，并赋值给组内所有数据
     */
    private void generateInterpretationForGroups(List<ReportInvertMarketAddDto> result) {
        if (result == null || result.isEmpty()) return;
        OllamaHttpClient ollamaClient = new OllamaHttpClient();
        Map<String, List<ReportInvertMarketAddDto>> groupMap = result.stream()
                .collect(Collectors.groupingBy(dto -> dto.getCommercialTypeCode() + "_" + (dto.getCategoryId() == null ? "null" : dto.getCategoryId())));
        for (Map.Entry<String, List<ReportInvertMarketAddDto>> entry : groupMap.entrySet()) {
            List<ReportInvertMarketAddDto> groupList = entry.getValue();
            if (groupList.isEmpty()) continue;
            ReportInvertMarketAddDto sample = groupList.get(0);
            StringBuilder sb = new StringBuilder();
            sb.append("业态[").append(sample.getCommercialTypeName()).append("]");
            if (sample.getCategoryName() != null) {
                sb.append(" 品类[").append(sample.getCategoryName()).append("]");
            }
            for (ReportInvertMarketAddDto report : groupList) {
                String indicator = report.getIndicator();
                sb.append(indicator).append("维度：\n");
                sb.append("  本期数据为: ").append(report.getCurrentPeriodData()).append("\n");
                sb.append("  同比数据为: ").append(report.getYearOnYearData()).append("\n");
                sb.append("  同比率为: ").append(report.getYearOnYearRate()).append("\n");
                sb.append("  环比数据为: ").append(report.getMonthOnMonthData()).append("\n");
                sb.append("  环比率为: ").append(report.getMonthOnMonthRate()).append("\n");
                sb.append("  均值为: ").append(report.getMeanValue()).append("\n");
                sb.append("  切尾均值为: ").append(report.getTrimmedMean()).append("\n");
                sb.append("  中位数为: ").append(report.getMedian()).append("\n");
                sb.append("  众数为: ").append(report.getMostFrequentValue()).append("\n");
                sb.append("  标准差为: ").append(report.getStandardDeviation()).append("\n");
                sb.append("  方差为: ").append(report.getVariance()).append("\n");
                sb.append("  中位数绝对偏差为: ").append(report.getMedianAbsoluteDeviation()).append("\n");
                sb.append("  极差为: ").append(report.getDataRange()).append("\n");
                sb.append("  四分位差为: ").append(report.getInterquartileRange()).append("\n");
                sb.append("  第一四分位为: ").append(report.getFirstQuartile()).append("\n");
                sb.append("  第三四分位为: ").append(report.getThirdQuartile()).append("\n");
                sb.append("\n");
                sb.append("----------------------------------------\n");
            }
            sb.append("请直接给出分析结果，不要包含思考过程。分析应该包括：1.整体趋势 2.具体维度分析 3.核心问题 4.改进建议。");
            String interpretation = null;
            try {
                interpretation = ollamaClient.chat(sb.toString());
                // 清理返回结果中的思考过程
                // 新增清理逻辑
                if (interpretation.contains("</think>")) {
                    interpretation = interpretation.split("</think>", 2)[1].trim();
                }
            } catch (IOException e) {
                e.printStackTrace();
                interpretation = "无法生成解读结果: " + e.getMessage();
            }
            for (ReportInvertMarketAddDto dto : groupList) {
                dto.setResultInterpretation(interpretation);
            }
        }
    }

    private ChartDataDto convertToChartData(List<BigDecimal> valueList, List<LocalDate> dateRangeList, String indicator) {
        ChartDataDto chartData = new ChartDataDto();
        List<ChartDataDto.ChartDataItem> dataItems = new ArrayList<>();
        ChartDataDto.ChartDataItem dataItem = new ChartDataDto.ChartDataItem();

        // 设置指标标签
        String label = indicator;
        Integer width = 1200;
        Integer height = 500;
        String keyTitle = "日期";
        String valueTitle = indicator;
        String countTitle = indicator;
        dataItem.setLabel(label);
        chartData.setWidth(width);
        chartData.setHeight(height);
        chartData.setKeyTitle(keyTitle);
        chartData.setValueTitle(valueTitle);
        chartData.setCountTitle(countTitle);

        List<ChartDataDto.StatItem> stats = new ArrayList<>();
        for (int i = 0; i < valueList.size(); i++) {
            BigDecimal value = valueList.get(i);
            ChartDataDto.StatItem statItem = new ChartDataDto.StatItem();
            statItem.setKey(dateRangeList.get(i).toString()); // 使用品牌名称作为key
            statItem.setValue(value.doubleValue());
            stats.add(statItem);
        }
        dataItem.setStats(stats);
        dataItems.add(dataItem);
        chartData.setData(dataItems);
        // 设置图表类型
        chartData.setType(Arrays.asList("box", "density", "barDensity", "scatter"));

        return chartData;
    }
    private ChartDataDto convertToChartData(List<AnalyseResp.Item> objList, String indicator) {
        ChartDataDto chartData = new ChartDataDto();
        List<ChartDataDto.ChartDataItem> dataItems = new ArrayList<>();
        ChartDataDto.ChartDataItem dataItem = new ChartDataDto.ChartDataItem();

        // 设置指标标签
        String label = indicator;
        Integer width = 1200;
        Integer height = 500;
        String keyTitle = "门店";
        String valueTitle = indicator;
        String countTitle = indicator;
        dataItem.setLabel(label);
        chartData.setWidth(width);
        chartData.setHeight(height);
        chartData.setKeyTitle(keyTitle);
        chartData.setValueTitle(valueTitle);
        chartData.setCountTitle(countTitle);

        List<ChartDataDto.StatItem> stats = new ArrayList<>();

        for (int i = 0; i < objList.size(); i++) {
            AnalyseResp.Item item = objList.get(i);
            ChartDataDto.StatItem statItem = new ChartDataDto.StatItem();
            statItem.setKey(item.getKey()); // 使用品牌名称作为key
            statItem.setValue(item.getValue().doubleValue());
            stats.add(statItem);
        }
        dataItem.setStats(stats);
        dataItems.add(dataItem);
        chartData.setData(dataItems);
        // 设置图表类型
        chartData.setType(Arrays.asList("box", "density", "barDensity", "scatter"));

        return chartData;
    }

    private AnalyseResp createAnalyseResp(int indicatorId, String indicator, String indicatorRemark, Long commercialTypeCode, String commercialTypeName, Long categoryId, String categoryName) {
        AnalyseResp itemCamp = new AnalyseResp();
        itemCamp.setIndicatorId(indicatorId);
        itemCamp.setIndicator(indicator);
        itemCamp.setIndicatorRemark(indicatorRemark);
        itemCamp.setCommercialTypeCode(commercialTypeCode);
        itemCamp.setCommercialTypeName(commercialTypeName);
        itemCamp.setCategoryId(categoryId);
        itemCamp.setCategoryName(categoryName);
        return itemCamp;
    }

    private AnalyseResp createAnalyseResp(int indicatorId, String indicator, String indicatorRemark, Long commercialTypeCode, String commercialTypeName) {
        AnalyseResp itemCamp = new AnalyseResp();
        itemCamp.setIndicatorId(indicatorId);
        itemCamp.setIndicator(indicator);
        itemCamp.setIndicatorRemark(indicatorRemark);
        itemCamp.setCommercialTypeCode(commercialTypeCode);
        itemCamp.setCommercialTypeName(commercialTypeName);
        return itemCamp;
    }

    /**
     * 根据tenantId和storeId，将合同信息（租赁开始日期、带看日期、招商周期）赋值到operateDayList中
     */
    private void fillContractInfoToOperateDayList(List<ReportOperateDayListVo> operateDayList, List<CommerceInvertContractListVo> allInvertContract) {
        if (operateDayList == null || allInvertContract == null) return;
        // 构建Map加速查找
        Map<String, CommerceInvertContractListVo> contractMap = new HashMap<>();
        for (CommerceInvertContractListVo contract : allInvertContract) {
            String key = contract.getTenantId() + "_" + contract.getStoreId();
            contractMap.put(key, contract);
        }
        for (ReportOperateDayListVo vo : operateDayList) {
            // 如果tenantId或storeId为空，直接跳过
            if (vo.getTenantId() == null || vo.getStoreId() == null) {
                continue;
            }
            String key = vo.getTenantId() + "_" + vo.getStoreId();
            CommerceInvertContractListVo contract = contractMap.get(key);
            if (contract != null) {
                vo.setRentStartDate(contract.getRentStartDate());
                vo.setVisitDate(contract.getVisitDate());
                vo.setInvestCycle(contract.getInvestCycle());
                vo.setBusinessType(contract.getBusinessType());
            }
        }
    }

}
