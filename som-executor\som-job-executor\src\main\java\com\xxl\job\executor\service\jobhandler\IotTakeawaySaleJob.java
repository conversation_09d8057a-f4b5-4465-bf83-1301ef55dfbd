package com.xxl.job.executor.service.jobhandler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.seewin.som.commerce.provider.CommerceBrandProvider;
import com.seewin.som.commerce.resp.CommerceBrandGetVo;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.resp.EntProjectGetVo;
import com.seewin.som.iot.provider.IotTakeawaySaleDataProvider;
import com.seewin.som.iot.resp.IotDaySettleParseVo;
import com.seewin.som.report.provider.ReportMasterSaleDataProvider;
import com.seewin.som.report.req.ReportMasterSaleDataAddDto;
import com.seewin.som.report.req.ReportMasterSaleDataEditDto;
import com.seewin.som.report.req.ReportMasterSaleDataListDto;
import com.seewin.som.report.resp.ReportDaySettleSaleDataVo;
import com.seewin.som.report.resp.ReportMasterSaleDataGetVo;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsGetVo;
import com.seewin.util.bean.BeanUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class IotTakeawaySaleJob {

    @DubboReference(providedBy = "som-report-mgt")
    private ReportMasterSaleDataProvider reportMasterSaleDataProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotTakeawaySaleDataProvider iotTakeawaySaleDataProvider;

    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceBrandProvider commerceBrandProvider;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;

    /**
     * 汇总外卖销售数据到主体数据库里面
     */
    @XxlJob("takeawaySale")
    public void takeawaySale() {
        log.info("汇总外卖销售数据到主体数据库里面...");

        LocalDate currentDate = LocalDate.now().minusDays(1);
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(jobParam)) {
            currentDate = LocalDate.parse(jobParam);
        }

        // 获取
        List<IotDaySettleParseVo> parseVoList = iotTakeawaySaleDataProvider.getJobSaleData(currentDate);
        if (CollectionUtil.isNotEmpty(parseVoList)){
            for (IotDaySettleParseVo parseVo : parseVoList) {
                Long tenantId = parseVo.getTenantId();
                String objCode = parseVo.getObjCode();
                LocalDate saleTime = parseVo.getSaleTime();
                BigDecimal takeawayAmount = parseVo.getTakeawayAmount();
                Integer takeawayOrder = parseVo.getTakeawayOrder() == null ? 0 : parseVo.getTakeawayOrder();

                BigDecimal totalAmount = BigDecimal.ZERO;
                Integer totalOrder = 0;
                BigDecimal storeAmount = BigDecimal.ZERO;
                Integer storeOrder = 0;

                ReportMasterSaleDataListDto saleDataDto = new ReportMasterSaleDataListDto();
                saleDataDto.setTenantId(tenantId);
                saleDataDto.setObjCode(objCode);
                saleDataDto.setSaleTime(saleTime);
                ReportMasterSaleDataGetVo saleDataVo = reportMasterSaleDataProvider.get(saleDataDto);
                if (saleDataVo!=null){
                    // 手动修改过的不覆盖
                    if (saleDataVo.getDataStatus()!=null && saleDataVo.getDataStatus().equals(1)){
                        continue;
                    }

                    // 修改之前的数据
                    String beforeSaleData = saleDataVo.getBeforeSaleData();
                    ReportDaySettleSaleDataVo settleSaleDataVo = BeanUtils.copyProperties(saleDataVo, ReportDaySettleSaleDataVo.class);

                    if (StringUtils.isEmpty(beforeSaleData)){
                        // 只有外卖单和销售单
                        totalAmount = saleDataVo.getTotalAmount();
                        totalOrder = saleDataVo.getTotalOrder()==null ? 0 : saleDataVo.getTotalOrder();

                        storeAmount = totalAmount;
                        storeOrder = totalOrder;

                        totalAmount = totalAmount.add(takeawayAmount);
                        totalOrder = totalOrder+takeawayOrder;

                        settleSaleDataVo.setSaleOrderType(2);
                        String updateBeforeSaleData = JSONUtil.toJsonStr(settleSaleDataVo);
                        saleDataVo.setBeforeSaleData(updateBeforeSaleData);
                    }else {
                        // 不为空判断是否有日结单数据
                        ReportDaySettleSaleDataVo beforeDataVo = JSONUtil.toBean(beforeSaleData, ReportDaySettleSaleDataVo.class);
                        int saleOrderType = beforeDataVo.getSaleOrderType() == null ? 1 : beforeDataVo.getSaleOrderType();
                        if (saleOrderType==1){
                            // 有日结单
                            totalAmount = saleDataVo.getTotalAmount();
                            totalOrder = saleDataVo.getTotalOrder()==null ? 0 : saleDataVo.getTotalOrder();

                            storeAmount = totalAmount;
                            storeOrder = totalOrder;

                            totalAmount = totalAmount.add(takeawayAmount);
                            totalOrder = totalOrder+takeawayOrder;

                            beforeDataVo.setSaleOrderType(3);
                            String updateBeforeSaleData = JSONUtil.toJsonStr(beforeDataVo);
                            saleDataVo.setBeforeSaleData(updateBeforeSaleData);
                        }else if (saleOrderType==2){

                        }else if (saleOrderType==3){

                        }
                    }

                    saleDataVo.setTotalAmount(totalAmount);
                    saleDataVo.setTotalOrder(totalOrder);
                    saleDataVo.setStoreAmount(storeAmount);
                    saleDataVo.setStoreOrder(storeOrder);
                    saleDataVo.setTakeawayAmount(takeawayAmount);
                    saleDataVo.setTakeawayOrder(takeawayOrder);

                    ReportMasterSaleDataEditDto saleDataEditDto = BeanUtils.copyProperties(saleDataVo, ReportMasterSaleDataEditDto.class);
                    reportMasterSaleDataProvider.edit(saleDataEditDto);
                }else{
                    // 如果为空则插入一条数据
                    RoomsListDto roomsListDto = new RoomsListDto();
                    roomsListDto.setTenantId(tenantId);
                    roomsListDto.setFcode(objCode);
                    RoomsGetVo roomsGetVo = roomProvider.get(roomsListDto);
                    if (roomsGetVo == null) {
                        continue;
                    }

                    ReportDaySettleSaleDataVo settleSaleDataVo = new ReportDaySettleSaleDataVo();
                    settleSaleDataVo.setSaleOrderType(2);
                    settleSaleDataVo.setTotalAmount(BigDecimal.ZERO);
                    settleSaleDataVo.setStoreAmount(BigDecimal.ZERO);
                    settleSaleDataVo.setTakeawayAmount(BigDecimal.ZERO);
                    settleSaleDataVo.setTotalOrder(0);
                    settleSaleDataVo.setStoreOrder(0);
                    settleSaleDataVo.setTakeawayOrder(0);
                    String updateBeforeSaleData = JSONUtil.toJsonStr(settleSaleDataVo);

                    ReportMasterSaleDataAddDto saleDataAddDto = new ReportMasterSaleDataAddDto();
                    saleDataAddDto.setBeforeSaleData(updateBeforeSaleData);

                    saleDataAddDto.setTotalAmount(takeawayAmount);
                    saleDataAddDto.setTotalOrder(takeawayOrder);
                    saleDataAddDto.setStoreAmount(BigDecimal.valueOf(0));
                    saleDataAddDto.setStoreOrder(0);
                    saleDataAddDto.setTakeawayAmount(takeawayAmount);
                    saleDataAddDto.setTakeawayOrder(takeawayOrder);

                    saleDataAddDto.setSaleTime(saleTime);

                    saleDataAddDto.setTenantId(tenantId);
                    saleDataAddDto.setTenantName(roomsGetVo.getTenantName());
                    saleDataAddDto.setEntId(roomsGetVo.getEntId());
                    saleDataAddDto.setOrgFid(roomsGetVo.getOrgFid());
                    saleDataAddDto.setOrgFname(roomsGetVo.getOrgFname());

                    saleDataAddDto.setShopNo(roomsGetVo.getName());
                    saleDataAddDto.setUseObj(roomsGetVo.getFname());
                    saleDataAddDto.setObjCode(objCode);

                    saleDataAddDto.setStoreId(roomsGetVo.getStoreId());
                    saleDataAddDto.setBrandName(roomsGetVo.getBrandName());

                    if(Objects.nonNull(roomsGetVo.getBrandId())) {
                        CommerceBrandGetVo brandGetVo = commerceBrandProvider.get(roomsGetVo.getBrandId());
                        if (Objects.nonNull(brandGetVo)) {
                            saleDataAddDto.setCommercialTypeCode(brandGetVo.getAdaptiveFormatId());
                            saleDataAddDto.setCommercialTypeName(brandGetVo.getAdaptiveFormat());
                            saleDataAddDto.setCategoryId(brandGetVo.getCategoryId());
                            saleDataAddDto.setCategoryName(brandGetVo.getCategory());
                            saleDataAddDto.setCommercialTwoId(brandGetVo.getCommercialTwoId());
                            saleDataAddDto.setCommercialTwo(brandGetVo.getCommercialTwo());
                        }
                    }

                    EntProjectGetVo entProjectGetVo = entProjectProvider.get(tenantId);
                    if (entProjectGetVo!=null){
                        saleDataAddDto.setCity(entProjectGetVo.getFcity());
                    }

                    saleDataAddDto.setCreateUser("takeawaySale");
                    saleDataAddDto.setCreateUserName("定时任务");
                    saleDataAddDto.setDataStatus(0);
                    saleDataAddDto.setDataSources(0);

                    reportMasterSaleDataProvider.add(saleDataAddDto);
                }

            }
        }
    }

}
