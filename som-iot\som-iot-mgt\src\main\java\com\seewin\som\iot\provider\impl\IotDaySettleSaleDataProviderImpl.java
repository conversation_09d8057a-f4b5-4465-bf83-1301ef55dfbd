package com.seewin.som.iot.provider.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.seewin.som.iot.provider.IotDeviceProvider;
import com.seewin.som.iot.provider.IotSaleBindProvider;
import com.seewin.som.iot.req.*;
import com.seewin.som.iot.resp.*;
import com.seewin.som.iot.utils.DaySettlePromptUtils;
import com.seewin.som.iot.utils.DeepSeekHttpClient;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsGetVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.iot.entity.IotDaySettleSaleData;
import com.seewin.util.bean.BeanUtils;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.seewin.som.iot.service.IotDaySettleSaleDataService;
import com.seewin.som.iot.provider.IotDaySettleSaleDataProvider;
/**
 * <p>
 * 日结单销售数据 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Slf4j
@DubboService
public class IotDaySettleSaleDataProviderImpl implements IotDaySettleSaleDataProvider{

	@Autowired
	private IotDaySettleSaleDataService iotDaySettleSaleDataService;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotSaleBindProvider iotSaleBindProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotDeviceProvider iotDeviceProvider;

    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomProvider;
	
	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<IotDaySettleSaleDataListVo> page(PageQuery<IotDaySettleSaleDataListDto> pageQuery) throws ServiceException
    {
    	IotDaySettleSaleDataListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<IotDaySettleSaleData> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<IotDaySettleSaleData> queryWrapper = queryBuild(dto);

        //查询数据
        page = iotDaySettleSaleDataService.page(page, queryWrapper);
        List<IotDaySettleSaleData> records = page.getRecords();

        //响应结果封装
        PageResult<IotDaySettleSaleDataListVo> result = new PageResult<>();
        List<IotDaySettleSaleDataListVo> items = BeanUtils.copyProperties(records, IotDaySettleSaleDataListVo.class);
        
        result.setItems(items);
        result.setPages((int)page.getPages());
        result.setTotal((int)page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<IotDaySettleSaleDataListVo> list(IotDaySettleSaleDataListDto dto) throws ServiceException
    {
    	  //构造查询条件
        QueryWrapper<IotDaySettleSaleData> queryWrapper = queryBuild(dto);

        //查询数据
        List<IotDaySettleSaleData> records = iotDaySettleSaleDataService.list(queryWrapper);

        //响应结果封装
        List<IotDaySettleSaleDataListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, IotDaySettleSaleDataListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(IotDaySettleSaleDataListDto dto) throws ServiceException
    {
     	//构造查询条件
        QueryWrapper<IotDaySettleSaleData> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) iotDaySettleSaleDataService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public IotDaySettleSaleDataGetVo get(Long id) throws ServiceException
    {
    	//查询数据
        IotDaySettleSaleData item = iotDaySettleSaleDataService.getById(id);

        //响应结果封装
        IotDaySettleSaleDataGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, IotDaySettleSaleDataGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
     @Override
    public IotDaySettleSaleDataGetVo get(IotDaySettleSaleDataListDto dto) throws ServiceException
    {
        //构造查询条件
        QueryWrapper<IotDaySettleSaleData> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        IotDaySettleSaleData item = iotDaySettleSaleDataService.getOne(queryWrapper);

        //响应结果封装
        IotDaySettleSaleDataGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, IotDaySettleSaleDataGetVo.class);
        }

        //返回查询结果
        return result; 	
    }


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
     @Override
    public IotDaySettleSaleDataAddVo add(IotDaySettleSaleDataAddDto dto) throws ServiceException
    {
    	 IotDaySettleSaleData entity = BeanUtils.copyProperties(dto, IotDaySettleSaleData.class);

        LocalDateTime nowTime = LocalDateTime.now();                
     	entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setUpdateTime(nowTime);
	    entity.setDelStatus(0);
        iotDaySettleSaleDataService.save(entity);

        //响应结果封装
        IotDaySettleSaleDataAddVo result = new IotDaySettleSaleDataAddVo();
        result.setId(entity.getId());

        return result;
    }


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void edit(IotDaySettleSaleDataEditDto dto) throws ServiceException
    {
    	IotDaySettleSaleData entity = BeanUtils.copyProperties(dto, IotDaySettleSaleData.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateTime(nowTime);

        iotDaySettleSaleDataService.updateById(entity);
    }

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(Long id) throws ServiceException
    {
    	 iotDaySettleSaleDataService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(IotDaySettleSaleDataListDto dto) throws ServiceException
    {
    	//构造查询条件
        QueryWrapper<IotDaySettleSaleData> queryWrapper = queryBuild(dto, false);

        //删除操作
        iotDaySettleSaleDataService.remove(queryWrapper);
    }

    @Override
    public IotDaySettlePromptVo getPromptInfo() {
        return iotDaySettleSaleDataService.getPromptInfo();
    }

    @Override
    public Boolean parseTextData(String data, String daySettlePrompt, String picUrl, String deviceId) {
        Boolean parseFlag = Boolean.TRUE;

        log.info("日结单解析, deviceId:{}, data:{}", deviceId, data);

        IotSaleBindListDto iotSaleBindListDto = new IotSaleBindListDto();
        iotSaleBindListDto.setDeviceId(deviceId);
        IotSaleBindGetVo iotSaleBindGetVo = iotSaleBindProvider.get(iotSaleBindListDto);
        if (iotSaleBindGetVo == null) {
            log.warn("日结单设备sn:{} 未找到销售绑定信息", deviceId);
            return Boolean.FALSE;
        }
        IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
        iotDeviceListDto.setDeviceId(deviceId);
        IotDeviceGetVo iotDeviceGetVo = iotDeviceProvider.get(iotDeviceListDto);
        if (iotDeviceGetVo == null) {
            log.warn("日结单设备sn:{} 未找到设备信息", deviceId);
            return Boolean.FALSE;
        }
        RoomsListDto roomsListDto = new RoomsListDto();
        roomsListDto.setTenantId(iotSaleBindGetVo.getTenantId());
        roomsListDto.setFcode(iotSaleBindGetVo.getObjCode());
        RoomsGetVo roomsGetVo = roomProvider.get(roomsListDto);
        if (roomsGetVo == null) {
            log.warn("日结单空间编码:{} 未找到门店数据", iotSaleBindGetVo.getObjCode());
            return Boolean.FALSE;
        }

        // 转成域名
        log.info("picUrlBefore:{}", picUrl);
        if (StrUtil.isNotEmpty(picUrl)){
            int indexLocation = picUrl.indexOf("/developer");
            if (indexLocation!=-1){
                String subAfter = picUrl.substring(indexLocation);
                picUrl = "https://erqi.leyingiot.com" + subAfter;
            }
        }
        log.info("picUrlAfter:{}", picUrl);

        IotDaySettleSaleDataAddDto addDto = BeanUtils.copyProperties(iotDeviceGetVo, IotDaySettleSaleDataAddDto.class);
        addDto.setTicketPic(picUrl);
        addDto.setTicketText(data);

        addDto.setShopNo(roomsGetVo.getName());
        addDto.setUseObj(iotSaleBindGetVo.getUseObj());
        addDto.setObjCode(iotSaleBindGetVo.getObjCode());
        addDto.setStoreId(roomsGetVo.getStoreId());
        addDto.setBrandId(roomsGetVo.getBrandId());
        addDto.setBrandName(roomsGetVo.getBrandName());

        // 入库变量
        String orderNo = null;
        BigDecimal totalAmount = BigDecimal.ZERO;
        Integer totalOrder = 1;
        BigDecimal takeawayAmount = BigDecimal.ZERO;
        Integer takeawayOrder = 0;
        BigDecimal storeAmount = BigDecimal.ZERO;
        Integer storeOrder = 0;
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime orderTime = now.withHour(0).withMinute(0).withSecond(0);
        LocalDate saleTime = LocalDate.now();

        LocalDateTime orderParseTime = null;
        LocalDateTime parseTime = null;
        String parseJson = null;

        DaySettlePromptUtils promptUtils = new DaySettlePromptUtils();
        String contentJoin = "\n"+"要解析的小票文本内容如下: "+"\n";

        // deepSeek在线版第一次解析
        try {
            LocalDateTime deepSeekOrderTime = null;
            LocalDateTime deepSeekTime = null;
            BigDecimal deepSeekTotalAmount = null;
            String deepResp = DeepSeekHttpClient.callDeepSeekAPI(daySettlePrompt+contentJoin+data);
            String deepSeekJson = promptUtils.extractJson(deepResp);
            if (StrUtil.isNotEmpty(deepSeekJson)){
                Map<String, Object> parseMap = JSONUtil.toBean(deepSeekJson, Map.class);
                for(Map.Entry<String, Object> entry : parseMap.entrySet()) {
                    String mapKey = entry.getKey();
                    String mapValue = entry.getValue() != null ? entry.getValue().toString() : null;
                    if (mapKey.contains("订单时间") || mapKey.contains("订单日期")){
                        String orderTimeStr = mapValue;
                        if (StrUtil.isNotEmpty(orderTimeStr)){
                            LocalDateTime orderTimeParse = promptUtils.parseDateTime(orderTimeStr);
                            if (orderTimeParse!=null){
                                deepSeekOrderTime = orderTimeParse.withHour(0).withMinute(0).withSecond(0);
                            }
                        }
                        continue;
                    }
                    if (mapKey.contains("时间") || mapKey.contains("日期")){
                        String orderTimeStr = mapValue;
                        if (StrUtil.isNotEmpty(orderTimeStr)){
                            LocalDateTime orderTimeParse = promptUtils.parseDateTime(orderTimeStr);
                            if (orderTimeParse!=null){
                                deepSeekTime = orderTimeParse.withHour(0).withMinute(0).withSecond(0);
                            }
                        }
                        continue;
                    }
                    if (mapKey.contains("外卖")){
                        continue;
                    }
                    if (mapKey.contains("销售")){
                        if (mapKey.contains("额")){
                            String totalAmountStr = mapValue;
                            String totalAmountEql = promptUtils.parseEquals(totalAmountStr);
                            if (StrUtil.isNotEmpty(totalAmountEql)){
                                deepSeekTotalAmount = new BigDecimal(totalAmountEql).setScale(2, RoundingMode.HALF_UP);
                            }
                            continue;
                        }
                    }
                }
            }
            if (deepSeekOrderTime==null && deepSeekTime!=null){
                deepSeekOrderTime = deepSeekTime;
            }
            if (deepSeekOrderTime!=null && deepSeekTotalAmount!=null){
                parseJson = deepSeekJson;
            }
        }catch (Exception e){
            log.info("deepSeek第一次解析异常:{}", e.getMessage());
        }

        // 千问第一次解析
        if (StrUtil.isEmpty(parseJson)){
            try {
                LocalDateTime qwOrderTime = null;
                LocalDateTime qwTime = null;
                BigDecimal qwTotalAmount = null;
                String qwResp = promptUtils.chat(daySettlePrompt+contentJoin+data);
                String qwJson = promptUtils.extractJson(qwResp);
                if (StrUtil.isNotEmpty(qwJson)){
                    Map<String, Object> parseMap = JSONUtil.toBean(qwJson, Map.class);
                    for(Map.Entry<String, Object> entry : parseMap.entrySet()) {
                        String mapKey = entry.getKey();
                        String mapValue = entry.getValue() != null ? entry.getValue().toString() : null;
                        if (mapKey.contains("订单时间") || mapKey.contains("订单日期")){
                            String orderTimeStr = mapValue;
                            if (StrUtil.isNotEmpty(orderTimeStr)){
                                LocalDateTime orderTimeParse = promptUtils.parseDateTime(orderTimeStr);
                                if (orderTimeParse!=null){
                                    qwOrderTime = orderTimeParse.withHour(0).withMinute(0).withSecond(0);
                                }
                            }
                            continue;
                        }
                        if (mapKey.contains("时间") || mapKey.contains("日期")){
                            String orderTimeStr = mapValue;
                            if (StrUtil.isNotEmpty(orderTimeStr)){
                                LocalDateTime orderTimeParse = promptUtils.parseDateTime(orderTimeStr);
                                if (orderTimeParse!=null){
                                    qwTime = orderTimeParse.withHour(0).withMinute(0).withSecond(0);
                                }
                            }
                            continue;
                        }
                        if (mapKey.contains("外卖")){
                            continue;
                        }
                        if (mapKey.contains("销售")){
                            if (mapKey.contains("额")){
                                String totalAmountStr = mapValue;
                                String totalAmountEql = promptUtils.parseEquals(totalAmountStr);
                                if (StrUtil.isNotEmpty(totalAmountEql)){
                                    qwTotalAmount = new BigDecimal(totalAmountEql).setScale(2, RoundingMode.HALF_UP);
                                }
                                continue;
                            }
                        }
                    }
                }
                if (qwOrderTime==null && qwTime!=null){
                    qwOrderTime = qwTime;
                }
                if (qwOrderTime!=null && qwTotalAmount!=null){
                    parseJson = qwJson;
                }
            }catch (Exception e){
                log.info("千问第一次解析异常:{}", e.getMessage());
            }
        }

        try {
            // deepSeek第二次解析
            if (StrUtil.isEmpty(parseJson)){
                String deepSecondResp = promptUtils.deepSeekChat(daySettlePrompt+contentJoin+data);
                parseJson = promptUtils.extractJson(deepSecondResp);
            }
            if (StrUtil.isNotEmpty(parseJson)){
                Map<String, Object> parseMap = JSONUtil.toBean(parseJson, Map.class);
                parseJson = JSONUtil.toJsonStr(parseMap);

                for(Map.Entry<String, Object> entry : parseMap.entrySet()){
                    String mapKey = entry.getKey();
                    String mapValue = entry.getValue()!=null ? entry.getValue().toString() : null;
                    if (mapKey.contains("订单")){
                        if (mapKey.contains("号")){
                            String orderNoStr = mapValue;
                            if (StrUtil.isNotEmpty(orderNoStr) && !orderNoStr.equals("无")){
                                orderNo = orderNoStr;
                            }
                            continue;
                        }
                        if (mapKey.contains("时间") || mapKey.contains("日期")){
                            String orderTimeStr = mapValue;
                            if (StrUtil.isNotEmpty(orderTimeStr)){
                                LocalDateTime orderTimeParse = promptUtils.parseDateTime(orderTimeStr);
                                if (orderTimeParse!=null){
                                    orderParseTime = orderTimeParse.withHour(0).withMinute(0).withSecond(0);
                                }
                            }
                            continue;
                        }
                    }
                    if (mapKey.contains("时间") || mapKey.contains("日期")){
                        String orderTimeStr = mapValue;
                        if (StrUtil.isNotEmpty(orderTimeStr)){
                            LocalDateTime orderTimeParse = promptUtils.parseDateTime(orderTimeStr);
                            if (orderTimeParse!=null){
                                parseTime = orderTimeParse.withHour(0).withMinute(0).withSecond(0);
                            }
                        }
                        continue;
                    }
                    if (mapKey.contains("外卖")){
                        if (mapKey.contains("额")){
                            String takeawayAmountStr = mapValue;
                            String takeawayAmountEql = promptUtils.parseEquals(takeawayAmountStr);
                            if (StrUtil.isNotEmpty(takeawayAmountEql)){
                                takeawayAmount = new BigDecimal(takeawayAmountEql).setScale(2, RoundingMode.HALF_UP);
                            }
                            continue;
                        }
                        if (mapKey.contains("笔") || mapKey.contains("数")){
                            String takeawayOrderStr = mapValue;
                            String takeawayOrderEql = promptUtils.parseEquals(takeawayOrderStr);
                            if (StrUtil.isNotEmpty(takeawayOrderEql)){
                                takeawayOrder = Integer.valueOf(takeawayOrderEql);
                            }
                            continue;
                        }
                    }
                    if (mapKey.contains("销售")){
                        if (mapKey.contains("额")){
                            String totalAmountStr = mapValue;
                            String totalAmountEql = promptUtils.parseEquals(totalAmountStr);
                            if (StrUtil.isNotEmpty(totalAmountEql)){
                                totalAmount = new BigDecimal(totalAmountEql).setScale(2, RoundingMode.HALF_UP);
                            }
                            continue;
                        }
                        if (mapKey.contains("笔") || mapKey.contains("数")){
                            String totalOrderStr = mapValue;
                            String totalOrderEql = promptUtils.parseEquals(totalOrderStr);
                            if (StrUtil.isNotEmpty(totalOrderEql)){
                                totalOrder = Integer.valueOf(totalOrderEql);
                            }
                            continue;
                        }
                    }
                }

                if (orderParseTime==null && parseTime!=null){
                    orderParseTime = parseTime;
                }

                if (orderParseTime!=null){
                    orderTime =  orderParseTime;
                }

                saleTime = orderTime.toLocalDate();

                // 门店销售额
                if (totalAmount.compareTo(takeawayAmount)>0){
                    storeAmount = totalAmount.subtract(takeawayAmount).setScale(2, RoundingMode.HALF_UP);
                }

                // 门店外卖总笔数
                if (totalOrder >= takeawayOrder){
                    storeOrder = totalOrder - takeawayOrder;
                }else {
                    // 小于外卖总笔数 门店总笔数取外卖总笔数
                    totalOrder = takeawayOrder;
                }

                addDto.setParseContent(parseJson);
                addDto.setOrderNo(orderNo);
                addDto.setOrderTime(orderTime);
                addDto.setSaleTime(saleTime);
                addDto.setParseStatus(0);

                addDto.setTotalAmount(totalAmount);
                addDto.setTotalOrder(totalOrder);
                addDto.setTakeawayAmount(takeawayAmount);
                addDto.setTakeawayOrder(takeawayOrder);
                addDto.setStoreAmount(storeAmount);
                addDto.setStoreOrder(storeOrder);

                add(addDto);
            }
        }catch (Exception e){
            parseFlag = Boolean.FALSE;
            log.info("小票文本解析日结单异常:{}", e.getMessage());

            addDto.setParseContent(parseJson);
            addDto.setOrderNo(orderNo);
            addDto.setOrderTime(orderTime);
            addDto.setSaleTime(saleTime);
            addDto.setParseStatus(1);

            addDto.setTotalAmount(totalAmount);
            addDto.setTotalOrder(totalOrder);
            addDto.setTakeawayAmount(takeawayAmount);
            addDto.setTakeawayOrder(takeawayOrder);
            addDto.setStoreAmount(storeAmount);
            addDto.setStoreOrder(storeOrder);

            add(addDto);
        }

        return parseFlag;
    }

    @Override
    public List<IotDaySettleParseVo> getJobSaleData(LocalDate currentDate) {
        return iotDaySettleSaleDataService.getJobSaleData(currentDate);
    }

    @Override
    public void prompt(IotDaySettleSaleDataAddDto dto) {
        IotDaySettlePromptVo promptInfo = iotDaySettleSaleDataService.getPromptInfo();
        String daySettlePrompt = promptInfo.getDaySettlePrompt();
        String ticketText = dto.getTicketText();
        String deviceId = dto.getDeviceId();
        String ticketPic = dto.getTicketPic();
        Boolean flag = parseTextData(ticketText, daySettlePrompt, ticketPic, deviceId);
        log.info("flag:{}", flag);
    }

    /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<IotDaySettleSaleData> queryBuild(IotDaySettleSaleDataListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<IotDaySettleSaleData> queryBuild(IotDaySettleSaleDataListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<IotDaySettleSaleData> queryWrapper = new QueryWrapper<>();

        IotDaySettleSaleData entity = BeanUtils.copyProperties(dto, IotDaySettleSaleData.class);
	    entity.setDelStatus(0);

        if (dto.getCreateTimeStart() != null && dto.getCreateTimeEnd() != null) {
            queryWrapper.between("create_time", dto.getCreateTimeStart(), dto.getCreateTimeEnd());
        }

        if (dto.getOrderTimeStart() != null && dto.getOrderTimeEnd() != null) {
            queryWrapper.between("order_time", dto.getOrderTimeStart(), dto.getOrderTimeEnd());
        }

        if (StringUtils.isNotBlank(dto.getDeviceCode())) {
            entity.setDeviceCode(null);
            queryWrapper.like("device_code", dto.getDeviceCode());
        }

        if (StringUtils.isNotBlank(dto.getDeviceId())) {
            entity.setDeviceId(null);
            queryWrapper.like("device_id", dto.getDeviceId());
        }

        /** 添加条件样例参考，不用请删除
        if (StringUtils.isNotBlank(dto.getName())) {
            entity.setName(null);
            queryWrapper.like("name", dto.getName());
        }

        queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

        if (orderBy) {
            if (dto.getTypeOrder() != null) {
                queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
            }

            queryWrapper.orderByAsc("order_by");
        }
        */

        //按创建时间倒序排序，根据需要添加
        queryWrapper.orderByDesc("order_time").orderByAsc("shop_no");

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }
}
