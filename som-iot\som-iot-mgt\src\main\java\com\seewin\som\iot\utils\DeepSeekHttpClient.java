package com.seewin.som.iot.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class DeepSeekHttpClient {

    private static final String API_KEY = "sk-f3e23440b4ea4d419c7a3bbf6f3c2eea";
    private static final String API_URL = "https://api.deepseek.com/v1/chat/completions";

    public static String callDeepSeekAPI(String userMessage) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(API_URL);
            httpPost.setHeader("Authorization", "Bearer " + API_KEY);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");

            String requestBody = buildRequestBody(userMessage);
            httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));

            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                throw new RuntimeException("API 调用失败，状态码: " + statusCode);
            }
            String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            String content = null;
            try {
                JSONObject jsonResponse = JSONObject.parseObject(result);
                JSONArray choices = jsonResponse.getJSONArray("choices");
                if (!choices.isEmpty()) {
                    JSONObject firstChoice = choices.getJSONObject(0);
                    JSONObject message = firstChoice.getJSONObject("message");
                    content= message.getString("content");
                    return content;
                }
            } catch (Exception e) {
                log.info("API 调用失败，返回结果解析错误:{}", e.getMessage());
                //throw new RuntimeException("API 调用失败，返回结果解析错误: " + e.getMessage());
            }
            return content;
        }
    }

    private static String buildRequestBody(String userMessage) {
        JSONObject requestBody = new JSONObject();
        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();

        requestBody.put("model", "deepseek-chat");
        message.put("role", "user");
        message.put("content", userMessage);
        messages.add(message);
        requestBody.put("messages", messages);
        requestBody.put("stream", false);

        return requestBody.toJSONString();
    }

    /**
     * 从文本中提取JSON内容
     * @param input 输入文本
     * @return 提取到的JSON字符串，如果没有则返回null
     */
    public static String extractJson(String input) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }

        // 尝试直接提取最像JSON的部分
        String json = extractJsonLikeContent(input);
        return json;
    }

    /**
     * 在没有标记的情况下，尝试提取最像JSON的内容
     */
    private static String extractJsonLikeContent(String input) {
        // 移除<think>标签
        String cleanThink = input.replaceAll("(?i)<think>.*?</think>", "").trim();
        String cleaned = cleanThink.replaceAll("```json|```JSON|```Json|```", "").trim();

        // 尝试匹配最外层的大括号内容
        Pattern pattern = Pattern.compile("\\{.*\\}", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(cleaned);

        if (matcher.find()) {
            String candidate = matcher.group(0).trim();
            // 简单验证是否是合法JSON
            if (candidate.startsWith("{") && candidate.endsWith("}")) {
                return candidate;
            }
        }

        return cleaned;
    }

    public static void main(String[] args) throws Exception{
        String prompt = "# 角色\n" +
                "你是一个专业的数据解析助手，专注于小票数据解析。你的任务是准确提取小票文本中的相关字段信息，并严格按照规定格式输出纯 JSON 数据。\n" +
                "\n" +
                "\n" +
                "## 技能\n" +
                "### 技能 1: 解析小票数据\n" +
                "\n" +
                "1. 仔细分析输入的小票文本，依据给定的字段定义提取信息。\n" +
                "2. 对于小票类型，固定为日结单。\n" +
                "3. 准确提取 订单号（关键字可能为订单号、订单编号）、销售总额（包含外卖销售额，关键字可能为营业额、总收款、应收金额、应付金额、总金额、合计金额、营业实收）、销售总笔数（关键字可能为交易笔数）、外卖销售额（关键词可能为支付方式为美团外卖、京东外卖、饿了么外卖、抖音外卖的交易金额之和。）、外卖笔数（外卖销售笔数。关键词可能为支付方式为美团外卖、京东外卖、饿了么、抖音外卖外卖的交易笔数之和。）、订单时间（关键字可能为日结时间、营业日期、营业日、营业时间、开始时间），格式为 yyyy-MM-dd HH:mm:ss。\n" +
                "4. 营业日期后面如果有日期: 2025.06.17 (yyyy-MM-dd) 或 yyyy/MM/dd 这样的格式，那么就取其作为订单时间，时分秒使用00:00:00\n" +
                "5. 订单号如果提取不到，就给其赋值为无。订单时间如果找不到就再取制表时间、打印时间。\n" +
                "\n" +
                "### 技能 2: 输出数据\n" +
                "\n" +
                "1. 将提取的信息按照JSON格式输出，确保字段名称和格式与要求一致。\n" +
                "2. 不要输出任何思考过程、解释、换行符或额外文本，仅输出单行 JSON 数据。\n" +
                "\n" +
                "\n" +
                "## 输出示例\n" +
                "\n" +
                "### 示例一：日结单\n" +
                "\n" +
                "{\n" +
                "  \"小票类型\": \"日结单\",\n" +
                "  \"销售总额\": \"150.00\",\n" +
                "  \"销售总笔数\": \"3\",\n" +
                "  \"外卖销售总额\": \"150.00\",\n" +
                "  \"外卖销售笔数\": \"3\",\n" +
                "  \"订单时间\": \"2025-06-18 14:30:00\"\n" +
                "}\n" +
                "\n" +
                "\n" +
                "## 限制:\n" +
                "- 仅依据小票中的数据进行提取，不要自行猜测数据。\n" +
                "\n" +
                "- 输出必须为JSON格式，且字段名称和格式要与规定一致。\n" +
                "\n" +
                "- 不输出任何思考过程或额外说明，仅输出符合格式要求的JSON数据。";

        String contentJoin = "\n"+"要解析的小票文本内容如下: "+"\n";
        //String ticketText = "P创S   拌粉君深圳湾公园店                  【营业日报】营业日期: 2025.06.17~2025.06.17                 业务类型: 全部业务类型                          ================================================【商品销售汇总】         交易笔数                                    77笔营业流水                                 2463.61退货金额                                   62.30优惠金额                                  537.09营业实收                                 1926.52客单价                                     25.02------------------------------------------------【实收明细】             支付方式                    交易笔数    实收金额美团券                             3       58.69抖音券                             1        9.90微信                              33     1136.62京东外卖                          20      301.02美团外卖                          13      248.92饿了么外卖                         5       73.77人民币                             1       57.80支付宝                             1       39.80------------------------------------------------【优惠明细】             名称                        交易笔数    优惠金额京东外卖商家优惠                  20      149.04美团商家优惠                      13      146.60饿了么商家优惠                     5       97.09美团外卖服务费/佣金               13       74.58饿了么外卖服务费/佣金              5       26.49美团券折扣                         3       23.2188折（堂食折扣）                   3       10.08抖音券折扣                         1       10.00------------------------------------------------【券核销明细】           名称                            张数    实收金额美团券                                          【超值】南昌拌粉+卤味拼            1       25.90多多+绿豆沙                                     拌粉君·锅气炒粉单人餐             1       29.80拌粉君·锅气炒粉燕麦绿豆           1        2.99沙1份                                           抖音券                                          【新人尝鲜超值价】单南昌           1        9.90拌粉                                            ------------------------------------------------【业务类型】             名称                客单价  实收金额        占比堂食                 33.41   1302.81         68%外卖                 16.41    623.71         32%================================================制单人:深圳湾店专用号                           制表时间: 2025-06-17 21:27:53                   打印时间：2025-06-17 21:27:53                   ";
        //String ticketText = "门店销售统计================================================营业日期: 2025-07-08 至 2025-07-08订单渠道: 店内销售、小程序&APP、美&饿&抖&京、自助点餐------------------------------------------------出金: 0.00入金: 0.00总收入(销售总金额 + 出金 + 入金): 5317.20------------------------------------------------统计项 笔数 金额小程序&APP销售 213 1721.70抖音小程序销售 26 216.40美&饿&抖&京销售 207 2190.40店内销售 189 1188.70------------------------------------------------小程序&APP退单 -3 -31.00快手小程序退单 -1 -7.00店内退单 -1 -4.00------------------------------------------------销售总金额: 5317.20(销售总金额 = 商品总金额 + 餐盒费 + 自配送费 + 不找零金额)商品总金额: 5209.20餐盒费: 108.00客单数: 635客单价(销售总金额/客单数): 8.37------------------------------------------------优惠明细合计: -621.85------------------------------------------------优惠明细1.套餐优惠活动: -0.402.美团: -27.003.饿了么: -2.404.小程序: -50.455.抖音小程序: -22.206.京东秒送: -358.207.美团拼好饭: -161.20------------------------------------------------美&饿&抖&京佣金合计: 49.31(销售总金额 = 收款明细合计 - 店内销售优惠明细合计)店内销售优惠明细合计: -0.40收款明细合计: 5316.80------------------------------------------------收款明细1.支付宝: 222.002.微信: 773.803.现金: 192.504.美团: 244.005.京东秒送: 1308.206.抖音小程序: 216.407.饿了么: 16.208.小程序: 1715.709.美团团购: 493.0010.APP: 6.0011.快手小程序: 0.0012.美团拼好饭: 120.0013.美团在线点: 9.00------------------------------------------------预计结算合计: 191.80抖音券明细 张数 预计结算 面额1.奶茶三选一2次卡(抖音专享券)-1/2 1 5.40 6.002.青提肉多多(抖音专享券) 7 43.40 49.003.三拼霸霸奶茶/芋圆葡萄二选一(抖音专享) 1 7.10 8.004.桃喜芒芒(抖音专享券) 3 16.44 18.005.奶茶三选一2次卡(抖音专享券)-2/2 1 5.40 6.006.满杯百香果/蜜桃四季春二选一(抖音专享) 7 43.70 49.007.棒打鲜橙(抖音专享券) 1 5.30 6.008.茉莉奶绿/珍珠奶茶二选一(抖音专享券) 3 15.60 18.009.四季春轻乳茶(抖音专享券) 1 5.40 6.0010.冰鲜柠檬水3次卡(抖音专享券)-3/3 1 3.92 4.0011.草莓啵啵(抖音专享券) 1 8.10 9.0012.冰鲜柠檬水3次卡(抖音专享券)-2/3 2 7.84 8.0013.雪王大圣代系列三选一(抖音专享券) 1 5.30 6.0014.雪王雪顶咖啡/拿铁咖啡二选一(抖音专享) 3 18.90 21.00------------------------------------------------预计结算合计: 8.60三方兑换券明细 张数 预计结算 面额1.冰淇淋免单券 1 2.00 2.002.周边零食1元抵扣券 4 3.20 4.003.天猫-2元代金券 2 3.40 4.00================================================操作员: 94145601门店: 蜜雪冰城（西大地铁站H口店）时间: 2025-07-08 22:40:53地址: 地铁1号线西大站物业区A49/A05-2电话: 18877161373";
        //String ticketText = "小面巴士 【交班单】交班班次: 午班交班单号: 10751开班时间: 2025/07/08 08:50:59交班时间: 2025/07/08 22:00:10统计范围: (07/08 08:50-07/08 22:00)交班人: 收银员2收银员: 收银员2交班模式: 明交班------------------------------------------------ 网络异常交班时网络异常，收款统计中未包含会员、挂账还款、 订金、礼品卡等数据，如有现金收款，请手工录入现金收款-会员 --------------------------------现金收款-挂账还款 --------------------------------现金收款-订金 --------------------------------现金收款-礼品卡 -------------------------------------------------------------------------------- 交班汇总本班次收款 1,144.95现金收款 30.70应交备用金 0.00上交现金 30.70------------------------------------------------ 收款统计收款业务 收款笔数 收款金额营业收入 86笔 1,144.95------------------------------------------------ 收款构成收款方式 收款笔数 收款金额现金 3笔 30.70 人民币 3笔 30.70扫码支付 79笔 1,061.40 微信 65笔 867.20 支付宝 14笔 194.20美团/大众点评团购 2笔 19.80 【吃的饱饱的】秘制肉 2笔 19.80 酱拌面单人餐（含煎蛋 /加面）外卖 1笔 15.55 美团外卖 1笔 15.55美团/大众点评支付 1笔 17.50 微信 1笔 17.50------------------------------------------------ 收款构成-营业收入收款方式 收款笔数 收款金额现金 3笔 30.70 人民币 3笔 30.70扫码支付 79笔 1,061.40 微信 65笔 867.20 支付宝 14笔 194.20美团/大众点评团购 2笔 19.80 【吃的饱饱的】秘制肉 2笔 19.80 酱拌面单人餐（含煎蛋 /加面）外卖 1笔 15.55 美团外卖 1笔 15.55美团/大众点评支付 1笔 17.50 微信 1笔 17.50------------------------------------------------ 美团团购券统计券名称 验券数量";
        //String ticketText = "a 营业收款统计 LIGHT TEA浅茶（南宁西大 地铁站店）------------------------------------------------营业日期: 2025-07-08至2025-07-08时间范围: 2025-07-08 00:00至2025-07-08 23:59打印时间: 2025-07-08 21:53:10打印人: 卢祖辉------------------------------------------------ 收款统计说明：营业收入中已包含“会员卡/订金/挂账/礼品卡”消费收入收款方式 次/张数 金额收款合计 161次/7张 1,593.96营业收入 161次/7张 1,593.96------------------------------------------------ 收款构成-营业收入收款方式 次/张数 金额现金 2次 8.00 人民币 2次 8.00扫码支付 38次 502.70 微信 34次 450.70 支付宝 4次 52.00美团/大众点评团购 6次/6张 73.95 「山河特饮」2次卡！ 1次/1张 12.45 广西宝藏鲜奶茶（大杯 ）5选1 「新品·海盐沁云层」 1次/1张 14.90 玫珑顶瓜瓜 「鲜活可口」原叶鲜果 1次/1张 13.90 茶2选1 白桃沁雪「冻干草莓动 3次/3张 32.70 物奶油雪顶」抖音团购 1次/1张 14.90 N「山河特饮」广西宝 1次/1张 14.90 藏鲜奶茶（大杯）5选1外卖 114次 994.41 美团外卖 6次 64.22 饿了么外卖 108次 930.19-----------------------------------------------";
        //String ticketText = "门店销售统计================================================营业日期: 2025-07-10 至 2025-07-10订单渠道: 店内销售、小程序&APP、美&饿&抖&京、自助点餐------------------------------------------------出金: 0.00入金: 0.00总收入(销售总金额 + 出金 + 入金): 5138.60------------------------------------------------统计项 笔数 金额小程序&APP销售 197 1630.50抖音小程序销售 30 206.20快手小程序销售 1 8.00美&饿&抖&京销售 190 2047.40店内销售 185 1246.50------------------------------------------------小程序&APP退单 -1 -4.00美&饿&抖&京退单 -4 -61.20店内退单 -2 -33.00------------------------------------------------销售总金额: 5138.60(销售总金额 = 商品总金额 + 餐盒费 + 自配送费 + 不找零金额)商品总金额: 5034.00餐盒费: 104.60客单数: 603客单价(销售总金额/客单数): 8.52------------------------------------------------优惠明细合计: -613.21------------------------------------------------优惠明细1.套餐优惠活动: -0.102.美团: -25.123.饿了么: -5.604.小程序: -70.915.抖音小程序: -16.226.快手小程序: -0.967.京东秒送: -333.308.美团拼好饭: -161.00------------------------------------------------美&饿&抖&京佣金合计: 57.18(销售总金额 = 收款明细合计 - 店内销售优惠明细合计)店内销售优惠明细合计: -0.10收款明细合计: 5138.50------------------------------------------------收款明细1.支付宝: 235.902.微信: 895.003.现金: 115.504.美团: 218.805.京东秒送: 1200.206.抖音小程序: 206.207.APP: 2.008.快手小程序: 8.009.饿了么: 42.4010.美团拼好饭: 115.0011.小程序: 1628.5012.美团团购: 471.00------------------------------------------------预计结算合计: 173.78抖音券明细 张数 预计结算 面额1.青提肉多多(抖音专享券) 4 24.90 28.002.青提肉多多(抖音） 4 25.90 28.003.三拼霸霸奶茶/芋圆葡萄二选一(抖音专享) 2 14.20 16.004.冰鲜柠檬水3次卡(抖音专享券)-1/3 2 7.84 8.005.满杯百香果/蜜桃四季春二选一(抖音专享) 5 31.00 35.006.柠檬绿茶(抖音专享券) 1 4.90 5.007.棒打鲜橙(抖音专享券) 2 10.70 12.008.四季春轻乳茶(抖音专享券) 2 10.80 12.009.冰鲜柠檬水3次卡(抖音专享券)-3/3 4 15.68 16.0010.芝士奶盖茶二选一(抖音专享券) 1 7.20 8.0011.茉莉绿茶(抖音专享) 1 3.60 4.0012.冰鲜柠檬水3次卡(抖音专享券)-2/3 3 11.76 12.0013.雪王大圣代系列三选一(抖音专享券) 1 5.30 6.00------------------------------------------------预计结算合计: 7.04快手券明细 张数 预计结算 面额1.芋圆葡萄(快手专用券) 1 7.04 8.00------------------------------------------------预计结算合计: 26.09三方兑换券明细 张数 预计结算 面额1.周边零食1元抵扣券 2 1.60 2.002.农业银行7元代金券 1 6.09 7.003.天猫-4元代金券 1 3.40 4.004.15元代金券 1 15.00 15.00================================================操作员: 94145601门店: 蜜雪冰城（西大地铁站H口店）时间: 2025-07-11 12:17:11地址: 地铁1号线西大站物业区A49/A05-2电话: 18877161373";
        //String ticketText = "a 营业收款统计 LIGHT TEA浅茶（南宁西大 地铁站店）------------------------------------------------营业日期: 2025-07-10至2025-07-10时间范围: 2025-07-10 00:00至2025-07-10 23:59打印时间: 2025-07-10 21:55:08打印人: 卢祖辉------------------------------------------------ 收款统计说明：营业收入中已包含“会员卡/订金/挂账/礼品卡”消费收入收款方式 次/张数 金额收款合计 154次/9张 1,784.07营业收入 154次/9张 1,784.07------------------------------------------------ 收款构成-营业收入收款方式 次/张数 金额扫码支付 35次 548.20 微信 34次 532.20 支付宝 1次 16.00会员卡 1次 6.00 卡余额消费-储值余额 1次 6.00美团/大众点评团购 4次/5张 64.05 「山河特饮」2次卡！ 1次/1张 12.45 广西宝藏鲜奶茶（大杯 ）5选1 白桃沁雪「冻干草莓动 2次/3张 36.70 物奶油雪顶」 石崖不扰眠「开心果动 1次/1张 14.90 物奶油雪顶」抖音团购 3次/4张 54.60 N 白桃沁雪「冻干草莓 1次/1张 10.90 动物奶油雪顶+白桃乌 龙鲜奶茶」 N「鲜活可口」原叶鲜 1次/1张 13.90 果茶2选1 「山河特饮」广西宝藏 1次/2张 29.80 鲜奶茶（大杯）5选1外卖 111次 1,111.22 美团外卖 7次 232.84 饿了么外卖 104次 878.38------------------------------------------------\b\u0001\b";
        String ticketText = "门店销售统计================================================营业日期: 2025-07-08 至 2025-07-08订单渠道: 店内销售、小程序&APP、美&饿&抖&京、自助点餐------------------------------------------------出金: 0.00入金: 0.00总收入(销售总金额 + 出金 + 入金): 5317.20------------------------------------------------统计项 笔数 金额小程序&APP销售 213 1721.70抖音小程序销售 26 216.40美&饿&抖&京销售 207 2190.40店内销售 189 1188.70------------------------------------------------小程序&APP退单 -3 -31.00快手小程序退单 -1 -7.00店内退单 -1 -4.00------------------------------------------------销售总金额: 5317.20(销售总金额 = 商品总金额 + 餐盒费 + 自配送费 + 不找零金额)商品总金额: 5209.20餐盒费: 108.00客单数: 635客单价(销售总金额/客单数): 8.37------------------------------------------------优惠明细合计: -621.85------------------------------------------------优惠明细1.套餐优惠活动: -0.402.美团: -27.003.饿了么: -2.404.小程序: -50.455.抖音小程序: -22.206.京东秒送: -358.207.美团拼好饭: -161.20------------------------------------------------美&饿&抖&京佣金合计: 49.31(销售总金额 = 收款明细合计 - 店内销售优惠明细合计)店内销售优惠明细合计: -0.40收款明细合计: 5316.80------------------------------------------------收款明细1.支付宝: 222.002.微信: 773.803.现金: 192.504.美团: 244.005.京东秒送: 1308.206.抖音小程序: 216.407.饿了么: 16.208.小程序: 1715.709.美团团购: 493.0010.APP: 6.0011.快手小程序: 0.0012.美团拼好饭: 120.0013.美团在线点: 9.00------------------------------------------------预计结算合计: 191.80抖音券明细 张数 预计结算 面额1.奶茶三选一2次卡(抖音专享券)-1/2 1 5.40 6.002.青提肉多多(抖音专享券) 7 43.40 49.003.三拼霸霸奶茶/芋圆葡萄二选一(抖音专享) 1 7.10 8.004.桃喜芒芒(抖音专享券) 3 16.44 18.005.奶茶三选一2次卡(抖音专享券)-2/2 1 5.40 6.006.满杯百香果/蜜桃四季春二选一(抖音专享) 7 43.70 49.007.棒打鲜橙(抖音专享券) 1 5.30 6.008.茉莉奶绿/珍珠奶茶二选一(抖音专享券) 3 15.60 18.009.四季春轻乳茶(抖音专享券) 1 5.40 6.0010.冰鲜柠檬水3次卡(抖音专享券)-3/3 1 3.92 4.0011.草莓啵啵(抖音专享券) 1 8.10 9.0012.冰鲜柠檬水3次卡(抖音专享券)-2/3 2 7.84 8.0013.雪王大圣代系列三选一(抖音专享券) 1 5.30 6.0014.雪王雪顶咖啡/拿铁咖啡二选一(抖音专享) 3 18.90 21.00------------------------------------------------预计结算合计: 8.60三方兑换券明细 张数 预计结算 面额1.冰淇淋免单券 1 2.00 2.002.周边零食1元抵扣券 4 3.20 4.003.天猫-2元代金券 2 3.40 4.00================================================操作员: 94145601门店: 蜜雪冰城（西大地铁站H口店）时间: 2025-07-08 22:40:53地址: 地铁1号线西大站物业区A49/A05-2电话: 18877161373";

        String chat = DeepSeekHttpClient.callDeepSeekAPI(prompt+contentJoin+ticketText);
        //System.out.println(chat);

        String jsonText = extractJson(chat);
        System.out.println(jsonText);
    }

}