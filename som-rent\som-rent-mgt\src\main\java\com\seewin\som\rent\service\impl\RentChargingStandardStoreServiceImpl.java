package com.seewin.som.rent.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.seewin.model.query.PageQuery;
import com.seewin.som.rent.entity.RentChargingItem;
import com.seewin.som.rent.entity.RentChargingStandard;
import com.seewin.som.rent.entity.RentChargingStandardStore;
import com.seewin.som.rent.entity.RentChargingTenantItem;
import com.seewin.som.rent.mapper.RentChargingStandardStoreMapper;
import com.seewin.som.rent.req.RentChargingStandardStoreBindDto;
import com.seewin.som.rent.req.RentChargingStandardStoreListDto;
import com.seewin.som.rent.resp.RentChargingBaseListVo;
import com.seewin.som.rent.resp.RentChargingStandardStoreGetVo;
import com.seewin.som.rent.service.RentChargingItemService;
import com.seewin.som.rent.service.RentChargingStandardService;
import com.seewin.som.rent.service.RentChargingStandardStoreService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.seewin.som.rent.service.RentChargingTenantItemService;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 收费标准关联门店表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Service
public class RentChargingStandardStoreServiceImpl extends ServiceImpl<RentChargingStandardStoreMapper, RentChargingStandardStore> implements RentChargingStandardStoreService {

    @Autowired
    private RentChargingStandardService rentChargingStandardService;

    @Autowired
    private RentChargingItemService rentChargingItemService;

    @Autowired
    private RentChargingTenantItemService rentChargingTenantItemService;


    @Override
    public List<RentChargingBaseListVo> getRentChargingBaseList(RentChargingStandardStoreListDto dto) throws ServiceException {
        List<RentChargingBaseListVo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dto.getContractCodeList())) {
            //构造查询条件
            QueryWrapper<RentChargingStandardStore> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("del_status", 0);
            queryWrapper.in("contract_code", dto.getContractCodeList());

            //查询标准关联门店数据
            List<RentChargingStandardStore> rentChargingStandardStoreList = list(queryWrapper);

            // 为空返回空数组
            if (CollectionUtil.isEmpty(rentChargingStandardStoreList)) {
                log.warn("rentChargingStandardStoreList is empty!");
                return result;
            }

            // 标准数据
            List<Long> chargingStandardIdList = new ArrayList<>();

            // 项目（租户）数据
            List<Long> tenantIdList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(rentChargingStandardStoreList)) {
                for (RentChargingStandardStore standardStore : rentChargingStandardStoreList) {
                    chargingStandardIdList.add(standardStore.getChargingStandardId());
                    tenantIdList.add(standardStore.getTenantId());
                }
            }
            Map<Long, RentChargingStandard> standarIdObjMap = new HashMap<>();

            Map<Long, RentChargingItem> itemIdObjMap = new HashMap<>();

            // key String类型  tenantId-itemId
            Map<String, RentChargingTenantItem> rentChargingTenantItemMap = new HashMap<>();

            if (CollectionUtil.isNotEmpty(chargingStandardIdList)) {
                //构造查询条件
                QueryWrapper<RentChargingStandard> standardQueryWrapper = new QueryWrapper<>();
                standardQueryWrapper.eq("del_status", 0);
                standardQueryWrapper.in("id", chargingStandardIdList);
                // 查询到的标准数据
                List<RentChargingStandard> rentChargingStandardList = rentChargingStandardService.list(standardQueryWrapper);
                // 为空返回空数组
                if (CollectionUtil.isEmpty(rentChargingStandardList)) {
                    log.warn("rentChargingStandardList is empty!");
                    return result;
                }

                if (CollectionUtil.isNotEmpty(rentChargingStandardList)) {
                    for (RentChargingStandard standard : rentChargingStandardList) {
                        standarIdObjMap.put(standard.getId(), standard);
                    }
                }

                // 项目数据
                List<Long> chargingItemIdList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(rentChargingStandardList)) {
                    for (RentChargingStandard standard : rentChargingStandardList) {
                        chargingItemIdList.add(standard.getChargingItemId());
                    }
                }

                if (CollectionUtil.isNotEmpty(chargingItemIdList)) {
                    //构造查询条件
                    QueryWrapper<RentChargingItem> itemQueryWrapper = new QueryWrapper<>();
                    itemQueryWrapper.eq("del_status", 0);
                    itemQueryWrapper.in("id", chargingItemIdList);
                    // 查询到的项目数据
                    List<RentChargingItem> rentChargingItemList = rentChargingItemService.list(itemQueryWrapper);

                    // 处理项目税率
                    //构造查询条件
                    QueryWrapper<RentChargingTenantItem> tenantItemQueryWrapper = new QueryWrapper<>();
                    tenantItemQueryWrapper.in("tenant_id", tenantIdList);
                    tenantItemQueryWrapper.eq("del_status", 0);
                    List<RentChargingTenantItem> rentChargingTenantItemList = rentChargingTenantItemService.list(tenantItemQueryWrapper);
                    if (CollectionUtil.isNotEmpty(rentChargingTenantItemList)) {
                        for (RentChargingTenantItem tenantItem : rentChargingTenantItemList) {
                            String key = tenantItem.getTenantId() + "-" + tenantItem.getChargingItemId();
                            rentChargingTenantItemMap.put(key, tenantItem);
                        }
                    }

                    // 为空返回空数组
                    if (CollectionUtil.isEmpty(rentChargingItemList)) {
                        log.warn("rentChargingItemList is empty!");
                        return result;
                    }

                    if (CollectionUtil.isNotEmpty(rentChargingItemList)) {
                        for (RentChargingItem item : rentChargingItemList) {
                            itemIdObjMap.put(item.getId(), item);
                        }
                    }
                }
            }

            if (CollectionUtil.isNotEmpty(rentChargingStandardStoreList)) {
                for (RentChargingStandardStore standardStore : rentChargingStandardStoreList) {
                    RentChargingBaseListVo vo = buildRentChargingBaseListVo(standardStore, standarIdObjMap, itemIdObjMap, rentChargingTenantItemMap);
                    result.add(vo);
                }
            }

            //返回查询结果
            return result;

        }
        return result;
    }

    private RentChargingBaseListVo buildRentChargingBaseListVo(RentChargingStandardStore standardStore, Map<Long, RentChargingStandard> standarIdObjMap, Map<Long, RentChargingItem> itemIdObjMap, Map<String, RentChargingTenantItem> rentChargingTenantItemMap) {
        RentChargingBaseListVo vo = new RentChargingBaseListVo();
        // 1. 标准绑定门店信息 前缀bind
        vo.setBindChargingStandardId(standardStore.getChargingStandardId());
        vo.setBindFcode(standardStore.getFcode());
        vo.setBindFid(standardStore.getFid());
        vo.setBindFname(standardStore.getFname());
        vo.setBindEntId(standardStore.getEntId());
        vo.setBindOrgFid(standardStore.getOrgFid());
        vo.setBindOrgFname(standardStore.getOrgFname());
        vo.setBindTenantId(standardStore.getTenantId());
        vo.setBindTenantName(standardStore.getTenantName());
        vo.setBindRoomId(standardStore.getRoomId());
        vo.setBindRoomName(standardStore.getRoomName());
        vo.setBindStoreId(standardStore.getStoreId());
        vo.setBindContractCode(standardStore.getContractCode());
        vo.setBindCommonContractType(standardStore.getCommonContractType());
        vo.setBindAdvertContractType(standardStore.getAdvertContractType());
        vo.setBindAdvertName(standardStore.getAdvertName());
        vo.setBindShopType(standardStore.getShopType());
        vo.setBindApplyPaymentTermSrart(standardStore.getApplyPaymentTermSrart());
        vo.setBindApplyPaymentTermEnd(standardStore.getApplyPaymentTermEnd());
        vo.setCreateBy(standardStore.getCreateBy());
        vo.setCreateUser(standardStore.getCreateUser());
        vo.setCreateUserName(standardStore.getCreateUserName());
        vo.setCreateTime(standardStore.getCreateTime());
        vo.setUpdateBy(standardStore.getUpdateBy());
        vo.setUpdateUser(standardStore.getUpdateUser());
        vo.setUpdateUserName(standardStore.getUpdateUserName());
        vo.setUpdateTime(standardStore.getUpdateTime());

        Long tenantId = standardStore.getTenantId();

        // 2.标准信息
        if (standardStore.getChargingStandardId() != null) {
            RentChargingStandard standard = standarIdObjMap.get(standardStore.getChargingStandardId());
            if (standard != null) {
                vo.setChargingItemId(standard.getChargingItemId());
                vo.setName(standard.getName());
                vo.setStandardCode(standard.getStandardCode());
                vo.setDataSource(standard.getDataSource());
                vo.setType(standard.getType());
                vo.setCalType(standard.getCalType());
                vo.setExpressionCode(standard.getExpressionCode());
                vo.setExpressionName(standard.getExpressionName());
                vo.setExpressionDesc(standard.getExpressionDesc());
                vo.setExpression(standard.getExpression());
                vo.setParamInputFlag(standard.getParamInputFlag());
                vo.setParams(standard.getParams());
                vo.setBillingPeriodNum(standard.getBillingPeriodNum());
                vo.setBillingPeriodUnit(standard.getBillingPeriodUnit());
                vo.setManuallyAmount(standard.getManuallyAmount());
                vo.setLatePaymentStartDate(standard.getLatePaymentStartDate());
                vo.setLatePaymentRate(standard.getLatePaymentRate());
                vo.setReadingFlag(standard.getReadingFlag());
                vo.setMagnificationFlag(standard.getMagnificationFlag());
                vo.setPriceFlag(standard.getPriceFlag());

                Long itemId = standard.getChargingItemId();

                // 3.项目信息 前缀item
                if (standard.getChargingItemId() != null) {
                    RentChargingItem item = itemIdObjMap.get(standard.getChargingItemId());
                    if (item != null) {
                        vo.setItemCode(item.getCode());
                        vo.setItemType(item.getType());
                        vo.setItemLevel(item.getLevel());
                        vo.setItemFeeName(item.getFeeName());
                        vo.setItemExtendFeeName(item.getExtendFeeName());
                        vo.setItemDataSource(item.getDataSource());
                        // 处理税率
                        if (tenantId != null && itemId != null) {
                            String key = tenantId + "-" + itemId;
                            RentChargingTenantItem rentChargingTenantItem = rentChargingTenantItemMap.get(key);
                            if (rentChargingTenantItem != null) {
                                vo.setItemTaxRate(rentChargingTenantItem.getTaxRate());
                                vo.setItemTaxCategoryCode(rentChargingTenantItem.getTaxCategoryCode());
                                vo.setItemTaxCategoryName(rentChargingTenantItem.getTaxCategoryName());
                                vo.setItemTaxExtendName(rentChargingTenantItem.getTaxExtendName());
                                vo.setItemPreferentialFlag(rentChargingTenantItem.getPreferentialFlag());
                                vo.setItemVoucherTenantInfoId(rentChargingTenantItem.getVoucherTenantInfoId());
                                vo.setItemAccountingOrgName(rentChargingTenantItem.getAccountingOrgName());
                                vo.setItemAccountingOrgCode(rentChargingTenantItem.getAccountingOrgCode());
                            }
                        }
                    }
                }
            }
        }

        return vo;
    }

}
