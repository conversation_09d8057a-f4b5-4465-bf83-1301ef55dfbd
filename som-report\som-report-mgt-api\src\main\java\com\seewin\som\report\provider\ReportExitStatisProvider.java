package com.seewin.som.report.provider;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import com.seewin.util.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目门店撤场数据分析 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29
 */
public interface ReportExitStatisProvider {

	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    PageResult<ReportExitStatisListVo> page(PageQuery<ReportExitStatisListDto> pageQuery) throws ServiceException;

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    List<ReportExitStatisListVo> list(ReportExitStatisListDto dto) throws ServiceException;

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    int count(ReportExitStatisListDto dto) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportExitStatisGetVo get(Long id) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportExitStatisGetVo get(ReportExitStatisListDto dto) throws ServiceException;


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    ReportExitStatisAddVo add(ReportExitStatisAddDto dto) throws ServiceException;


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    void edit(ReportExitStatisEditDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    void delete(Long id) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(ReportExitStatisListDto dto) throws ServiceException;

    void saveBatch(List<ReportExitStatisEditDto> listDto) throws ServiceException;

    List<ReportExitVo> exitAreaRateByDay(ReportMultiDataDto curMultiDataDto);

    List<ReportExitVo> exitAreaRateByMonth(ReportMultiDataDto curMultiDataDto);

    List<ReportExitStatisListVo> getAllData(ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityAllData(ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityDateAllData(ReportOperateDayAnalyseDto dto);
}
