package com.seewin.som.report.provider;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.som.report.req.MultiDataAnalyseDto;
import com.seewin.som.report.resp.*;
import com.seewin.som.report.resp.SaleDataAnalyseVo;
import com.seewin.som.report.req.*;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.report.req.ReportMasterSaleDataAddDto;
import com.seewin.som.report.req.ReportMasterSaleDataEditDto;
import com.seewin.som.report.req.ReportMasterSaleDataListDto;
import com.seewin.som.report.resp.ReportMasterSaleDataAddVo;
import com.seewin.som.report.resp.ReportMasterSaleDataGetVo;
import com.seewin.som.report.resp.ReportMasterSaleDataListVo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 主数据库销售数据 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
public interface ReportMasterSaleDataProvider {

    /**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    PageResult<ReportMasterSaleDataListVo> page(PageQuery<ReportMasterSaleDataListDto> pageQuery) throws ServiceException;

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    List<ReportMasterSaleDataListVo> list(ReportMasterSaleDataListDto dto) throws ServiceException;

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    int count(ReportMasterSaleDataListDto dto) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportMasterSaleDataGetVo get(Long id) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportMasterSaleDataGetVo get(ReportMasterSaleDataListDto dto) throws ServiceException;
    BigDecimal getMonthSaleTotal(String objCode, LocalDate startDate,LocalDate endDate) throws ServiceException;


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    ReportMasterSaleDataAddVo add(ReportMasterSaleDataAddDto dto) throws ServiceException;
    /**
     * 批量新增
     * <AUTHOR>
     * @date 2024/5/30 15:44
     * @param dtos
     * @return boolean
     */
    boolean saveBatch( List<ReportMasterSaleDataAddDto> dtos) throws ServiceException;

    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    void edit(ReportMasterSaleDataEditDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    void delete(Long id) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(ReportMasterSaleDataListDto dto) throws ServiceException;

    void accSaleData(ReportMasterSaleDataAddDto addDto) throws ServiceException;

    Double storePreTaxProfit(ReportMasterSaleDataListDto dto) throws ServiceException;

    List<ReportMasterSaleDataGetVo> getLastWeekTotalSales(String shopNo, String startDate, String endDate, Long tenantId);

    BigDecimal getTotalAmountSum(ReportMasterSaleDataListDto dto);

    void editBatch(List<ReportMasterSaleDataEditDto> reportMasterSaleDataEditDtos);

    List<SaleDataAnalyseVo> saleAnalyse(MultiDataAnalyseDto dto);

    SaleDataAnalyseVo saleAnalyseMonth(MultiDataAnalyseDto dto);

    List<SalePerOrderAnalyseVo> salePerOrderAnalyse(SalePerOrderAnalyseDto dto);

    SaleAnalyseTenantDetailVo saleAnalyseTenantDetail(MultiDataAnalyseDto dto);

    SalePerOrderAnalyseTenantDetailVo salePerOrderAnalyseTenantDetail(MultiDataAnalyseDto dto);

    SaleAnalyseFloorDetailVo saleAnalyseFloorDetail(MultiDataAnalyseDto dto);

    SalePerOrderAnalyseFloorDetailVo salePerOrderAnalyseFloorDetail(MultiDataAnalyseDto dto);

    List<SalePerOrderAnalyseContrast> salePerOrderAnalyseContrast(MultiDataAnalyseDto dto);

    /**
     * 统计销售数据
     *
     * @param req
     * @return
     * @throws ServiceException
     */
    Map<String, BigDecimal> statisticStoreSalesData(MasterDataStatReq req) throws ServiceException;

    /**
     * 获取招调
     * 项目周销售额
     * 项目总客流
     * @param tenantId  项目ID
     * @param roomNo    铺位号
     * @param fname     空间完整名称
     * @return
     */
    ProjectInformationVo getProjectInformation(Long tenantId, String roomNo, String fname);

    /**
     * 获取同品类对比排名-销售额对比
     * @param dto
     * @return
     */
    List<ReportCategoryCompareVo> categorySaleAmountCompare(ReportCategoryCompareDto dto);

    /**
     * 获取同品类对比排名-客单价对比
     * @param dto
     * @return
     */
    List<ReportCategoryCompareVo> categoryGuestAvgPriceCompare(ReportCategoryCompareDto dto);

    /**
     * 根据时间范围统计销售转化率柱状图数据
     * @param multiDataDto 时间范围、门店
     */
    List<ReportMultiDataVo> saleConvertRate(ReportMultiDataDto multiDataDto);

    /**
     * 分组获取店铺月销售总额
     * @param saleMonth
     * @return
     */
    List<SaleDataMonthTotalAmountVo> selectMonthTotalAmount(String saleMonth);

    /**
     * 根据时间范围统计各门店销售转化率集合
     * @param curMultiDataDto 时间范围、门店集合
     */
    List<ReportMultiDataDetailVo> saleConvertStoreContrast(ReportMultiDataDto curMultiDataDto);

    /**
     * 门店销售额
     */
    BigDecimal getMonthSaleTotalTenant(Long tenantId, String storeId, LocalDate lastMonthFirstDay, LocalDate lastMonthLastDay);

    /**
     * 根据时间范围统计销售坪效柱状图数据
     * @param multiDataDto 时间范围
     */
    List<ReportMultiDataVo> saleSquare(ReportMultiDataDto multiDataDto);

    /**
     * 根据门店id获取业态id
     * @param multiDataDto 门店id
     * @return 业态id
     */
    Long getFormatIdByStoreId(ReportMultiDataDto multiDataDto);
    /**
     * 获取StoreId与SaleTime连接字符串(用于判断店铺是否存在某销售日期数据)
     * <AUTHOR>
     * @date 2024/6/5 15:25
     * @param tenantId
     * @param objCodes
     * @return java.util.Set<java.lang.String>
     */
    Set<String> selectStoreIdSaleTime(long tenantId, List<String> objCodes);

    /**
     * 获取多维数据导出的数据
     * @param curMultiDataDto
     * @return
     */
    ReportMultiDataProjectVo getExportData(ReportMultiDataDto curMultiDataDto);

    /**
     * 按门店分组获取门店详情数据
     * @param curMultiDataDto
     * @return
     */
    List<ReportMultiDataStoreVo> getStoreSaleData(ReportMultiDataDto curMultiDataDto);

    BigDecimal getSaleTotalById(Long tenantId, String storeId, LocalDate periodStart, LocalDate periodEnd);

    /**
     * 拓店管理-销售分析
     * @param dto
     * @return
     */
    List<SaleDataCategoryAnalyseVo> categoryAnalyseStatistic(CategoryAnalyseDto dto);

    /**
     * 店铺销售额排名前3
     * @param dto
     * @return
     */
    List<String> saleRank(CategoryAnalyseDto dto);

    /**
     * 拓店管理-销售分析-详情
     * @param dto
     * @return
     */
    List<SaleDataCategoryAnalyseVo> categoryAnalyseStatisticDetail(CategoryAnalyseDto dto);

    BigDecimal getSaleTotalAmount(ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleDistributeList(ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleAmountByDate(ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleAnalyseList(ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleDetailByGatherType(ReportMultiAnalyseListDto dto);

    List<ReportSaleAndFlowListVo>  getOperationReport(OperationReportListDto dto) throws ServiceException;

    List<SaleDataAnalyseVo> saleAmount(MultiDataAnalyseDto dto);

    List<SaleDataAnalyseVo> salePredictAmount(MultiDataAnalyseDto dto);

    List<SaleDataAnalyseVo> salePredictStoreAmount(MultiDataAnalyseDto dto);

    PredictDataVo getPredictData(PredictDataDto dto);

    PredictDataVo getPredictStoreData(PredictDataDto dto);

    List<ReportSaleRecommendVo> getRecommendSaleTotal(ReportMasterSaleDataListDto saleListDto);

    /**
     * 查询客流、销售额、租管费 汇总查询
     */
    List<ManageAnalyseVo> getFlowAmountFeeByProject(ReportManageMultiAnalyseListDto dto);
}
