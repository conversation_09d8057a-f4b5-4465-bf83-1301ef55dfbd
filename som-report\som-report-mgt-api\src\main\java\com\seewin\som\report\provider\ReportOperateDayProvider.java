package com.seewin.som.report.provider;

import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import com.seewin.util.exception.ServiceException;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目面积数据统计到天 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
public interface ReportOperateDayProvider {

    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    ReportOperateDayAddVo add(ReportOperateDayAddDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(ReportOperateDayListDto dto) throws ServiceException;

    /**
     * 根据时间范围统计租赁/出租率柱状图数据
     * @param dto 时间范围查询
     */
    List<ReportMultiDataVo> rentRate(ReportMultiDataDto dto);
    /**
     * 根据时间范围统计开业率柱状图数据
     * @param dto 时间范围查询
     */
    List<ReportMultiDataVo> openRate(ReportMultiDataDto dto);
    /**
     * 根据时间范围统计开业率柱状图数据 开业面积
     * @param dto 时间范围查询
     */
    List<ReportMultiDataVo> openArea(ReportMultiDataDto dto);

    /**
     * 根据时间范围统计各门店租售比集合
     * @param dto 时间范围、楼层、门店集合
     */
    List<ReportMultiDataDetailVo> saleSquareStoreContrast(ReportMultiDataDto dto);

    /**
     * 根据时间范围统计项目、楼层所有店铺实用面积
     * @param dto 时间范围、楼层
     * @return
     */
    Double saleSquareArea(ReportMultiDataDto dto);

    /**
     * 根据时间范围统计项目下各楼层销售转化率集合
     * @param dto 时间范围
     */
    List<ReportMultiDataDetailVo> saleSquareTenantDetail(ReportMultiDataDto dto);

    List<ReportOperateDayStatisticVo> getOperatingRooms(CategoryAnalyseDto dto);

    List<ReportOperateDayStatisticVo> getOperatingRoomsDetail(CategoryAnalyseDto dto);

    List<ReportMultiAnalyseVo> multiAnalyseList(ReportMultiAnalyseListDto dto);

    List<ReportOperateDayListVo> list(ReportOperateDayListDto dto) throws ServiceException;

    List<ReportOperateDayListVo> getOperateDayAndFlowList(ReportOperateDayListDto dto);

    List<ReportOperateDayAnalyseListVo> getAllData(ReportOperateDayAnalyseDto dto);

    List<ReportOperateDayAnalyseListVo> getOpenAllData(ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityAllData(ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityOpenAllData(ReportOperateDayAnalyseDto dto);
}
