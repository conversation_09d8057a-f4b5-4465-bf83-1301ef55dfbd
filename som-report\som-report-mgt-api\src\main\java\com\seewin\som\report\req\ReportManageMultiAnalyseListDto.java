package com.seewin.som.report.req;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 经营多维数据分析入参
 * </p>
 */
@Getter
@Setter
public class ReportManageMultiAnalyseListDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private LocalDate startDate;

    private LocalDate endDate;

    private List<Long> tenantIdList;

    /**
     * 指标类型: 0-租售比 1-销售转化率 2-销售坪效 3-全查
     */
    private Integer indicatorType;
    /**
     * 时间类型: 0-日 1-月 2-全部
     */
    private Integer timeType;

    /**
     * 查询类型: 0-全国 1-城市 2-项目
     */
    private Integer searchType;

    /**
     * 汇总类型: 0-按业态汇总 1-按一级品类汇总 2-按二级品类汇总 3-按项目分组
     */
    private Integer gatherType;
}
