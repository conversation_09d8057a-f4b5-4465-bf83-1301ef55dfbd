package com.seewin.som.report.req;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 多维数据分析入参
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Getter
@Setter
public class ReportOperateDayAnalyseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private LocalDate startDate;

    private LocalDate endDate;

    private List<Long> tenantIdList;
    /**
     * 汇总类型: 0-按业态汇总 1-按一级品类汇总 2-按二级品类汇总
     */
    private Integer gatherType;

    /**
     * 查询类型: 0-全国 1-城市 2-项目
     */
    private Integer searchType;

    private List<String> tenantNames;
}
