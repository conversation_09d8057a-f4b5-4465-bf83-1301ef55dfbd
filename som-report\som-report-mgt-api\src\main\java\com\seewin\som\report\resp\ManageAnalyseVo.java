package com.seewin.som.report.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class ManageAnalyseVo implements Serializable {
    /**
     * 销售日期
     */
    private String saleTime;

    /**
     * 城市
     */
    private String city;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 业态
     */
    private String commercialTypeName;

    /**
     * 一级品类名称
     */
    private String categoryName;

    /**
     * 二级类品名称
     */
    private String commercialTwo;

    /**
     * 进店客流
     */
    private BigDecimal inshopCount;

    /**
     * 总销售额
     */
    private BigDecimal totalAmount;

    /**
     * 总订单量
     */
    private BigDecimal totalOrder;

    /**
     * 总费用 租金+管理费 (均值)
     */
    private BigDecimal theoryRent;

    /**
     * 店铺面积
     */
    private BigDecimal totalRoomArea;
}
