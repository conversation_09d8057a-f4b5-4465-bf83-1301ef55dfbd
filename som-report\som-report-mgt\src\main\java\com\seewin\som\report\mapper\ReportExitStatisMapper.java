package com.seewin.som.report.mapper;

import com.seewin.som.report.entity.ReportExitStatis;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seewin.som.report.req.ReportMultiDataDto;
import com.seewin.som.report.req.ReportOperateDayAnalyseDto;
import com.seewin.som.report.resp.ReportExitStatisListVo;
import com.seewin.som.report.resp.ReportExitVo;
import com.seewin.som.report.resp.ReportMultiDataVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目门店撤场数据分析 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29
 */
public interface ReportExitStatisMapper extends BaseMapper<ReportExitStatis> {

    List<ReportExitVo> exitAreaRateByDay(@Param("dto")ReportMultiDataDto dto);

    List<ReportExitVo> exitAreaRateByMonth(@Param("dto")ReportMultiDataDto dto);

    List<ReportExitStatisListVo> getAllData(@Param("dto") ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityAllData(@Param("dto") ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityDateAllData(@Param("dto") ReportOperateDayAnalyseDto dto);
}
