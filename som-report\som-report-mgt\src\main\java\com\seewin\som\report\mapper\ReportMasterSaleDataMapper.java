package com.seewin.som.report.mapper;

import com.seewin.som.report.entity.ReportMasterSaleData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 主数据库销售数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
public interface ReportMasterSaleDataMapper extends BaseMapper<ReportMasterSaleData> {
    BigDecimal getTotalAmountSum(@Param("dto") ReportMasterSaleDataListDto dto);

    void accReportSaleData(@Param("item") ReportMasterSaleData iotMasterSaleData);

    List<ReportMasterSaleData> statisticData(@Param("req") MasterDataStatReq req);

    List<ReportCategoryCompareVo> categorySaleAmountCompare(@Param("dto") ReportCategoryCompareDto dto);

    List<ReportCategoryCompareVo> categoryGuestAvgPriceCompare(@Param("dto") ReportCategoryCompareDto dto);

    List<ReportMultiDataVo> saleConvertRate(@Param("dto") ReportMultiDataDto dto);
    /**
     * 分组获取店铺月销售总额
     * @param saleMonth
     * @return
     */
    List<SaleDataMonthTotalAmountVo> selectMonthTotalAmount(@Param("saleMonth") String saleMonth);

    List<ReportMultiDataDetailVo> saleConvertStoreContrast(@Param("dto") ReportMultiDataDto dto);

    List<ReportMultiDataVo> saleSquare(@Param("dto") ReportMultiDataDto dto);

    /**
     * 获取StoreId与SaleTime连接字符串(用于判断店铺是否存在某销售日期数据)
     * <AUTHOR>
     * @date 2024/6/5 15:25
     * @param tenantId
     * @param objCodes
     * @return java.util.Set<java.lang.String>
     */
    Set<String> selectStoreIdSaleTime(@Param("tenantId") long tenantId, @Param("objCodes") List<String> objCodes);

    ReportMultiDataProjectVo getExportData(@Param("dto") ReportMultiDataDto dto);

    List<ReportMultiDataStoreVo> getStoreSaleData(@Param("dto") ReportMultiDataDto dto);

    BigDecimal getSaleTotalById(@Param("tenantId") Long tenantId, @Param("storeId") String storeId, @Param("periodStart") LocalDate periodStart, @Param("periodEnd") LocalDate periodEnd);

    List<SaleDataCategoryAnalyseVo> categoryAnalyseStatistic(@Param("dto") CategoryAnalyseDto dto);

    List<String> saleRank(@Param("dto") CategoryAnalyseDto dto);

    List<SaleDataCategoryAnalyseVo> categoryAnalyseStatisticDetail(@Param("dto") CategoryAnalyseDto dto);

    BigDecimal getSaleTotalAmount(@Param("dto") ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleDistributeList(@Param("dto") ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleAmountByDate(@Param("dto") ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleAnalyseList(@Param("dto") ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleDetailByGatherType(@Param("dto")ReportMultiAnalyseListDto dto);

    List<ReportSaleAndFlowListVo> getOperationReport(@Param("dto")OperationReportListDto dto);

    List<SaleDataAnalyseVo> saleAmount(@Param("dto") MultiDataAnalyseDto dto);

    List<SaleDataAnalyseVo> salePredictAmount(@Param("dto") MultiDataAnalyseDto dto);

    List<SaleDataAnalyseVo> salePredictStoreAmount(@Param("dto") MultiDataAnalyseDto dto);

    PredictDataVo getPredictData(@Param("dto") PredictDataDto dto);

    PredictDataVo getPredictStoreData(@Param("dto") PredictDataDto dto);

    List<ReportSaleRecommendVo> getRecommendSaleTotal(@Param("dto") ReportMasterSaleDataListDto dto);

    /**
     * 查询客流、销售额、租管费 汇总查询
     */
    List<ManageAnalyseVo> getFlowAmountFeeByProject(@Param("dto") ReportManageMultiAnalyseListDto dto);
}
