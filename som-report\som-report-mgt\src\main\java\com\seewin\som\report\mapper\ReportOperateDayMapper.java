package com.seewin.som.report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seewin.som.report.entity.ReportOperateDay;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目面积数据统计到天 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
public interface ReportOperateDayMapper extends BaseMapper<ReportOperateDay> {

    List<ReportMultiDataVo> rentRate(@Param("dto") ReportMultiDataDto dto);

    List<ReportMultiDataVo> openRate(@Param("dto")ReportMultiDataDto dto);

    List<ReportMultiDataVo> openArea(@Param("dto")ReportMultiDataDto dto);

    List<ReportMultiDataDetailVo> saleSquareStoreContrast(@Param("dto") ReportMultiDataDto dto);

    Double saleSquareArea(@Param("dto") ReportMultiDataDto dto);

    List<ReportMultiDataDetailVo> saleSquareTenantDetail(@Param("dto") ReportMultiDataDto dto);

    List<ReportMultiDataVo> getRoomAreaByData(@Param("dto") MobileSaleDataAnalyseDto dto);

    List<ReportOperateDayStatisticVo> getOperatingRooms(@Param("dto") CategoryAnalyseDto dto);

    List<ReportOperateDayStatisticVo> getOperatingRoomsDetail(@Param("dto") CategoryAnalyseDto dto);

    List<ReportMultiAnalyseVo> multiAnalyseList(@Param("dto") ReportMultiAnalyseListDto dto);
    List<ReportOperateDayListVo> getOperateDayAndFlowList(@Param("dto") ReportOperateDayListDto dto);

    List<ReportOperateDayAnalyseListVo> getAllData(@Param("dto")ReportOperateDayAnalyseDto dto);

    Double getYoyAndQoqRate(@Param("dto")ReportOperateDayAnalyseDto dto);

    List<ReportOperateDayAnalyseListVo> getOpenAllData(@Param("dto")ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityAllData(@Param("dto")ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityOpenAllData(@Param("dto")ReportOperateDayAnalyseDto dto);
}
