package com.seewin.som.report.provider.impl;

import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.report.entity.ReportExitStatis;
import com.seewin.util.bean.BeanUtils;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.seewin.som.report.service.ReportExitStatisService;
import com.seewin.som.report.provider.ReportExitStatisProvider;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 项目门店撤场数据分析 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29
 */
@DubboService
public class ReportExitStatisProviderImpl implements ReportExitStatisProvider{

	@Autowired
	private ReportExitStatisService reportExitStatisService;
	
	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<ReportExitStatisListVo> page(PageQuery<ReportExitStatisListDto> pageQuery) throws ServiceException
    {
    	ReportExitStatisListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<ReportExitStatis> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<ReportExitStatis> queryWrapper = queryBuild(dto);

        //查询数据
        page = reportExitStatisService.page(page, queryWrapper);
        List<ReportExitStatis> records = page.getRecords();

        //响应结果封装
        PageResult<ReportExitStatisListVo> result = new PageResult<>();
        List<ReportExitStatisListVo> items = BeanUtils.copyProperties(records, ReportExitStatisListVo.class);
        
        result.setItems(items);
        result.setPages((int)page.getPages());
        result.setTotal((int)page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<ReportExitStatisListVo> list(ReportExitStatisListDto dto) throws ServiceException
    {
    	  //构造查询条件
        QueryWrapper<ReportExitStatis> queryWrapper = queryBuild(dto);

        //查询数据
        List<ReportExitStatis> records = reportExitStatisService.list(queryWrapper);

        //响应结果封装
        List<ReportExitStatisListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, ReportExitStatisListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(ReportExitStatisListDto dto) throws ServiceException
    {
     	//构造查询条件
        QueryWrapper<ReportExitStatis> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) reportExitStatisService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportExitStatisGetVo get(Long id) throws ServiceException
    {
    	//查询数据
        ReportExitStatis item = reportExitStatisService.getById(id);

        //响应结果封装
        ReportExitStatisGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportExitStatisGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
     @Override
    public ReportExitStatisGetVo get(ReportExitStatisListDto dto) throws ServiceException
    {
        //构造查询条件
        QueryWrapper<ReportExitStatis> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        ReportExitStatis item = reportExitStatisService.getOne(queryWrapper);

        //响应结果封装
        ReportExitStatisGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportExitStatisGetVo.class);
        }

        //返回查询结果
        return result; 	
    }


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
     @Override
    public ReportExitStatisAddVo add(ReportExitStatisAddDto dto) throws ServiceException
    {
    	 ReportExitStatis entity = BeanUtils.copyProperties(dto, ReportExitStatis.class);

        LocalDateTime nowTime = LocalDateTime.now();                
     	entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setCreateBy(dto.getCreateBy());
        entity.setCreateUser(dto.getCreateUser());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setUpdateBy(dto.getCreateBy());
        entity.setUpdateUser(dto.getCreateUser());
        entity.setUpdateUserName(dto.getCreateUserName());
        entity.setUpdateTime(nowTime);
	    entity.setDelStatus(0);
        reportExitStatisService.save(entity);

        //响应结果封装
        ReportExitStatisAddVo result = new ReportExitStatisAddVo();
        result.setId(entity.getId());

        return result;
    }


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void edit(ReportExitStatisEditDto dto) throws ServiceException
    {
    	ReportExitStatis entity = BeanUtils.copyProperties(dto, ReportExitStatis.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateUser(dto.getUpdateUser());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateTime(nowTime);

        reportExitStatisService.updateById(entity);
    }

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(Long id) throws ServiceException
    {
    	 reportExitStatisService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(ReportExitStatisListDto dto) throws ServiceException
    {
    	//构造查询条件
        QueryWrapper<ReportExitStatis> queryWrapper = queryBuild(dto, false);

        //删除操作
        reportExitStatisService.remove(queryWrapper);
    }

    @Override
    public void saveBatch(List<ReportExitStatisEditDto> listDto) throws ServiceException {
        List<ReportExitStatis> saveList = BeanUtils.copyProperties(listDto, ReportExitStatis.class);
        LocalDateTime nowTime = LocalDateTime.now();
        for (ReportExitStatis entity : saveList) {
            entity.setCreateTime(nowTime);
            entity.setUpdateTime(nowTime);
            entity.setDelStatus(0);
        }
        reportExitStatisService.saveBatch(saveList);
    }

    @Override
    public List<ReportExitVo> exitAreaRateByDay(ReportMultiDataDto dto) {
        return reportExitStatisService.exitAreaRateByDay(dto);
    }

    @Override
    public List<ReportExitVo> exitAreaRateByMonth(ReportMultiDataDto dto) {
        return reportExitStatisService.exitAreaRateByMonth(dto);
    }

    @Override
    public List<ReportExitStatisListVo> getAllData(ReportOperateDayAnalyseDto dto) {
        return reportExitStatisService.getAllData(dto);
    }

    /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportExitStatis> queryBuild(ReportExitStatisListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportExitStatis> queryBuild(ReportExitStatisListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<ReportExitStatis> queryWrapper = new QueryWrapper<>();

        ReportExitStatis entity = BeanUtils.copyProperties(dto, ReportExitStatis.class);
	    entity.setDelStatus(0);

        /** 添加条件样例参考，不用请删除
        if (StringUtils.isNotBlank(dto.getName())) {
            entity.setName(null);
            queryWrapper.like("name", dto.getName());
        }

        queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

        if (orderBy) {
            if (dto.getTypeOrder() != null) {
                queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
            }

            queryWrapper.orderByAsc("order_by");
        }
        */
        List<String> fcodes = dto.getFcodes();
        if(!CollectionUtils.isEmpty(fcodes)){
            queryWrapper.and(qw -> {
                for (String fcode : fcodes) {
                    qw.or(qw1 -> qw1.like("fcode", fcode));
                }
            });
        }
        if (dto.getStartLocalDate() != null&& dto.getEndLocalDate()!= null) {
            queryWrapper.between("exit_date", dto.getStartLocalDate(), dto.getEndLocalDate());
        }
        if (dto.getExitDate() != null) {
            entity.setExitDate(null);
            queryWrapper.between("exit_date", dto.getExitDate(), LocalDate.now());

        }
        //按创建时间倒序排序，根据需要添加
        queryWrapper.orderByAsc("room_shop_id");

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }
}
