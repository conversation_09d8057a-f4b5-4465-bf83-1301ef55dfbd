package com.seewin.som.report.provider.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.som.commerce.provider.CommerceEntexitProvider;
import com.seewin.som.commerce.provider.CommercePatrolWeekDetailProvider;
import com.seewin.som.commerce.req.CommerceEntexitListDto;
import com.seewin.som.commerce.resp.CommerceEntexitListVo;
import com.seewin.som.report.entity.ReportFee;
import com.seewin.som.report.entity.ReportMasterSaleData;
import com.seewin.som.report.entity.ReportStatisFlowData;
import com.seewin.som.report.enums.FlowDataStatisTypeEnum;
import com.seewin.som.report.provider.ReportMasterSaleDataProvider;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import com.seewin.som.report.service.ReportFeeService;
import com.seewin.som.report.service.ReportMasterSaleDataService;
import com.seewin.som.report.service.ReportStatisFlowDataService;
import com.seewin.som.report.util.CalculateUtil;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsListVo;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 主数据库销售数据 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
@DubboService
public class ReportMasterSaleDataProviderImpl implements ReportMasterSaleDataProvider {
    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomsProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommercePatrolWeekDetailProvider commercePatrolWeekDetailProvider;

    @Autowired
    private ReportMasterSaleDataService reportMasterSaleDataService;

    @Autowired
    private ReportStatisFlowDataService reportStatisFlowDataService;

    @Autowired
    private ReportFeeService reportFeeService;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceEntexitProvider commerceEntexitProvider;

    /**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<ReportMasterSaleDataListVo> page(PageQuery<ReportMasterSaleDataListDto> pageQuery) throws ServiceException {
        ReportMasterSaleDataListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<ReportMasterSaleData> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<ReportMasterSaleData> queryWrapper = queryBuild(dto);

        //查询数据
        page = reportMasterSaleDataService.page(page, queryWrapper);
        List<ReportMasterSaleData> records = page.getRecords();

        //响应结果封装
        PageResult<ReportMasterSaleDataListVo> result = new PageResult<>();
        List<ReportMasterSaleDataListVo> items = BeanUtils.copyProperties(records, ReportMasterSaleDataListVo.class);

        result.setItems(items);
        result.setPages((int) page.getPages());
        result.setTotal((int) page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<ReportMasterSaleDataListVo> list(ReportMasterSaleDataListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportMasterSaleData> queryWrapper = queryBuild(dto);


        ReportMasterSaleData entity = BeanUtils.copyProperties(dto, ReportMasterSaleData.class);
        entity.setDelStatus(0);
        if (StringUtils.isNotBlank(dto.getBrandName())) {
            entity.setBrandName(null);
        }
        queryWrapper.setEntity(entity);
        if (dto.getStartDate() != null) {
            queryWrapper.between("sale_time", dto.getStartDate(), dto.getEndDate());
        }
        if (dto.getSearchType() != null && dto.getSearchType() == 0) {
            queryWrapper.orderByDesc("sale_time");
        }
        if (!CollectionUtils.isEmpty(dto.getTenantIdList())) {
            queryWrapper.in("tenant_id", dto.getTenantIdList());
            dto.setTenantId(null);
        }
        //批量查询销售日期
        if (!CollectionUtils.isEmpty(dto.getSaleTimeList())) {
            queryWrapper.in("sale_time", dto.getSaleTimeList());
            dto.setSaleTime(null);
        }
        //查询数据
        List<ReportMasterSaleData> records = reportMasterSaleDataService.list(queryWrapper);

        //响应结果封装
        List<ReportMasterSaleDataListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, ReportMasterSaleDataListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(ReportMasterSaleDataListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportMasterSaleData> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) reportMasterSaleDataService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportMasterSaleDataGetVo get(Long id) throws ServiceException {
        //查询数据
        ReportMasterSaleData item = reportMasterSaleDataService.getById(id);

        //响应结果封装
        ReportMasterSaleDataGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportMasterSaleDataGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportMasterSaleDataGetVo get(ReportMasterSaleDataListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportMasterSaleData> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        ReportMasterSaleData item = reportMasterSaleDataService.getOne(queryWrapper);

        //响应结果封装
        ReportMasterSaleDataGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportMasterSaleDataGetVo.class);
        }

        //返回查询结果
        return result;
    }

    @Override
    public BigDecimal getMonthSaleTotal(String objCode, LocalDate startDate, LocalDate endDate) throws ServiceException {
        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("obj_code", objCode);
        queryWrapper.eq("del_status", 0);
        queryWrapper.between("sale_time", startDate, endDate);
        List<ReportMasterSaleData> list = reportMasterSaleDataService.list(queryWrapper);
        BigDecimal bigDecimal = BigDecimal.ZERO;
        Function<BigDecimal, BigDecimal> function = (a) -> a != null ? a : BigDecimal.ZERO;
        for (ReportMasterSaleData r : list) {
            bigDecimal = bigDecimal.add(function.apply(r.getTotalAmount()));
        }
        return bigDecimal;
    }


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportMasterSaleDataAddVo add(ReportMasterSaleDataAddDto dto) throws ServiceException {
        ReportMasterSaleData entity = BeanUtils.copyProperties(dto, ReportMasterSaleData.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setCreateBy(dto.getCreateBy());
        entity.setCreateUser(dto.getCreateUser());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setUpdateBy(dto.getCreateBy());
        entity.setUpdateUser(dto.getCreateUser());
        entity.setUpdateUserName(dto.getCreateUserName());
        entity.setUpdateTime(nowTime);
        entity.setDelStatus(0);
        reportMasterSaleDataService.save(entity);

        //响应结果封装
        ReportMasterSaleDataAddVo result = new ReportMasterSaleDataAddVo();
        result.setId(entity.getId());

        return result;
    }

    @Override
    public boolean saveBatch(List<ReportMasterSaleDataAddDto> dtos) throws ServiceException {
        List<ReportMasterSaleData> entitys = BeanUtils.copyProperties(dtos, ReportMasterSaleData.class);

        LocalDateTime nowTime = LocalDateTime.now();
        for (ReportMasterSaleData entity : entitys) {
            entity.setId(IdWorker.getId());
            entity.setCreateTime(nowTime);
            entity.setUpdateBy(entity.getCreateBy());
            entity.setUpdateUser(entity.getCreateUser());
            entity.setUpdateUserName(entity.getCreateUserName());
            entity.setUpdateTime(nowTime);
            entity.setDelStatus(0);
        }
        return reportMasterSaleDataService.saveBatch(entitys);
    }


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void edit(ReportMasterSaleDataEditDto dto) throws ServiceException {
        ReportMasterSaleData entity = BeanUtils.copyProperties(dto, ReportMasterSaleData.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateUser(dto.getUpdateUser());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateTime(nowTime);

        reportMasterSaleDataService.updateById(entity);
    }


    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void delete(Long id) throws ServiceException {
        reportMasterSaleDataService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void delete(ReportMasterSaleDataListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportMasterSaleData> queryWrapper = queryBuild(dto, false);

        //删除操作
        reportMasterSaleDataService.remove(queryWrapper);
    }

    /**
     * iot销售数据 按销售日期累加入库
     *
     * @param addDto
     * @throws ServiceException
     */
    @Override
    public void accSaleData(ReportMasterSaleDataAddDto addDto) throws ServiceException {
        reportMasterSaleDataService.accSaleData(addDto);
    }

    @Override
    public Double storePreTaxProfit(ReportMasterSaleDataListDto dto) throws ServiceException {
        Double sum = 0.0;
        RoomsListDto roomsListDto = new RoomsListDto();
        roomsListDto.setTenantId(dto.getTenantId());
        roomsListDto.setFcode(dto.getObjCode());
        List<RoomsListVo> list1 = roomsProvider.list(roomsListDto);
        if (StringUtils.isNotBlank(dto.getObjCode()) && list1.size() > 0) {
            QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("obj_code", dto.getObjCode());
            // 获取当前月的第一天
            LocalDate firstDay = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
            //上月最后一天
            LocalDate lastMontlastDay = firstDay.minusDays(1);
            // 获取上月的第一天
            LocalDate lastMonthfirstDay = firstDay.minusMonths(1);
            queryWrapper.between("sale_time", lastMonthfirstDay, lastMontlastDay);
            queryWrapper.eq("del_status", 0);
            queryWrapper.eq("tenant_id", dto.getTenantId());
            List<ReportMasterSaleData> list = reportMasterSaleDataService.list(queryWrapper);
            for (ReportMasterSaleData reportMasterSaleData : list) {
                sum += reportMasterSaleData.getTotalAmount().doubleValue();
            }
            BigDecimal b = commercePatrolWeekDetailProvider.getLastWeekDataMonthlyCost(dto.getTenantId(), list1.get(0).getId());
            if (b == null) {
                return null;
            }
            sum -= b.doubleValue();
            BigDecimal bd = new BigDecimal(sum);
            sum = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        return sum;
    }

    @Override
    public List<ReportMasterSaleDataGetVo> getLastWeekTotalSales(String shopNo, String startDate, String endDate, Long tenantId) {
        LambdaQueryWrapper<ReportMasterSaleData> wrapper = Wrappers.<ReportMasterSaleData>lambdaQuery();
        wrapper.eq(ReportMasterSaleData::getDelStatus, 0);
        wrapper.eq(ReportMasterSaleData::getTenantId, tenantId);
        wrapper.eq(ReportMasterSaleData::getShopNo, shopNo);
        wrapper.between(ReportMasterSaleData::getSaleTime, startDate, endDate);
        List<ReportMasterSaleData> list = reportMasterSaleDataService.list(wrapper);
        return BeanUtils.copyProperties(list, ReportMasterSaleDataGetVo.class);
    }


    /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportMasterSaleData> queryBuild(ReportMasterSaleDataListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportMasterSaleData> queryBuild(ReportMasterSaleDataListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        ReportMasterSaleData entity = BeanUtils.copyProperties(dto, ReportMasterSaleData.class);
        entity.setDelStatus(0);

        /** 添加条件样例参考，不用请删除
         if (StringUtils.isNotBlank(dto.getName())) {
         entity.setName(null);
         queryWrapper.like("name", dto.getName());
         }

         queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

         if (orderBy) {
         if (dto.getTypeOrder() != null) {
         queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
         }

         queryWrapper.orderByAsc("order_by");
         }
         */

        if (StringUtils.isNotBlank(dto.getBrandName())) {
            entity.setBrandName(null);
            queryWrapper.like("brand_name", dto.getBrandName());
        }

        if (dto.getStartDate() != null) {
            queryWrapper.between("sale_time", dto.getStartDate(), dto.getEndDate());
        }

        if (StringUtils.isNotBlank(dto.getObjCode())) {
            entity.setObjCode(null);
            queryWrapper.likeRight("obj_code", dto.getObjCode());
        }


        if (dto.getSearchType() != null && dto.getSearchType() == 0) {
            queryWrapper.orderByDesc("sale_time");
        } else if (dto.getSearchType() != null && dto.getSearchType() == 1) {
            if (StringUtils.isNotBlank(dto.getOrderField())) {
                queryWrapper.orderBy(true, dto.isAsc(), humpToLine(dto.getOrderField()));
            } else {
                queryWrapper.orderByDesc("sale_time").orderByAsc("shop_no");
            }


        } else {
            //按创建时间倒序排序，根据需要添加
            queryWrapper.orderByDesc("sale_time").orderByAsc("shop_no");
        }

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }

    @Override
    public BigDecimal getTotalAmountSum(ReportMasterSaleDataListDto dto) {
        BigDecimal getVo = reportMasterSaleDataService.getTotalAmountSum(dto);
        if (getVo != null)
            return getVo;
        else
            return new BigDecimal(0.00);
    }

    @Override
    public void editBatch(List<ReportMasterSaleDataEditDto> reportMasterSaleDataEditDtos) throws ServiceException {
        // step 1：抽取出所有id集合
        List<Long> ids = reportMasterSaleDataEditDtos.stream().map(ReportMasterSaleDataEditDto::getId).collect(Collectors.toList());
        // step 2：获取数据库所有对应数据
        Map<Long, ReportMasterSaleData> dbReportMasterSaleDataMap = reportMasterSaleDataService.listByIds(ids).stream().collect(Collectors.toMap(ReportMasterSaleData::getId, e -> e));
        List<ReportMasterSaleData> reportMasterSaleData = BeanUtils.copyProperties(reportMasterSaleDataEditDtos, ReportMasterSaleData.class);
        // step 3：循环判断如果传入值与数据库值不一致，则将数据库值赋值给Before值
        for (ReportMasterSaleData reqReportMasterSaleData : reportMasterSaleData) {
            ReportMasterSaleData dbReportMasterSaleData = dbReportMasterSaleDataMap.get(reqReportMasterSaleData.getId());
            // 避免前端传空所以使用三元运算符判断
            BigDecimal storeAmount = Objects.isNull(reqReportMasterSaleData.getStoreAmount()) ? BigDecimal.valueOf(0) : reqReportMasterSaleData.getStoreAmount();
            BigDecimal takeawayAmount = Objects.isNull(reqReportMasterSaleData.getTakeawayAmount()) ? BigDecimal.valueOf(0) : reqReportMasterSaleData.getTakeawayAmount();
            Integer storeOrder = Objects.isNull(reqReportMasterSaleData.getStoreOrder()) ? Integer.valueOf(0) : reqReportMasterSaleData.getStoreOrder();
            Integer takeawayOrder = Objects.isNull(reqReportMasterSaleData.getTakeawayOrder()) ? Integer.valueOf(0) : reqReportMasterSaleData.getTakeawayOrder();
            reqReportMasterSaleData.setTotalAmount(storeAmount.add(takeawayAmount));
            reqReportMasterSaleData.setTotalOrder(storeOrder + takeawayOrder);

            // 只需要判断总订单数和总金额变了那数据就是变了
            if (reqReportMasterSaleData.getTotalAmount().compareTo(dbReportMasterSaleData.getTotalAmount()) != 0
                    || reqReportMasterSaleData.getTotalOrder().compareTo(dbReportMasterSaleData.getTotalOrder()) != 0) {
                reqReportMasterSaleData.setDataStatus(1);
            }
        }
        reportMasterSaleDataService.updateBatchById(reportMasterSaleData);
    }

    @Override
    public SaleDataAnalyseVo saleAnalyseMonth(MultiDataAnalyseDto dto) {
        LocalDate thisStartDate = dto.getStartLocalDate().minusMonths(1);  // 减一个月，取环比数据
        LocalDate thisEndDate = dto.getEndLocalDate();
        LocalDate lastStartDate = dto.getStartLocalDate().minusYears(1);  // 减一个年，取同比数据
        LocalDate lastEndDate = dto.getEndLocalDate().minusYears(1);  // 减一个年，取同比数据
        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper2 = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", dto.getTenantId());
        queryWrapper2.eq("tenant_id", dto.getTenantId());
        queryWrapper.eq("store_id", dto.getStoreId());
        queryWrapper2.eq("store_id", dto.getStoreId());
        queryWrapper.eq("store_id", dto.getStoreId());
        queryWrapper2.eq("store_id", dto.getStoreId());
        queryWrapper.ge("sale_time", thisStartDate);
        queryWrapper.le("sale_time", thisEndDate);
        queryWrapper2.ge("sale_time", lastStartDate);
        queryWrapper2.le("sale_time", lastEndDate);
        // 今年数据
        List<ReportMasterSaleData> thisSaleDataList = reportMasterSaleDataService.list(queryWrapper);
        // 去年数据，用于同比
        List<ReportMasterSaleData> lastSaleDataList = reportMasterSaleDataService.list(queryWrapper2);
        Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString().substring(0, 7)));
        Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString().substring(0, 7)));
        String dateStr = dto.getStartLocalDate().toString();
        String lastDateStr = dto.getStartLocalDate().minusYears(1).toString();  // 同比日期
        String ringDateStr = dto.getStartLocalDate().minusDays(1).toString();  // 环比日期
        // 本年数据
        List<ReportMasterSaleData> thisSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(dateStr)).orElse(new ArrayList<>());
        // 去年同期数据，用于同比计算
        List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(lastDateStr)).orElse(new ArrayList<>());
        // 环比数据，用于环比计算
        List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(ringDateStr)).orElse(new ArrayList<>());
        double thisTotalAmount;
        double lastTotalAmount;
        double ringTotalAmount;
        if (Objects.isNull(dto.getSearchType()) || dto.getSearchType().equals(0)) {
            thisTotalAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        } else {
            thisTotalAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
        }
        SaleDataAnalyseVo saleDataAnalyseVo = new SaleDataAnalyseVo();
        saleDataAnalyseVo.setDateStr(dateStr);
        saleDataAnalyseVo.setValue(NumberUtil.round(thisTotalAmount, 2).doubleValue());
        saleDataAnalyseVo.setPariPassuValue(NumberUtil.round(lastTotalAmount, 2).doubleValue());
        saleDataAnalyseVo.setRingThanValue(NumberUtil.round(ringTotalAmount, 2).doubleValue());
        // 同比率
        if (lastTotalAmount > 0)
            saleDataAnalyseVo.setPariPassuPercent(NumberUtil.round((thisTotalAmount - lastTotalAmount) * 100 / lastTotalAmount, 2).doubleValue());
        else
            saleDataAnalyseVo.setPariPassuPercent(0D);
        // 环比率
        if (ringTotalAmount > 0)
            saleDataAnalyseVo.setRingThanPercent(NumberUtil.round((thisTotalAmount - ringTotalAmount) * 100 / ringTotalAmount, 2).doubleValue());
        else
            saleDataAnalyseVo.setRingThanPercent(0D);
        return saleDataAnalyseVo;
    }

    @Override
    public List<SaleDataAnalyseVo> saleAnalyse(MultiDataAnalyseDto dto) {

        LocalDate thisStartDate;
        LocalDate thisEndDate;
        if (dto.getTimeType().equals(0)) {
            thisStartDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        } else {
            thisStartDate = dto.getStartLocalDate().minusMonths(1);  // 减一个月，取环比数据
        }
        thisEndDate = dto.getEndLocalDate();

        LocalDate lastStartDate = dto.getStartLocalDate().minusYears(1);  // 减一个年，取同比数据
        LocalDate lastEndDate = dto.getEndLocalDate().minusYears(1);  // 减一个年，取同比数据

        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper2 = new QueryWrapper<>();
        // 项目级
        if (Objects.nonNull(dto.getTenantId())) {
            queryWrapper.eq("tenant_id", dto.getTenantId());
            queryWrapper2.eq("tenant_id", dto.getTenantId());
        }
        // 楼层级
        if (StringUtils.isNotBlank(dto.getObjCode())) {
            queryWrapper.likeRight("obj_code", dto.getObjCode());
            queryWrapper2.likeRight("obj_code", dto.getObjCode());
        }
        // 门店级
        if (StringUtils.isNotBlank(dto.getStoreId())) {
            queryWrapper.eq("store_id", dto.getStoreId());
            queryWrapper2.eq("store_id", dto.getStoreId());
        }
        queryWrapper.ge("sale_time", thisStartDate);
        queryWrapper.le("sale_time", thisEndDate);
        queryWrapper2.ge("sale_time", lastStartDate);
        queryWrapper2.le("sale_time", lastEndDate);
        // 今年数据
        List<ReportMasterSaleData> thisSaleDataList = reportMasterSaleDataService.list(queryWrapper);
        // 去年数据，用于同比
        List<ReportMasterSaleData> lastSaleDataList = reportMasterSaleDataService.list(queryWrapper2);

        List<SaleDataAnalyseVo> saleDataAnalyseVoList = new ArrayList<>();
        LocalDate startDate = dto.getStartLocalDate();
        if (dto.getTimeType().equals(0)) {
            // 按日分组
            Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString()));
            Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString()));
            // for循环处理开始日期到结束日期的数据
            int daysBetween = (int) ChronoUnit.DAYS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            for (int i = 0; i <= daysBetween; i++) {
                SaleDataAnalyseVo saleDataAnalyseVo = new SaleDataAnalyseVo();
                LocalDate tempDate = startDate.plusDays(i);
                String dateStr = tempDate.toString();
                String lastDateStr = tempDate.minusYears(1).toString();  // 同比日期
                String ringDateStr = tempDate.minusDays(1).toString();  // 环比日期

                // 本年数据
                List<ReportMasterSaleData> thisSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(dateStr)).orElse(new ArrayList<>());
                // 去年同期数据，用于同比计算
                List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(lastDateStr)).orElse(new ArrayList<>());
                // 环比数据，用于环比计算
                List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(ringDateStr)).orElse(new ArrayList<>());
                double thisTotalAmount;
                double lastTotalAmount;
                double ringTotalAmount;
                if (Objects.isNull(dto.getSearchType()) || dto.getSearchType().equals(0)) {
                    thisTotalAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                    lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                    ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                } else {
                    thisTotalAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                    lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                    ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                }

                saleDataAnalyseVo.setDateStr(dateStr);
                saleDataAnalyseVo.setValue(NumberUtil.round(thisTotalAmount, 2).doubleValue());
                saleDataAnalyseVo.setPariPassuValue(NumberUtil.round(lastTotalAmount, 2).doubleValue());
                saleDataAnalyseVo.setRingThanValue(NumberUtil.round(ringTotalAmount, 2).doubleValue());
                // 同比率
                if (lastTotalAmount > 0)
                    saleDataAnalyseVo.setPariPassuPercent(NumberUtil.round((thisTotalAmount - lastTotalAmount) * 100 / lastTotalAmount, 2).doubleValue());
                else
                    saleDataAnalyseVo.setPariPassuPercent(0D);
                // 环比率
                if (ringTotalAmount > 0)
                    saleDataAnalyseVo.setRingThanPercent(NumberUtil.round((thisTotalAmount - ringTotalAmount) * 100 / ringTotalAmount, 2).doubleValue());
                else
                    saleDataAnalyseVo.setRingThanPercent(0D);

                saleDataAnalyseVoList.add(saleDataAnalyseVo);
            }
        } else {
            // 按月分组
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString().substring(0, 7)));
            Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString().substring(0, 7)));
            // for循环处理开始月份到结束月份的数据
            int monthsBetween = (int) ChronoUnit.MONTHS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            for (int i = 0; i <= monthsBetween; i++) {
                SaleDataAnalyseVo saleDataAnalyseVo = new SaleDataAnalyseVo();
                LocalDate tempDate = startDate.plusMonths(i);
                String dateStr = tempDate.toString().substring(0, 7);
                String lastDateStr = tempDate.minusYears(1).toString().substring(0, 7);  // 同比日期
                String ringDateStr = tempDate.minusMonths(1).toString().substring(0, 7);  // 环比日期

                // 本年数据
                List<ReportMasterSaleData> thisSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(dateStr)).orElse(new ArrayList<>());
                // 去年同期数据，用于同比计算
                List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(lastDateStr)).orElse(new ArrayList<>());
                // 环比数据，用于环比计算
                List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(ringDateStr)).orElse(new ArrayList<>());
                double thisTotalAmount;
                double lastTotalAmount;
                double ringTotalAmount;
                if (Objects.isNull(dto.getSearchType()) || dto.getSearchType().equals(0)) {
                    thisTotalAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                    lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                    ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                } else {
                    thisTotalAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                    lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                    ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                }

                saleDataAnalyseVo.setDateStr(dateStr);
                saleDataAnalyseVo.setValue(NumberUtil.round(thisTotalAmount, 2).doubleValue());
                saleDataAnalyseVo.setPariPassuValue(NumberUtil.round(lastTotalAmount, 2).doubleValue());
                saleDataAnalyseVo.setRingThanValue(NumberUtil.round(ringTotalAmount, 2).doubleValue());
                // 同比率
                if (lastTotalAmount > 0)
                    saleDataAnalyseVo.setPariPassuPercent(NumberUtil.round((thisTotalAmount - lastTotalAmount) * 100 / lastTotalAmount, 2).doubleValue());
                else
                    saleDataAnalyseVo.setPariPassuPercent(0D);
                // 环比率
                if (ringTotalAmount > 0)
                    saleDataAnalyseVo.setRingThanPercent(NumberUtil.round((thisTotalAmount - ringTotalAmount) * 100 / ringTotalAmount, 2).doubleValue());
                else
                    saleDataAnalyseVo.setRingThanPercent(0D);

                saleDataAnalyseVoList.add(saleDataAnalyseVo);
            }
        }

        // 返回数据
        return saleDataAnalyseVoList;
    }

    @Override
    public List<SalePerOrderAnalyseVo> salePerOrderAnalyse(SalePerOrderAnalyseDto dto) {

        LocalDate thisStartDate;
        LocalDate thisEndDate;
        if (dto.getTimeType().equals(0)) {
            thisStartDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        } else {
            thisStartDate = dto.getStartLocalDate().minusMonths(1);  // 减一个月，取环比数据
        }
        thisEndDate = dto.getEndLocalDate();

        LocalDate lastStartDate = dto.getStartLocalDate().minusYears(1);  // 减一个年，取同比数据
        LocalDate lastEndDate = dto.getEndLocalDate().minusYears(1);  // 减一个年，取同比数据

        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper2 = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper3 = new QueryWrapper<>();
        boolean isSearchStore = false;
        // 项目级
        if (Objects.nonNull(dto.getTenantId())) {
            queryWrapper.eq("tenant_id", dto.getTenantId());
            queryWrapper2.eq("tenant_id", dto.getTenantId());
            queryWrapper3.eq("tenant_id", dto.getTenantId());
        }
        // 楼层级
        if (StringUtils.isNotBlank(dto.getObjCode())) {
            queryWrapper.likeRight("obj_code", dto.getObjCode());
            queryWrapper2.likeRight("obj_code", dto.getObjCode());
        }
        // 门店级
        if (StringUtils.isNotBlank(dto.getStoreId())) {
            isSearchStore = true;
            queryWrapper.eq("store_id", dto.getStoreId());
            queryWrapper2.eq("store_id", dto.getStoreId());
        }
        queryWrapper.ge("sale_time", thisStartDate);
        queryWrapper.le("sale_time", thisEndDate);
        queryWrapper2.ge("sale_time", lastStartDate);
        queryWrapper2.le("sale_time", lastEndDate);
        // 今年数据
        List<ReportMasterSaleData> thisSaleDataList = reportMasterSaleDataService.list(queryWrapper);
        // 去年数据，用于同比
        List<ReportMasterSaleData> lastSaleDataList = reportMasterSaleDataService.list(queryWrapper2);
        // 业态数据
        List<ReportMasterSaleData> commercialDataList = new ArrayList<>();
        if (isSearchStore && !CollectionUtils.isEmpty(thisSaleDataList)) {
            Long commercialTypeCode = thisSaleDataList.get(0).getCommercialTypeCode();
            queryWrapper3.ge("sale_time", thisStartDate);
            queryWrapper3.le("sale_time", thisEndDate);
            queryWrapper3.eq("commercial_type_code", commercialTypeCode);
            commercialDataList = reportMasterSaleDataService.list(queryWrapper3);
        }

        List<SalePerOrderAnalyseVo> salePerOrderAnalyseVoList = new ArrayList<>();
        LocalDate startDate = dto.getStartLocalDate();
        if (dto.getTimeType().equals(0)) {
            // 按日分组
            Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString()));
            Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString()));
            Map<String, List<ReportMasterSaleData>> commercialDataMap = new HashMap<>();
            if (isSearchStore)
                commercialDataMap = commercialDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString()));
            // for循环处理开始日期到结束日期的数据
            int daysBetween = (int) ChronoUnit.DAYS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            for (int i = 0; i <= daysBetween; i++) {
                LocalDate tempDate = startDate.plusDays(i);
                String dateStr = tempDate.toString();
                String lastDateStr = tempDate.minusYears(1).toString();  // 同比日期
                String ringDateStr = tempDate.minusDays(1).toString();  // 环比日期
                // 本年数据
                List<ReportMasterSaleData> thisSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(dateStr)).orElse(new ArrayList<>());
                // 去年同期数据，用于同比计算
                List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(lastDateStr)).orElse(new ArrayList<>());
                // 环比数据，用于环比计算
                List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(ringDateStr)).orElse(new ArrayList<>());
                // 统计数据
                SalePerOrderAnalyseVo salePerOrderAnalyseVo = statisticsSalePerOrderData(thisSaleDataMapList, lastSaleDataMapList, ringSaleDataMapList);
                salePerOrderAnalyseVo.setDateStr(dateStr);
                // 外卖客单价、门店客单价、业态客单价
                if (isSearchStore) {
                    List<ReportMasterSaleData> commercialDataMapLit = Optional.ofNullable(commercialDataMap.get(dateStr)).orElse(new ArrayList<>());
                    statisticsStoreSalePerOrderData(salePerOrderAnalyseVo, thisSaleDataMapList, commercialDataMapLit);
                }
                salePerOrderAnalyseVoList.add(salePerOrderAnalyseVo);
            }
        } else {
            // 按月分组
            Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString().substring(0, 7)));
            Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString().substring(0, 7)));
            Map<String, List<ReportMasterSaleData>> commercialDataMap = new HashMap<>();
            if (isSearchStore)
                commercialDataMap = commercialDataList.stream().collect(Collectors.groupingBy(item -> item.getSaleTime().toString().substring(0, 7)));
            // for循环处理开始月份到结束月份的数据
            int monthsBetween = (int) ChronoUnit.MONTHS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            for (int i = 0; i <= monthsBetween; i++) {
                LocalDate tempDate = startDate.plusMonths(i);
                String dateStr = tempDate.toString().substring(0, 7);
                String lastDateStr = tempDate.minusYears(1).toString().substring(0, 7);  // 同比日期
                String ringDateStr = tempDate.minusMonths(1).toString().substring(0, 7);  // 环比日期
                // 本年数据
                List<ReportMasterSaleData> thisSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(dateStr)).orElse(new ArrayList<>());
                // 去年同期数据，用于同比计算
                List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(lastDateStr)).orElse(new ArrayList<>());
                // 环比数据，用于环比计算
                List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(thisSaleDataMap.get(ringDateStr)).orElse(new ArrayList<>());
                // 统计数据
                SalePerOrderAnalyseVo salePerOrderAnalyseVo = statisticsSalePerOrderData(thisSaleDataMapList, lastSaleDataMapList, ringSaleDataMapList);
                salePerOrderAnalyseVo.setDateStr(dateStr);
                // 外卖客单价、门店客单价、业态客单价
                if (isSearchStore) {
                    List<ReportMasterSaleData> commercialDataMapLit = Optional.ofNullable(commercialDataMap.get(dateStr)).orElse(new ArrayList<>());
                    statisticsStoreSalePerOrderData(salePerOrderAnalyseVo, thisSaleDataMapList, commercialDataMapLit);
                }
                salePerOrderAnalyseVoList.add(salePerOrderAnalyseVo);
            }
        }
        // 返回数据
        return salePerOrderAnalyseVoList;
    }

    private SalePerOrderAnalyseVo statisticsSalePerOrderData(List<ReportMasterSaleData> thisSaleDataMapList, List<ReportMasterSaleData> lastSaleDataMapList, List<ReportMasterSaleData> ringSaleDataMapList) {
        SalePerOrderAnalyseVo salePerOrderAnalyseVo = new SalePerOrderAnalyseVo();

        double thisTotalAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        double lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        double ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        int thisTotalOrder = thisSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
        int lastTotalOrder = lastSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
        int ringTotalOrder = ringSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();

        double salePerOrderValue = CalculateUtil.division(thisTotalAmount, (double) thisTotalOrder);
        double pariPassuValue = CalculateUtil.division(lastTotalAmount, (double) lastTotalOrder);
        double ringThanValue = CalculateUtil.division(ringTotalAmount, (double) ringTotalOrder);
        // 销售总额
        salePerOrderAnalyseVo.setTotalAmount(NumberUtil.round(thisTotalAmount, 2).doubleValue());
        // 销售总笔数
        salePerOrderAnalyseVo.setTotalOrder(thisTotalOrder);
        // 客单价
        salePerOrderAnalyseVo.setSalePerOrderValue(NumberUtil.round(salePerOrderValue, 2).doubleValue());
        // 客单价（同比数据）
        salePerOrderAnalyseVo.setPariPassuValue(NumberUtil.round(pariPassuValue, 2).doubleValue());
        // 客单价（环比数据）
        salePerOrderAnalyseVo.setRingThanValue(NumberUtil.round(ringThanValue, 2).doubleValue());
        // 同比率
        salePerOrderAnalyseVo.setPariPassuPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, pariPassuValue), 2).doubleValue());
        // 环比率
        salePerOrderAnalyseVo.setRingThanPercent(NumberUtil.round(CalculateUtil.ringThanPercent(salePerOrderValue, ringThanValue), 2).doubleValue());

        return salePerOrderAnalyseVo;
    }

    private void statisticsStoreSalePerOrderData(SalePerOrderAnalyseVo salePerOrderAnalyseVo, List<ReportMasterSaleData> thisSaleDataMapList, List<ReportMasterSaleData> commercialDataMapLit) {

        double thisStoreAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getStoreAmount().doubleValue()).sum();
        double thisTakeawayAmount = thisSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
        double commercialTotalAmount = commercialDataMapLit.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        int thisStoreOrder = thisSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getStoreOrder).sum();
        int thisTakeawayOrder = thisSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTakeawayOrder).sum();
        int commercialTotalOrder = commercialDataMapLit.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();

        salePerOrderAnalyseVo.setStorePerOrderValue(NumberUtil.round(CalculateUtil.division(thisStoreAmount, (double) thisStoreOrder), 2).doubleValue());
        salePerOrderAnalyseVo.setTakeawayPerOrderValue(NumberUtil.round(CalculateUtil.division(thisTakeawayAmount, (double) thisTakeawayOrder), 2).doubleValue());
        salePerOrderAnalyseVo.setCommercialPerOrderValue(NumberUtil.round(CalculateUtil.division(commercialTotalAmount, (double) commercialTotalOrder), 2).doubleValue());
    }

    @Override
    public SaleAnalyseTenantDetailVo saleAnalyseTenantDetail(MultiDataAnalyseDto dto) {
        LocalDate ringStartDate;
        LocalDate ringEndDate;
        if (dto.getTimeType().equals(0)) {
            int daysBetween = (int) ChronoUnit.DAYS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusDays(daysBetween + 1);  // 减一天，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        } else {
            int monthsBetween = (int) ChronoUnit.MONTHS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusMonths(monthsBetween + 1);  // 减一个月，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        }
        LocalDate thisStartDate = dto.getStartLocalDate();
        LocalDate thisEndDate = dto.getEndLocalDate();

        LocalDate lastStartDate = dto.getStartLocalDate().minusYears(1);  // 减一个年，取同比数据
        LocalDate lastEndDate = dto.getEndLocalDate().minusYears(1);  // 减一个年，取同比数据

        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper2 = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper3 = new QueryWrapper<>();
        // 项目级
        if (Objects.nonNull(dto.getTenantId())) {
            queryWrapper.eq("tenant_id", dto.getTenantId());
            queryWrapper2.eq("tenant_id", dto.getTenantId());
            queryWrapper3.eq("tenant_id", dto.getTenantId());
        }
        // 楼层级
        if (StringUtils.isNotBlank(dto.getObjCode())) {
            queryWrapper.likeRight("obj_code", dto.getObjCode());
            queryWrapper2.likeRight("obj_code", dto.getObjCode());
            queryWrapper3.likeRight("obj_code", dto.getObjCode());
        }
        // 门店级
        if (StringUtils.isNotBlank(dto.getStoreId())) {
            queryWrapper.eq("store_id", dto.getStoreId());
            queryWrapper2.eq("store_id", dto.getStoreId());
            queryWrapper3.eq("store_id", dto.getStoreId());
        }
        queryWrapper.ge("sale_time", thisStartDate);
        queryWrapper.le("sale_time", thisEndDate);
        queryWrapper2.ge("sale_time", lastStartDate);
        queryWrapper2.le("sale_time", lastEndDate);
        queryWrapper3.ge("sale_time", ringStartDate);
        queryWrapper3.le("sale_time", ringEndDate);
        // 今年数据
        List<ReportMasterSaleData> thisSaleDataList = reportMasterSaleDataService.list(queryWrapper);
        // 去年数据，用于同比
        List<ReportMasterSaleData> lastSaleDataList = reportMasterSaleDataService.list(queryWrapper2);
        // 环比数据
        List<ReportMasterSaleData> ringSaleDataList = reportMasterSaleDataService.list(queryWrapper3);

        // 按楼层分组
        Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> {
            String[] split = item.getUseObj().split("/");
            return split[1] + split[2];
        }));
        Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> {
            String[] split = item.getUseObj().split("/");
            return split[1] + split[2];
        }));
        Map<String, List<ReportMasterSaleData>> ringSaleDataMap = ringSaleDataList.stream().collect(Collectors.groupingBy(item -> {
            String[] split = item.getUseObj().split("/");
            return split[1] + split[2];
        }));

        List<SaleAnalyseTenantDetailItemVo> saleAnalyseTenantDetailItemVoList = new ArrayList<>();
        for (Map.Entry<String, List<ReportMasterSaleData>> thisSaleDataEntry : thisSaleDataMap.entrySet()) {
            SaleAnalyseTenantDetailItemVo itemVo = new SaleAnalyseTenantDetailItemVo();
            // 本年数据
            // 去年同期数据，用于同比计算
            List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());
            // 环比数据，用于环比计算
            List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(ringSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());
            double thisTotalAmount;
            double lastTotalAmount;
            double ringTotalAmount;
            if (Objects.isNull(dto.getSearchType()) || dto.getSearchType().equals(0)) {
                thisTotalAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            } else {
                thisTotalAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            }

            itemVo.setFloorInfo(thisSaleDataEntry.getKey());
            itemVo.setValue(NumberUtil.round(thisTotalAmount, 2).doubleValue());
            itemVo.setPariPassuValue(NumberUtil.round(lastTotalAmount, 2).doubleValue());
            itemVo.setRingThanValue(NumberUtil.round(ringTotalAmount, 2).doubleValue());
            // 同比率
            if (lastTotalAmount > 0)
                itemVo.setPariPassuPercent(NumberUtil.round((thisTotalAmount - lastTotalAmount) * 100 / lastTotalAmount, 2).doubleValue());
            else
                itemVo.setPariPassuPercent(0D);
            // 环比率
            if (ringTotalAmount > 0)
                itemVo.setRingThanPercent(NumberUtil.round((thisTotalAmount - ringTotalAmount) * 100 / ringTotalAmount, 2).doubleValue());
            else
                itemVo.setRingThanPercent(0D);
            // 楼层编码
            String[] objCodeArray = thisSaleDataEntry.getValue().get(0).getObjCode().split("\\.");
            itemVo.setObjCode(objCodeArray[0] + "." + objCodeArray[1]);

            saleAnalyseTenantDetailItemVoList.add(itemVo);
        }

        SaleAnalyseTenantDetailVo saleAnalyseTenantDetailVo = new SaleAnalyseTenantDetailVo();
        saleAnalyseTenantDetailVo.setSaleAnalyseTenantDetailItemVoList(saleAnalyseTenantDetailItemVoList);

        double thisTotalAmount;
        double lastTotalAmount;
        double ringTotalAmount;
        if (Objects.isNull(dto.getSearchType()) || dto.getSearchType().equals(0)) {
            thisTotalAmount = thisSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            lastTotalAmount = lastSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            ringTotalAmount = ringSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        } else {
            thisTotalAmount = thisSaleDataList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            lastTotalAmount = lastSaleDataList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            ringTotalAmount = ringSaleDataList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
        }
        saleAnalyseTenantDetailVo.setTenantSale(NumberUtil.round(thisTotalAmount, 2).doubleValue());
        // 同比率
        if (lastTotalAmount > 0)
            saleAnalyseTenantDetailVo.setPariPassuPercent(NumberUtil.round((thisTotalAmount - lastTotalAmount) * 100 / lastTotalAmount, 2).doubleValue());
        else
            saleAnalyseTenantDetailVo.setPariPassuPercent(0D);
        // 环比率
        if (ringTotalAmount > 0)
            saleAnalyseTenantDetailVo.setRingThanPercent(NumberUtil.round((thisTotalAmount - ringTotalAmount) * 100 / ringTotalAmount, 2).doubleValue());
        else
            saleAnalyseTenantDetailVo.setRingThanPercent(0D);

        return saleAnalyseTenantDetailVo;
    }

    @Override
    public SalePerOrderAnalyseTenantDetailVo salePerOrderAnalyseTenantDetail(MultiDataAnalyseDto dto) {
        LocalDate ringStartDate;
        LocalDate ringEndDate;
        if (dto.getTimeType().equals(0)) {
            int daysBetween = (int) ChronoUnit.DAYS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusDays(daysBetween + 1);  // 减一天，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        } else {
            int monthsBetween = (int) ChronoUnit.MONTHS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusMonths(monthsBetween + 1);  // 减一个月，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        }
        LocalDate thisStartDate = dto.getStartLocalDate();
        LocalDate thisEndDate = dto.getEndLocalDate();

        LocalDate lastStartDate = dto.getStartLocalDate().minusYears(1);  // 减一个年，取同比数据
        LocalDate lastEndDate = dto.getEndLocalDate().minusYears(1);  // 减一个年，取同比数据

        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper2 = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper3 = new QueryWrapper<>();
        // 项目级
        if (Objects.nonNull(dto.getTenantId())) {
            queryWrapper.eq("tenant_id", dto.getTenantId());
            queryWrapper2.eq("tenant_id", dto.getTenantId());
            queryWrapper3.eq("tenant_id", dto.getTenantId());
        }
        // 楼层级
        if (StringUtils.isNotBlank(dto.getObjCode())) {
            queryWrapper.likeRight("obj_code", dto.getObjCode());
            queryWrapper2.likeRight("obj_code", dto.getObjCode());
            queryWrapper3.likeRight("obj_code", dto.getObjCode());
        }
        // 门店级
        if (StringUtils.isNotBlank(dto.getStoreId())) {
            queryWrapper.eq("store_id", dto.getStoreId());
            queryWrapper2.eq("store_id", dto.getStoreId());
            queryWrapper3.eq("store_id", dto.getStoreId());
        }
        queryWrapper.ge("sale_time", thisStartDate);
        queryWrapper.le("sale_time", thisEndDate);
        queryWrapper2.ge("sale_time", lastStartDate);
        queryWrapper2.le("sale_time", lastEndDate);
        queryWrapper3.ge("sale_time", ringStartDate);
        queryWrapper3.le("sale_time", ringEndDate);
        // 今年数据
        List<ReportMasterSaleData> thisSaleDataList = reportMasterSaleDataService.list(queryWrapper);
        // 去年数据，用于同比
        List<ReportMasterSaleData> lastSaleDataList = reportMasterSaleDataService.list(queryWrapper2);
        // 环比数据
        List<ReportMasterSaleData> ringSaleDataList = reportMasterSaleDataService.list(queryWrapper3);

        // 按楼层分组
        Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> {
            String[] split = item.getUseObj().split("/");
            return split[1] + split[2];
        }));
        Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> {
            String[] split = item.getUseObj().split("/");
            return split[1] + split[2];
        }));
        Map<String, List<ReportMasterSaleData>> ringSaleDataMap = ringSaleDataList.stream().collect(Collectors.groupingBy(item -> {
            String[] split = item.getUseObj().split("/");
            return split[1] + split[2];
        }));

        List<SalePerOrderAnalyseTenantDetailItemVo> salePerOrderAnalyseTenantDetailItemVoList = new ArrayList<>();
        for (Map.Entry<String, List<ReportMasterSaleData>> thisSaleDataEntry : thisSaleDataMap.entrySet()) {
            SalePerOrderAnalyseTenantDetailItemVo itemVo = new SalePerOrderAnalyseTenantDetailItemVo();
            // 本年数据
            // 去年同期数据，用于同比计算
            List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());
            // 环比数据，用于环比计算
            List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(ringSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());

            double thisTotalAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            double lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            double ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            int thisTotalOrder = thisSaleDataEntry.getValue().stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
            int lastTotalOrder = lastSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
            int ringTotalOrder = ringSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();

            double salePerOrderValue = CalculateUtil.division(thisTotalAmount, (double) thisTotalOrder);
            double pariPassuValue = CalculateUtil.division(lastTotalAmount, (double) lastTotalOrder);
            double ringThanValue = CalculateUtil.division(ringTotalAmount, (double) ringTotalOrder);

            itemVo.setFloorInfo(thisSaleDataEntry.getKey());
            // 本期销售总额
            itemVo.setTotalAmount(NumberUtil.round(thisTotalAmount, 2).doubleValue());
            // 本期销售总笔数
            itemVo.setTotalOrder(thisTotalOrder);
            // 本期总客单价
            itemVo.setSalePerOrder(NumberUtil.round(salePerOrderValue, 2).doubleValue());
            // 同比销售总额
            itemVo.setPariPassuTotalAmount(NumberUtil.round(lastTotalAmount, 2).doubleValue());
            // 同比销售总笔数
            itemVo.setPariPassuTotalOrder(lastTotalOrder);
            // 同比总客单价
            itemVo.setPariPassuSalePerOrder(NumberUtil.round(pariPassuValue, 2).doubleValue());
            // 环比销售总额
            itemVo.setRingThanTotalAmount(NumberUtil.round(ringTotalAmount, 2).doubleValue());
            // 环比销售总笔数
            itemVo.setRingThanTotalOrder(ringTotalOrder);
            // 环比总客单价
            itemVo.setRingThanSalePerOrder(NumberUtil.round(ringThanValue, 2).doubleValue());
            // 同比率
            itemVo.setPariPassuPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, pariPassuValue), 2).doubleValue());
            // 环比率
            itemVo.setRingThanPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, ringThanValue), 2).doubleValue());
            // 楼层编码
            String[] objCodeArray = thisSaleDataEntry.getValue().get(0).getObjCode().split("\\.");
            itemVo.setObjCode(objCodeArray[0] + "." + objCodeArray[1]);

            salePerOrderAnalyseTenantDetailItemVoList.add(itemVo);
        }

        SalePerOrderAnalyseTenantDetailVo salePerOrderAnalyseTenantDetailVo = new SalePerOrderAnalyseTenantDetailVo();
        salePerOrderAnalyseTenantDetailVo.setSalePerOrderAnalyseTenantDetailItemVoList(salePerOrderAnalyseTenantDetailItemVoList);

        double thisTotalAmount = thisSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        int thisTotalOrder = thisSaleDataList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
        double lastTotalAmount = lastSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        int lastTotalOrder = lastSaleDataList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
        double ringTotalAmount = ringSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        int ringTotalOrder = ringSaleDataList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();

        double salePerOrderValue = CalculateUtil.division(thisTotalAmount, (double) thisTotalOrder);
        double pariPassuValue = CalculateUtil.division(lastTotalAmount, (double) lastTotalOrder);
        double ringThanValue = CalculateUtil.division(ringTotalAmount, (double) ringTotalOrder);

        salePerOrderAnalyseTenantDetailVo.setTenantSalePerOrder(NumberUtil.round(salePerOrderValue, 2).doubleValue());
        // 同比率
        salePerOrderAnalyseTenantDetailVo.setPariPassuPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, pariPassuValue), 2).doubleValue());
        // 环比率
        salePerOrderAnalyseTenantDetailVo.setRingThanPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, ringThanValue), 2).doubleValue());

        return salePerOrderAnalyseTenantDetailVo;
    }

    @Override
    public SaleAnalyseFloorDetailVo saleAnalyseFloorDetail(MultiDataAnalyseDto dto) {
        LocalDate ringStartDate;
        LocalDate ringEndDate;
        if (dto.getTimeType().equals(0)) {
            int daysBetween = (int) ChronoUnit.DAYS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusDays(daysBetween + 1);  // 减一天，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        } else {
            int monthsBetween = (int) ChronoUnit.MONTHS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusMonths(monthsBetween + 1);  // 减一个月，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        }
        LocalDate thisStartDate = dto.getStartLocalDate();
        LocalDate thisEndDate = dto.getEndLocalDate();

        LocalDate lastStartDate = dto.getStartLocalDate().minusYears(1);  // 减一个年，取同比数据
        LocalDate lastEndDate = dto.getEndLocalDate().minusYears(1);  // 减一个年，取同比数据

        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper2 = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper3 = new QueryWrapper<>();
        // 项目级
        if (Objects.nonNull(dto.getTenantId())) {
            queryWrapper.eq("tenant_id", dto.getTenantId());
            queryWrapper2.eq("tenant_id", dto.getTenantId());
            queryWrapper3.eq("tenant_id", dto.getTenantId());
        }
        // 楼层级
        if (StringUtils.isNotBlank(dto.getObjCode())) {
            queryWrapper.likeRight("obj_code", dto.getObjCode());
            queryWrapper2.likeRight("obj_code", dto.getObjCode());
            queryWrapper3.likeRight("obj_code", dto.getObjCode());
        }
        // 门店级
        if (StringUtils.isNotBlank(dto.getStoreId())) {
            queryWrapper.eq("store_id", dto.getStoreId());
            queryWrapper2.eq("store_id", dto.getStoreId());
            queryWrapper3.eq("store_id", dto.getStoreId());
        }
        if (!CollectionUtils.isEmpty(dto.getStoreIdList())) {
            queryWrapper.in("store_id", dto.getStoreIdList());
            queryWrapper2.in("store_id", dto.getStoreIdList());
            queryWrapper3.in("store_id", dto.getStoreIdList());
        }
        queryWrapper.ge("sale_time", thisStartDate);
        queryWrapper.le("sale_time", thisEndDate);
        queryWrapper2.ge("sale_time", lastStartDate);
        queryWrapper2.le("sale_time", lastEndDate);
        queryWrapper3.ge("sale_time", ringStartDate);
        queryWrapper3.le("sale_time", ringEndDate);
        // 今年数据
        List<ReportMasterSaleData> thisSaleDataList = reportMasterSaleDataService.list(queryWrapper);
        // 去年数据，用于同比
        List<ReportMasterSaleData> lastSaleDataList = reportMasterSaleDataService.list(queryWrapper2);
        // 环比数据
        List<ReportMasterSaleData> ringSaleDataList = reportMasterSaleDataService.list(queryWrapper3);

        // 按楼层编码和品牌名分组
        Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));
        Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));
        Map<String, List<ReportMasterSaleData>> ringSaleDataMap = ringSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));

        List<SaleAnalyseFloorDetailItemVo> saleAnalyseFloorDetailItemVoList = new ArrayList<>();
        for (Map.Entry<String, List<ReportMasterSaleData>> thisSaleDataEntry : thisSaleDataMap.entrySet()) {
            SaleAnalyseFloorDetailItemVo itemVo = new SaleAnalyseFloorDetailItemVo();
            // 本年数据
            // 去年同期数据，用于同比计算
            List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());
            // 环比数据，用于环比计算
            List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(ringSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());
            double thisTotalAmount;
            double lastTotalAmount;
            double ringTotalAmount;
            if (Objects.isNull(dto.getSearchType()) || dto.getSearchType().equals(0)) {
                thisTotalAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
                ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            } else {
                thisTotalAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
                ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            }

            ReportMasterSaleData reportMasterSaleData = thisSaleDataEntry.getValue().get(0);
            itemVo.setStoreId(reportMasterSaleData.getStoreId());
            itemVo.setBrandName(reportMasterSaleData.getBrandName());
            itemVo.setValue(NumberUtil.round(thisTotalAmount, 2).doubleValue());
            itemVo.setPariPassuValue(NumberUtil.round(lastTotalAmount, 2).doubleValue());
            itemVo.setRingThanValue(NumberUtil.round(ringTotalAmount, 2).doubleValue());
            // 同比率
            if (lastTotalAmount > 0)
                itemVo.setPariPassuPercent(NumberUtil.round((thisTotalAmount - lastTotalAmount) * 100 / lastTotalAmount, 2).doubleValue());
            else
                itemVo.setPariPassuPercent(0D);
            // 环比率
            if (ringTotalAmount > 0)
                itemVo.setRingThanPercent(NumberUtil.round((thisTotalAmount - ringTotalAmount) * 100 / ringTotalAmount, 2).doubleValue());
            else
                itemVo.setRingThanPercent(0D);
            // 门店位置
            String[] useObjArray = reportMasterSaleData.getUseObj().split("/");
            itemVo.setStoreLocation(useObjArray[1] + useObjArray[2]);

            saleAnalyseFloorDetailItemVoList.add(itemVo);
        }

        SaleAnalyseFloorDetailVo saleAnalyseFloorDetailVo = new SaleAnalyseFloorDetailVo();
        saleAnalyseFloorDetailVo.setSaleAnalyseFloorDetailItemVoList(saleAnalyseFloorDetailItemVoList);

        double thisTotalAmount;
        double lastTotalAmount;
        double ringTotalAmount;
        if (Objects.isNull(dto.getSearchType()) || dto.getSearchType().equals(0)) {
            thisTotalAmount = thisSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            lastTotalAmount = lastSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            ringTotalAmount = ringSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        } else {
            thisTotalAmount = thisSaleDataList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            lastTotalAmount = lastSaleDataList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            ringTotalAmount = ringSaleDataList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
        }
        saleAnalyseFloorDetailVo.setFloorSale(NumberUtil.round(thisTotalAmount, 2).doubleValue());
        // 同比率
        if (lastTotalAmount > 0)
            saleAnalyseFloorDetailVo.setPariPassuPercent(NumberUtil.round((thisTotalAmount - lastTotalAmount) * 100 / lastTotalAmount, 2).doubleValue());
        else
            saleAnalyseFloorDetailVo.setPariPassuPercent(0D);
        // 环比率
        if (ringTotalAmount > 0)
            saleAnalyseFloorDetailVo.setRingThanPercent(NumberUtil.round((thisTotalAmount - ringTotalAmount) * 100 / ringTotalAmount, 2).doubleValue());
        else
            saleAnalyseFloorDetailVo.setRingThanPercent(0D);

        return saleAnalyseFloorDetailVo;
    }

    @Override
    public SalePerOrderAnalyseFloorDetailVo salePerOrderAnalyseFloorDetail(MultiDataAnalyseDto dto) {
        LocalDate ringStartDate;
        LocalDate ringEndDate;
        if (dto.getTimeType().equals(0)) {
            int daysBetween = (int) ChronoUnit.DAYS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusDays(daysBetween + 1);  // 减一天，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        } else {
            int monthsBetween = (int) ChronoUnit.MONTHS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusMonths(monthsBetween + 1);  // 减一个月，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        }
        LocalDate thisStartDate = dto.getStartLocalDate();
        LocalDate thisEndDate = dto.getEndLocalDate();

        LocalDate lastStartDate = dto.getStartLocalDate().minusYears(1);  // 减一个年，取同比数据
        LocalDate lastEndDate = dto.getEndLocalDate().minusYears(1);  // 减一个年，取同比数据

        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper2 = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper3 = new QueryWrapper<>();
        // 项目级
        if (Objects.nonNull(dto.getTenantId())) {
            queryWrapper.eq("tenant_id", dto.getTenantId());
            queryWrapper2.eq("tenant_id", dto.getTenantId());
            queryWrapper3.eq("tenant_id", dto.getTenantId());
        }
        // 楼层级
        if (StringUtils.isNotBlank(dto.getObjCode())) {
            queryWrapper.likeRight("obj_code", dto.getObjCode());
            queryWrapper2.likeRight("obj_code", dto.getObjCode());
            queryWrapper3.likeRight("obj_code", dto.getObjCode());
        }
        // 门店级
        if (StringUtils.isNotBlank(dto.getStoreId())) {
            queryWrapper.eq("store_id", dto.getStoreId());
            queryWrapper2.eq("store_id", dto.getStoreId());
            queryWrapper3.eq("store_id", dto.getStoreId());
        }
        if (!CollectionUtils.isEmpty(dto.getStoreIdList())) {
            queryWrapper.in("store_id", dto.getStoreIdList());
            queryWrapper2.in("store_id", dto.getStoreIdList());
            queryWrapper3.in("store_id", dto.getStoreIdList());
        }
        queryWrapper.ge("sale_time", thisStartDate);
        queryWrapper.le("sale_time", thisEndDate);
        queryWrapper2.ge("sale_time", lastStartDate);
        queryWrapper2.le("sale_time", lastEndDate);
        queryWrapper3.ge("sale_time", ringStartDate);
        queryWrapper3.le("sale_time", ringEndDate);
        // 今年数据
        List<ReportMasterSaleData> thisSaleDataList = reportMasterSaleDataService.list(queryWrapper);
        // 去年数据，用于同比
        List<ReportMasterSaleData> lastSaleDataList = reportMasterSaleDataService.list(queryWrapper2);
        // 环比数据
        List<ReportMasterSaleData> ringSaleDataList = reportMasterSaleDataService.list(queryWrapper3);

        // 按楼层编码和品牌名分组
        Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));
        Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));
        Map<String, List<ReportMasterSaleData>> ringSaleDataMap = ringSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));

        List<SalePerOrderAnalyseFloorDetailItemVo> salePerOrderAnalyseFloorDetailItemVoList = new ArrayList<>();
        for (Map.Entry<String, List<ReportMasterSaleData>> thisSaleDataEntry : thisSaleDataMap.entrySet()) {
            SalePerOrderAnalyseFloorDetailItemVo itemVo = new SalePerOrderAnalyseFloorDetailItemVo();
            // 本年数据
            // 去年同期数据，用于同比计算
            List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());
            // 环比数据，用于环比计算
            List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(ringSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());

            double thisTotalAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            double lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            double ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            int thisTotalOrder = thisSaleDataEntry.getValue().stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
            int lastTotalOrder = lastSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
            int ringTotalOrder = ringSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();

            double salePerOrderValue = CalculateUtil.division(thisTotalAmount, (double) thisTotalOrder);
            double pariPassuValue = CalculateUtil.division(lastTotalAmount, (double) lastTotalOrder);
            double ringThanValue = CalculateUtil.division(ringTotalAmount, (double) ringTotalOrder);

            double thisTakeawayAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            double lastTakeawayAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            double ringTakeawayAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTakeawayAmount().doubleValue()).sum();
            int thisTakeawayOrder = thisSaleDataEntry.getValue().stream().mapToInt(ReportMasterSaleData::getTakeawayOrder).sum();
            int lastTakeawayOrder = lastSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTakeawayOrder).sum();
            int ringTakeawayOrder = ringSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTakeawayOrder).sum();

            double thisStoreAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getStoreAmount().doubleValue()).sum();
            double lastStoreAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getStoreAmount().doubleValue()).sum();
            double ringStoreAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getStoreAmount().doubleValue()).sum();
            int thisStoreOrder = thisSaleDataEntry.getValue().stream().mapToInt(ReportMasterSaleData::getStoreOrder).sum();
            int lastStoreOrder = lastSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getStoreOrder).sum();
            int ringStoreOrder = ringSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getStoreOrder).sum();

            ReportMasterSaleData reportMasterSaleData = thisSaleDataEntry.getValue().get(0);
            itemVo.setStoreId(reportMasterSaleData.getStoreId());
            itemVo.setBrandName(reportMasterSaleData.getBrandName());
            // 本期销售总额
            itemVo.setTotalAmount(NumberUtil.round(thisTotalAmount, 2).doubleValue());
            // 本期销售总笔数
            itemVo.setTotalOrder(thisTotalOrder);
            // 本期外卖客单价
            itemVo.setTakeawaySalePerOrder(NumberUtil.round(CalculateUtil.division(thisTakeawayAmount, (double) thisTakeawayOrder), 2).doubleValue());
            // 本期门店客单价
            itemVo.setStoreSalePerOrder(NumberUtil.round(CalculateUtil.division(thisStoreAmount, (double) thisStoreOrder), 2).doubleValue());
            // 本期总客单价
            itemVo.setSalePerOrder(NumberUtil.round(salePerOrderValue, 2).doubleValue());
            // 同比销售总额
            itemVo.setPariPassuTotalAmount(NumberUtil.round(lastTotalAmount, 2).doubleValue());
            // 同比销售总笔数
            itemVo.setPariPassuTotalOrder(lastTotalOrder);
            // 同比外卖客单价
            itemVo.setPariPassuTakeawaySalePerOrder(NumberUtil.round(CalculateUtil.division(lastTakeawayAmount, (double) lastTakeawayOrder), 2).doubleValue());
            // 同比门店客单价
            itemVo.setPariPassuStoreSalePerOrder(NumberUtil.round(CalculateUtil.division(lastStoreAmount, (double) lastStoreOrder), 2).doubleValue());
            // 同比总客单价
            itemVo.setPariPassuSalePerOrder(NumberUtil.round(pariPassuValue, 2).doubleValue());
            // 环比销售总额
            itemVo.setRingThanTotalAmount(NumberUtil.round(ringTotalAmount, 2).doubleValue());
            // 环比销售总笔数
            itemVo.setRingThanTotalOrder(ringTotalOrder);
            // 环比外卖客单价
            itemVo.setRingThanTakeawaySalePerOrder(NumberUtil.round(CalculateUtil.division(ringTakeawayAmount, (double) ringTakeawayOrder), 2).doubleValue());
            // 环比门店客单价
            itemVo.setRingThanStoreSalePerOrder(NumberUtil.round(CalculateUtil.division(ringStoreAmount, (double) ringStoreOrder), 2).doubleValue());
            // 环比总客单价
            itemVo.setRingThanSalePerOrder(NumberUtil.round(ringThanValue, 2).doubleValue());
            // 同比率
            itemVo.setPariPassuPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, pariPassuValue), 2).doubleValue());
            // 环比率
            itemVo.setRingThanPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, ringThanValue), 2).doubleValue());

            salePerOrderAnalyseFloorDetailItemVoList.add(itemVo);
        }

        SalePerOrderAnalyseFloorDetailVo salePerOrderAnalyseFloorDetailVo = new SalePerOrderAnalyseFloorDetailVo();
        salePerOrderAnalyseFloorDetailVo.setSalePerOrderAnalyseFloorDetailItemVoList(salePerOrderAnalyseFloorDetailItemVoList);

        double thisTotalAmount = thisSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        int thisTotalOrder = thisSaleDataList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
        double lastTotalAmount = lastSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        int lastTotalOrder = lastSaleDataList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
        double ringTotalAmount = ringSaleDataList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
        int ringTotalOrder = ringSaleDataList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();

        double salePerOrderValue = CalculateUtil.division(thisTotalAmount, (double) thisTotalOrder);
        double pariPassuValue = CalculateUtil.division(lastTotalAmount, (double) lastTotalOrder);
        double ringThanValue = CalculateUtil.division(ringTotalAmount, (double) ringTotalOrder);

        salePerOrderAnalyseFloorDetailVo.setFloorSalePerOrder(NumberUtil.round(salePerOrderValue, 2).doubleValue());
        // 同比率
        salePerOrderAnalyseFloorDetailVo.setPariPassuPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, pariPassuValue), 2).doubleValue());
        // 环比率
        salePerOrderAnalyseFloorDetailVo.setRingThanPercent(NumberUtil.round(CalculateUtil.ringThanPercent(salePerOrderValue, ringThanValue), 2).doubleValue());

        return salePerOrderAnalyseFloorDetailVo;
    }

    @Override
    public List<SalePerOrderAnalyseContrast> salePerOrderAnalyseContrast(MultiDataAnalyseDto dto) {
        LocalDate ringStartDate;
        LocalDate ringEndDate;
        if (dto.getTimeType().equals(0)) {
            int daysBetween = (int) ChronoUnit.DAYS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusDays(daysBetween + 1);  // 减一天，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        } else {
            int monthsBetween = (int) ChronoUnit.MONTHS.between(dto.getStartLocalDate(), dto.getEndLocalDate());
            ringStartDate = dto.getStartLocalDate().minusMonths(monthsBetween + 1);  // 减一个月，取环比数据
            ringEndDate = dto.getStartLocalDate().minusDays(1);  // 减一天，取环比数据
        }
        LocalDate thisStartDate = dto.getStartLocalDate();
        LocalDate thisEndDate = dto.getEndLocalDate();

        LocalDate lastStartDate = dto.getStartLocalDate().minusYears(1);  // 减一个年，取同比数据
        LocalDate lastEndDate = dto.getEndLocalDate().minusYears(1);  // 减一个年，取同比数据

        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper2 = new QueryWrapper<>();
        QueryWrapper<ReportMasterSaleData> queryWrapper3 = new QueryWrapper<>();
        // 项目级
        if (Objects.nonNull(dto.getTenantId())) {
            queryWrapper.eq("tenant_id", dto.getTenantId());
            queryWrapper2.eq("tenant_id", dto.getTenantId());
            queryWrapper3.eq("tenant_id", dto.getTenantId());
        }
        // 楼层级
        if (StringUtils.isNotBlank(dto.getObjCode())) {
            queryWrapper.likeRight("obj_code", dto.getObjCode());
            queryWrapper2.likeRight("obj_code", dto.getObjCode());
            queryWrapper3.likeRight("obj_code", dto.getObjCode());
        }
        // 门店级
        if (StringUtils.isNotBlank(dto.getStoreId())) {
            queryWrapper.eq("store_id", dto.getStoreId());
            queryWrapper2.eq("store_id", dto.getStoreId());
            queryWrapper3.eq("store_id", dto.getStoreId());
        }
        if (!CollectionUtils.isEmpty(dto.getStoreIdList())) {
            queryWrapper.in("store_id", dto.getStoreIdList());
            queryWrapper2.in("store_id", dto.getStoreIdList());
            queryWrapper3.in("store_id", dto.getStoreIdList());
        }
        queryWrapper.ge("sale_time", thisStartDate);
        queryWrapper.le("sale_time", thisEndDate);
        queryWrapper2.ge("sale_time", lastStartDate);
        queryWrapper2.le("sale_time", lastEndDate);
        queryWrapper3.ge("sale_time", ringStartDate);
        queryWrapper3.le("sale_time", ringEndDate);
        // 今年数据
        List<ReportMasterSaleData> thisSaleDataList = reportMasterSaleDataService.list(queryWrapper);
        // 去年数据，用于同比
        List<ReportMasterSaleData> lastSaleDataList = reportMasterSaleDataService.list(queryWrapper2);
        // 环比数据
        List<ReportMasterSaleData> ringSaleDataList = reportMasterSaleDataService.list(queryWrapper3);

        // 按楼层编码和品牌名分组
        Map<String, List<ReportMasterSaleData>> thisSaleDataMap = thisSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));
        Map<String, List<ReportMasterSaleData>> lastSaleDataMap = lastSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));
        Map<String, List<ReportMasterSaleData>> ringSaleDataMap = ringSaleDataList.stream().collect(Collectors.groupingBy(item -> item.getObjCode() + item.getBrandName()));

        List<SalePerOrderAnalyseContrast> salePerOrderAnalyseContrastList = new ArrayList<>();
        for (Map.Entry<String, List<ReportMasterSaleData>> thisSaleDataEntry : thisSaleDataMap.entrySet()) {
            SalePerOrderAnalyseContrast itemVo = new SalePerOrderAnalyseContrast();
            // 本年数据
            // 去年同期数据，用于同比计算
            List<ReportMasterSaleData> lastSaleDataMapList = Optional.ofNullable(lastSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());
            // 环比数据，用于环比计算
            List<ReportMasterSaleData> ringSaleDataMapList = Optional.ofNullable(ringSaleDataMap.get(thisSaleDataEntry.getKey())).orElse(new ArrayList<>());

            double thisTotalAmount = thisSaleDataEntry.getValue().stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            double lastTotalAmount = lastSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            double ringTotalAmount = ringSaleDataMapList.stream().mapToDouble(item -> item.getTotalAmount().doubleValue()).sum();
            int thisTotalOrder = thisSaleDataEntry.getValue().stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
            int lastTotalOrder = lastSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();
            int ringTotalOrder = ringSaleDataMapList.stream().mapToInt(ReportMasterSaleData::getTotalOrder).sum();

            double salePerOrderValue = CalculateUtil.division(thisTotalAmount, (double) thisTotalOrder);
            double pariPassuValue = CalculateUtil.division(lastTotalAmount, (double) lastTotalOrder);
            double ringThanValue = CalculateUtil.division(ringTotalAmount, (double) ringTotalOrder);

            ReportMasterSaleData reportMasterSaleData = thisSaleDataEntry.getValue().get(0);
            itemVo.setStoreId(reportMasterSaleData.getStoreId());
            itemVo.setBrandName(reportMasterSaleData.getBrandName());
            itemVo.setCommercialTypeName(reportMasterSaleData.getCommercialTypeName());
            itemVo.setCategoryName(reportMasterSaleData.getCategoryName());
            // 门店位置
            String[] useObjArray = reportMasterSaleData.getUseObj().split("/");
            itemVo.setStoreLocation(useObjArray[1] + useObjArray[2]);

            // 本期总客单价
            itemVo.setSalePerOrder(NumberUtil.round(salePerOrderValue, 2).doubleValue());
            // 同比总客单价
            itemVo.setPariPassuSalePerOrder(NumberUtil.round(pariPassuValue, 2).doubleValue());
            // 环比总客单价
            itemVo.setRingThanSalePerOrder(NumberUtil.round(ringThanValue, 2).doubleValue());
            // 同比率
            itemVo.setPariPassuPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, pariPassuValue), 2).doubleValue());
            // 环比率
            itemVo.setRingThanPercent(NumberUtil.round(CalculateUtil.pariPassuPercent(salePerOrderValue, ringThanValue), 2).doubleValue());

            salePerOrderAnalyseContrastList.add(itemVo);
        }

        return salePerOrderAnalyseContrastList;
    }

    @Override
    public Map<String, BigDecimal> statisticStoreSalesData(MasterDataStatReq req) throws ServiceException {
        Map<String, BigDecimal> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(req.getStoreIds())) {
            for (String storeId : req.getStoreIds()) {
                result.put(storeId, BigDecimal.ZERO);
            }
            List<ReportMasterSaleData> masterSaleData = reportMasterSaleDataService.statisticData(req);
            masterSaleData.forEach(e -> {
                result.put(e.getStoreId(), e.getTotalAmount());
            });
        }
        return result;
    }


    /**
     * 获取招调
     * 项目周销售额
     * 项目总客流
     *
     * @return
     */
    @Override
    public ProjectInformationVo getProjectInformation(Long tenantId, String roomNo, String fname) {
        ProjectInformationVo result = new ProjectInformationVo();
        LocalDate currentNow = LocalDate.now();
        LocalDate threeMonthsAgo = currentNow.minus(Period.ofDays(90));
        //开始时间
        String endDate = currentNow.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00";
        String startDate = threeMonthsAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00";
        Map<String, BigDecimal> saleRefMap = getProjectSaleRef(threeMonthsAgo, tenantId, startDate, endDate, roomNo);
        BigDecimal projectSaleRef = saleRefMap.get("projectSaleRef");
        //销售笔数
        BigDecimal totalStoreOrder = saleRefMap.get("totalStoreOrder").multiply(new BigDecimal(7)).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal projectFlowRef = getProjectFlowRef(threeMonthsAgo, startDate, endDate, tenantId, fname);
        BigDecimal projectRentRef = getProjectRentRef(currentNow, tenantId, roomNo);
        //项目销售总额
        result.setProjectSaleRef(projectSaleRef.multiply(new BigDecimal(7)).setScale(2, BigDecimal.ROUND_HALF_UP));
        //项目总客流
        result.setProjectFlowRef(projectFlowRef.multiply(new BigDecimal(7)).setScale(0, BigDecimal.ROUND_HALF_UP).longValue());
        //项目租金总额
        result.setProjectRentRef(projectRentRef.multiply(new BigDecimal(7)).setScale(2, BigDecimal.ROUND_HALF_UP));
        //周项目租售比计算
        Double projectPercentRef = 0D;
        Double transferRate = 0D;
        if (ObjectUtils.isNotEmpty(result.getProjectSaleRef()) && ObjectUtils.isNotEmpty(result.getProjectRentRef())
                && BigDecimal.ZERO.compareTo(result.getProjectSaleRef()) != 0 && BigDecimal.ZERO.compareTo(result.getProjectRentRef()) != 0) {
            projectPercentRef = result.getProjectRentRef().divide(result.getProjectSaleRef(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        if (ObjectUtils.isNotEmpty(totalStoreOrder) && ObjectUtils.isNotEmpty(result.getProjectFlowRef())
                && BigDecimal.ZERO.compareTo(totalStoreOrder) != 0
                && BigDecimal.ZERO.compareTo(new BigDecimal(result.getProjectFlowRef())) != 0) {
            transferRate = totalStoreOrder.divide(new BigDecimal(result.getProjectFlowRef()), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        result.setTransferRate(transferRate);
        result.setProjectPercentRef(projectPercentRef);
        return result;
    }

    @Override
    public List<ReportCategoryCompareVo> categorySaleAmountCompare(ReportCategoryCompareDto dto) {
        return reportMasterSaleDataService.categorySaleAmountCompare(dto);
    }

    @Override
    public List<ReportCategoryCompareVo> categoryGuestAvgPriceCompare(ReportCategoryCompareDto dto) {
        return reportMasterSaleDataService.categoryGuestAvgPriceCompare(dto);
    }

    @Override
    public List<ReportMultiDataVo> saleConvertRate(ReportMultiDataDto multiDataDto) {
        return reportMasterSaleDataService.saleConvertRate(multiDataDto);
    }

    @Override
    public List<SaleDataMonthTotalAmountVo> selectMonthTotalAmount(String saleMonth) {
        return reportMasterSaleDataService.selectMonthTotalAmount(saleMonth);
    }

    @Override
    public List<ReportMultiDataDetailVo> saleConvertStoreContrast(ReportMultiDataDto multiDataDto) {
        return reportMasterSaleDataService.saleConvertStoreContrast(multiDataDto);
    }

    @Override
    public BigDecimal getMonthSaleTotalTenant(Long tenantId, String storeId, LocalDate startDate, LocalDate endDate) {
        QueryWrapper<ReportMasterSaleData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.eq("store_id", storeId);
        queryWrapper.eq("del_status", 0);
        queryWrapper.between("sale_time", startDate, endDate);
        List<ReportMasterSaleData> list = reportMasterSaleDataService.list(queryWrapper);
        BigDecimal bigDecimal = BigDecimal.ZERO;
        Function<BigDecimal, BigDecimal> function = (a) -> a != null ? a : BigDecimal.ZERO;
        for (ReportMasterSaleData r : list) {
            bigDecimal = bigDecimal.add(function.apply(r.getTotalAmount()));
        }
        return bigDecimal;
    }

    @Override
    public List<ReportMultiDataVo> saleSquare(ReportMultiDataDto multiDataDto) {
        return reportMasterSaleDataService.saleSquare(multiDataDto);
    }

    @Override
    public Set<String> selectStoreIdSaleTime(long tenantId, List<String> objCodes) {
        return reportMasterSaleDataService.selectStoreIdSaleTime(tenantId, objCodes);
    }

    @Override
    public ReportMultiDataProjectVo getExportData(ReportMultiDataDto curMultiDataDto) {
        return reportMasterSaleDataService.getExportData(curMultiDataDto);
    }

    @Override
    public List<ReportMultiDataStoreVo> getStoreSaleData(ReportMultiDataDto curMultiDataDto) {
        return reportMasterSaleDataService.getStoreSaleData(curMultiDataDto);
    }

    @Override
    public BigDecimal getSaleTotalById(Long tenantId, String storeId, LocalDate periodStart, LocalDate periodEnd) {
        return reportMasterSaleDataService.getSaleTotalById(tenantId, storeId, periodStart, periodEnd);
    }

    @Override
    public List<SaleDataCategoryAnalyseVo> categoryAnalyseStatistic(CategoryAnalyseDto analyseDtoBasic) {
        return reportMasterSaleDataService.categoryAnalyseStatistic(analyseDtoBasic);
    }

    @Override
    public List<String> saleRank(CategoryAnalyseDto dto) {
        return reportMasterSaleDataService.saleRank(dto);
    }

    @Override
    public List<SaleDataCategoryAnalyseVo> categoryAnalyseStatisticDetail(CategoryAnalyseDto dto) {
        return reportMasterSaleDataService.categoryAnalyseStatisticDetail(dto);
    }

    @Override
    public BigDecimal getSaleTotalAmount(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataService.getSaleTotalAmount(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> getSaleDistributeList(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataService.getSaleDistributeList(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> getSaleAmountByDate(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataService.getSaleAmountByDate(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> getSaleAnalyseList(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataService.getSaleAnalyseList(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> getSaleDetailByGatherType(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataService.getSaleDetailByGatherType(dto);
    }

    @Override
    public List<ReportSaleAndFlowListVo> getOperationReport(OperationReportListDto dto) throws ServiceException {
        return reportMasterSaleDataService.getOperationReport(dto);
    }

    @Override
    public List<SaleDataAnalyseVo> saleAmount(MultiDataAnalyseDto dto) {
        return reportMasterSaleDataService.saleAmount(dto);
    }

    @Override
    public List<SaleDataAnalyseVo> salePredictAmount(MultiDataAnalyseDto dto) {
        return reportMasterSaleDataService.salePredictAmount(dto);
    }

    @Override
    public List<SaleDataAnalyseVo> salePredictStoreAmount(MultiDataAnalyseDto dto) {
        return reportMasterSaleDataService.salePredictStoreAmount(dto);
    }

    @Override
    public PredictDataVo getPredictData(PredictDataDto dto) {
        return reportMasterSaleDataService.getPredictData(dto);
    }

    @Override
    public PredictDataVo getPredictStoreData(PredictDataDto dto) {
        return reportMasterSaleDataService.getPredictStoreData(dto);
    }

    @Override
    public List<ReportSaleRecommendVo> getRecommendSaleTotal(ReportMasterSaleDataListDto saleListDto) {
        return reportMasterSaleDataService.getRecommendSaleTotal(saleListDto);
    }

    @Override
    public List<ManageAnalyseVo> getFlowAmountFeeByProject(ReportManageMultiAnalyseListDto dto) {
        return reportMasterSaleDataService.getFlowAmountFeeByProject(dto);
    }

    @Override
    public Long getFormatIdByStoreId(ReportMultiDataDto multiDataDto) {
        Long formatId = null;
        CommerceEntexitListDto dto = new CommerceEntexitListDto();
        dto.setTenantId(multiDataDto.getTenantId());
        dto.setRoomId(multiDataDto.getStoreId());
        List<CommerceEntexitListVo> entexitListVos = commerceEntexitProvider.list(dto);
        if (!CollectionUtils.isEmpty(entexitListVos)) {
            formatId = Long.valueOf(entexitListVos.get(0).getCommercialTypeCode());
        }
        return formatId;
    }

    /**
     * 租金总额
     *
     * @param currentNow
     * @param tenantId
     * @param roomNo
     * @return
     */
    private BigDecimal getProjectRentRef(LocalDate currentNow, Long tenantId, String roomNo) {
        BigDecimal projectRentRef = new BigDecimal(0);
        List<ReportFee> reportFeeList = new ArrayList<>();
        LambdaQueryWrapper<ReportFee> firstDayLambdaQueryWrapper = new LambdaQueryWrapper<>();
        firstDayLambdaQueryWrapper.eq(ReportFee::getTenantId, tenantId).orderByAsc(ReportFee::getMonth)
                .orderByAsc(ReportFee::getYear).last("LIMIT 1");
        if (ObjectUtils.isNotEmpty(roomNo)) {
            firstDayLambdaQueryWrapper.eq(ReportFee::getRoomNo, roomNo);
        }
        ReportFee firstDayReportFee = reportFeeService.getOne(firstDayLambdaQueryWrapper);
        if (ObjectUtils.isEmpty(firstDayReportFee)) {
            return projectRentRef;
        }
        int monthSum = 3;
        boolean is = false;
        for (int i = 1; i <= 3; i++) {
            LocalDate months = currentNow.minus(Period.ofMonths(i));
            String selectDate = months.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String year = selectDate.split("-")[0];
            String month = selectDate.split("-")[1];
            if (i == 3 && firstDayReportFee.getYear() > Integer.parseInt(year)) {
                is = true;
            } else if (i == 3 && firstDayReportFee.getYear() == Integer.parseInt(year)
                    && firstDayReportFee.getMonth() >= Integer.valueOf(month)) {
                is = true;
            }
            LambdaQueryWrapper<ReportFee> reportFeeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            reportFeeLambdaQueryWrapper.eq(ReportFee::getTenantId, tenantId).eq(ReportFee::getYear, year)
                    .eq(ReportFee::getMonth, month);
            if (ObjectUtils.isNotEmpty(roomNo)) {
                reportFeeLambdaQueryWrapper.eq(ReportFee::getRoomNo, roomNo);
            }
            List<ReportFee> reportFees = reportFeeService.list(reportFeeLambdaQueryWrapper);
            if (!CollectionUtils.isEmpty(reportFees)) {
                reportFeeList.addAll(reportFees);
            } else {
                monthSum--;
            }
        }
        if (!CollectionUtils.isEmpty(reportFeeList)) {
            BigDecimal theoryRent = new BigDecimal(0);
            for (ReportFee reportFee : reportFeeList) {
                if (ObjectUtils.isEmpty(reportFee.getTheoryRent())) {
                    continue;
                }
                theoryRent = theoryRent.add(reportFee.getTheoryRent());
            }
            //天数
            int daySum = is ? 90 : monthSum * 30;
            projectRentRef = theoryRent.divide(new BigDecimal(daySum), 4, BigDecimal.ROUND_HALF_UP);
        }
        return projectRentRef;
    }

    /**
     * 获取销售总额
     *
     * @param tenantId
     * @param startDate
     * @param endDate
     * @param roomNo
     * @return
     */
    private Map<String, BigDecimal> getProjectSaleRef(LocalDate threeMonthsAgo, Long tenantId, String startDate, String endDate, String roomNo) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        resultMap.put("projectSaleRef", new BigDecimal(0));
        resultMap.put("totalStoreOrder", new BigDecimal(0));
        BigDecimal projectSaleRef = new BigDecimal(0);
        //第一天数据
        LambdaQueryWrapper<ReportMasterSaleData> firstDayLambdaQueryWrapper = new LambdaQueryWrapper<>();
        firstDayLambdaQueryWrapper.eq(ReportMasterSaleData::getTenantId, tenantId)
                .orderByAsc(ReportMasterSaleData::getSaleTime).last("LIMIT 1");
        //项目周销售额
        LambdaQueryWrapper<ReportMasterSaleData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ReportMasterSaleData::getTenantId, tenantId)
                .ge(ReportMasterSaleData::getSaleTime, startDate)
                .lt(ReportMasterSaleData::getSaleTime, endDate);
        if (ObjectUtils.isNotEmpty(roomNo)) {
            lambdaQueryWrapper.eq(ReportMasterSaleData::getShopNo, roomNo);
            firstDayLambdaQueryWrapper.eq(ReportMasterSaleData::getShopNo, roomNo);
        }
        ReportMasterSaleData firstDay = reportMasterSaleDataService.getOne(firstDayLambdaQueryWrapper);
        if (ObjectUtils.isEmpty(firstDay)) {
            return resultMap;
        }
        //判断日期
        boolean is = firstDay.getSaleTime().isBefore(threeMonthsAgo);
        List<ReportMasterSaleData> reportMasterSaleDataList = reportMasterSaleDataService.list(lambdaQueryWrapper);
        if (ObjectUtils.isNotEmpty(reportMasterSaleDataList)) {
            Set<String> daySet = new HashSet<>();
            //项目销售总额
            BigDecimal totalAmountCount = new BigDecimal(0);
            //项目销售笔数
            BigDecimal totalStoreOrder = new BigDecimal(0);
            for (ReportMasterSaleData reportMasterSaleData : reportMasterSaleDataList) {
                String saleTimeStr = reportMasterSaleData.getSaleTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                daySet.add(saleTimeStr);
                if (ObjectUtils.isNotEmpty(reportMasterSaleData.getTotalAmount())) {
                    totalAmountCount = totalAmountCount.add(reportMasterSaleData.getTotalAmount());
                }
                if (ObjectUtils.isNotEmpty(reportMasterSaleData.getStoreOrder())) {
                    totalStoreOrder = totalStoreOrder.add(new BigDecimal(reportMasterSaleData.getStoreOrder()));
                }
            }
            BigDecimal num = new BigDecimal(is ? 90 : daySet.size());
            projectSaleRef = totalAmountCount.divide(num, 4, BigDecimal.ROUND_HALF_UP);
            totalStoreOrder = totalStoreOrder.divide(num, 4, BigDecimal.ROUND_HALF_UP);
            resultMap.put("projectSaleRef", projectSaleRef);
            resultMap.put("totalStoreOrder", totalStoreOrder);
        }
        return resultMap;
    }

    /**
     * 获取客流
     *
     * @return
     */
    private BigDecimal getProjectFlowRef(LocalDate threeMonthsAgo, String startDate, String endDate, Long tenantId, String fname) {
        BigDecimal projectFlowRef = new BigDecimal(0);
        //第一天数据
        LambdaQueryWrapper<ReportStatisFlowData> firstDayLambdaQueryWrapper = new LambdaQueryWrapper<>();
        firstDayLambdaQueryWrapper.eq(ReportStatisFlowData::getTenantId, tenantId)
                .orderByAsc(ReportStatisFlowData::getStatisTime).last("LIMIT 1");
        //项目总客流
        LambdaQueryWrapper<ReportStatisFlowData> statisFlowDataLambdaQueryWrapper = new LambdaQueryWrapper<>();
        statisFlowDataLambdaQueryWrapper.eq(ReportStatisFlowData::getTenantId, tenantId).ge(ReportStatisFlowData::getStatisTime, startDate)
                .lt(ReportStatisFlowData::getStatisTime, endDate);
        if (ObjectUtils.isNotEmpty(fname)) {
            firstDayLambdaQueryWrapper.eq(ReportStatisFlowData::getStatisType, FlowDataStatisTypeEnum.SHOP_ENTRANCE_AND_EXIT.value())
                    .eq(ReportStatisFlowData::getUseObj, fname);
            statisFlowDataLambdaQueryWrapper.eq(ReportStatisFlowData::getStatisType, FlowDataStatisTypeEnum.SHOP_ENTRANCE_AND_EXIT.value())
                    .eq(ReportStatisFlowData::getUseObj, fname);
        } else {
            statisFlowDataLambdaQueryWrapper.eq(ReportStatisFlowData::getStatisType, FlowDataStatisTypeEnum.PROJECT_ENTRANCE_AND_EXIT.value());
            firstDayLambdaQueryWrapper.eq(ReportStatisFlowData::getStatisType, FlowDataStatisTypeEnum.PROJECT_ENTRANCE_AND_EXIT.value());
        }
        ReportStatisFlowData firstDay = reportStatisFlowDataService.getOne(firstDayLambdaQueryWrapper);
        if (ObjectUtils.isEmpty(firstDay)) {
            return projectFlowRef;
        }
        //判断日期
        boolean is = firstDay.getStatisTime().isBefore(threeMonthsAgo);
        List<ReportStatisFlowData> reportStatisFlowDataList = reportStatisFlowDataService.list(statisFlowDataLambdaQueryWrapper);
        if (ObjectUtils.isNotEmpty(reportStatisFlowDataList)) {
            Set<String> daySet = new HashSet<>();
            Long enterCountCount = 0L;
            Long frontCountCount = 0L;
            for (ReportStatisFlowData reportStatisFlowData : reportStatisFlowDataList) {
                String statisTimeStr = reportStatisFlowData.getStatisTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                daySet.add(statisTimeStr);
                if (ObjectUtils.isNotEmpty(reportStatisFlowData.getEnterCount())) {
                    enterCountCount = enterCountCount + reportStatisFlowData.getEnterCount();
                }
                if (ObjectUtils.isNotEmpty(reportStatisFlowData.getOutCount())) {
                    frontCountCount = frontCountCount + reportStatisFlowData.getFrontCount();
                }
            }
            BigDecimal num = new BigDecimal(is ? 90 : daySet.size());
            if (ObjectUtils.isNotEmpty(fname)) {
                projectFlowRef = new BigDecimal(frontCountCount).divide(num, 4, BigDecimal.ROUND_HALF_UP);
            } else {
                projectFlowRef = new BigDecimal(enterCountCount).divide(num, 4, BigDecimal.ROUND_HALF_UP);
            }
        }
        return projectFlowRef;
    }

    private String humpToLine(String str) {
        return str.replaceAll("[A-Z]", "_$0").toLowerCase();
    }
}
