package com.seewin.som.report.provider.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.seewin.som.report.entity.ReportOperateDay;
import com.seewin.som.report.provider.ReportOperateDayProvider;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import com.seewin.som.report.service.ReportOperateDayService;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目面积数据统计到天 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@DubboService
public class ReportOperateDayProviderImpl implements ReportOperateDayProvider {

	@Autowired
	private ReportOperateDayService reportOperateDayService;

    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportOperateDayAddVo add(ReportOperateDayAddDto dto) throws ServiceException
    {
        ReportOperateDay entity = BeanUtils.copyProperties(dto, ReportOperateDay.class);

        LocalDateTime nowTime = LocalDateTime.now();                
     	entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setCreateBy(dto.getCreateBy());
        entity.setCreateUser(dto.getCreateUser());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setUpdateBy(dto.getCreateBy());
        entity.setUpdateUser(dto.getCreateUser());
        entity.setUpdateUserName(dto.getCreateUserName());
        entity.setUpdateTime(nowTime);
        entity.setBrandId(dto.getBrandId());
        entity.setBrandName(dto.getBrandName());
	    entity.setDelStatus(0);
        reportOperateDayService.save(entity);

        //响应结果封装
        ReportOperateDayAddVo result = new ReportOperateDayAddVo();
        result.setId(entity.getId());

        return result;
    }

    @Override
    public void delete(ReportOperateDayListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportOperateDay> queryWrapper = queryBuild(dto, false);
        //删除操作
        reportOperateDayService.remove(queryWrapper);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportOperateDay> queryBuild(ReportOperateDayListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<ReportOperateDay> queryWrapper = new QueryWrapper<>();

        ReportOperateDay entity = BeanUtils.copyProperties(dto, ReportOperateDay.class);
        entity.setDelStatus(0);
        if(dto.getStartDate()!=null&&dto.getEndDate()!=null){
            int startYear = dto.getStartDate().getYear();
            int startMonth = dto.getStartDate().getMonthValue();
            int startDay = dto.getStartDate().getDayOfMonth();
            int endYear = dto.getEndDate().getYear();
            int endMonth = dto.getEndDate().getMonthValue();
            int endDay = dto.getEndDate().getDayOfMonth();
            queryWrapper.and(wrapper -> wrapper
                    .gt("year", startYear)
                    .or()
                    .eq("year", startYear)
                    .gt("month", startMonth)
                    .or()
                    .eq("year", startYear)
                    .eq("month", startMonth)
                    .ge("day", startDay)
            );
            queryWrapper.and(wrapper -> wrapper
                    .lt("year", endYear)
                    .or()
                    .eq("year", endYear)
                    .lt("month", endMonth)
                    .or()
                    .eq("year", endYear)
                    .eq("month", endMonth)
                    .le("day", endDay)
            );
        }
        queryWrapper.setEntity(entity);

        return queryWrapper;
    }

    @Override
    public List<ReportMultiDataVo> rentRate(ReportMultiDataDto dto) {
        return reportOperateDayService.rentRate(dto);
    }

    @Override
    public List<ReportMultiDataVo> openRate(ReportMultiDataDto dto) {
        return reportOperateDayService.openRate(dto);
    }

    @Override
    public List<ReportMultiDataVo> openArea(ReportMultiDataDto dto) {
        return reportOperateDayService.openArea(dto);
    }

    @Override
    public List<ReportMultiDataDetailVo> saleSquareStoreContrast(ReportMultiDataDto multiDataDto) {
        return reportOperateDayService.saleSquareStoreContrast(multiDataDto);
    }

    @Override
    public Double saleSquareArea(ReportMultiDataDto dto) {
        return reportOperateDayService.saleSquareArea(dto);
    }

    @Override
    public List<ReportMultiDataDetailVo> saleSquareTenantDetail(ReportMultiDataDto dto) {
        return reportOperateDayService.saleSquareTenantDetail(dto);
    }

    @Override
    public List<ReportOperateDayStatisticVo> getOperatingRooms(CategoryAnalyseDto dto) {
        return reportOperateDayService.getOperatingRooms(dto);
    }

    @Override
    public List<ReportOperateDayStatisticVo> getOperatingRoomsDetail(CategoryAnalyseDto dto) {
        return reportOperateDayService.getOperatingRoomsDetail(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> multiAnalyseList(ReportMultiAnalyseListDto dto) {
        return reportOperateDayService.multiAnalyseList(dto);
    }
    @Override
    public List<ReportOperateDayListVo> list(ReportOperateDayListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportOperateDay> queryWrapper = queryBuild(dto, true);

        //查询数据
        List<ReportOperateDay> records = reportOperateDayService.list(queryWrapper);

        //响应结果封装
        List<ReportOperateDayListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, ReportOperateDayListVo.class);

        //返回查询结果
        return result;
    }
    @Override
    public List<ReportOperateDayListVo> getOperateDayAndFlowList(ReportOperateDayListDto dto) {
        return reportOperateDayService.getOperateDayAndFlowList(dto);
    }

    @Override
    public List<ReportOperateDayAnalyseListVo> getAllData(ReportOperateDayAnalyseDto dto) {
        return reportOperateDayService.getAllData(dto);
    }


    @Override
    public Double getYoyAndQoqRate(ReportOperateDayAnalyseDto dto) {
        return reportOperateDayService.getYoyAndQoqRate(dto);
    }

    @Override
    public List<ReportOperateDayAnalyseListVo> getOpenAllData(ReportOperateDayAnalyseDto dto) {
        return reportOperateDayService.getOpenAllData(dto);
    }

    @Override
    public List<Map<String, Double>> getCityAllData(ReportOperateDayAnalyseDto dto) {
        return reportOperateDayService.getCityAllData(dto);
    }

    @Override
    public List<Map<String, Double>> getCityOpenAllData(ReportOperateDayAnalyseDto dto) {
        return reportOperateDayService.getCityOpenAllData(dto);
    }
}
