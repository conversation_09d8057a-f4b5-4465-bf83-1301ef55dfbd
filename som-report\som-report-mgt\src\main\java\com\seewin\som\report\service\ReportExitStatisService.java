package com.seewin.som.report.service;

import com.seewin.som.report.entity.ReportExitStatis;
import com.baomidou.mybatisplus.extension.service.IService;
import com.seewin.som.report.req.ReportMultiDataDto;
import com.seewin.som.report.req.ReportOperateDayAnalyseDto;
import com.seewin.som.report.resp.ReportExitStatisListVo;
import com.seewin.som.report.resp.ReportExitVo;
import com.seewin.som.report.resp.ReportMultiDataVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目门店撤场数据分析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29
 */
public interface ReportExitStatisService extends IService<ReportExitStatis> {

    List<ReportExitVo> exitAreaRateByDay(ReportMultiDataDto dto);

    List<ReportExitVo> exitAreaRateByMonth(ReportMultiDataDto dto);

    List<ReportExitStatisListVo> getAllData(ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityAllData(ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityDateAllData(ReportOperateDayAnalyseDto dto);
}
