package com.seewin.som.report.service;

import com.seewin.som.report.entity.ReportMasterSaleData;
import com.baomidou.mybatisplus.extension.service.IService;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 主数据库销售数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
public interface ReportMasterSaleDataService extends IService<ReportMasterSaleData> {
    BigDecimal getTotalAmountSum(ReportMasterSaleDataListDto dto);

    /**
     * iot销售数据 按销售日期累加入库
     */
    void accSaleData(ReportMasterSaleDataAddDto saleDataListDto);

    List<ReportMasterSaleData> statisticData(MasterDataStatReq req);

    List<ReportCategoryCompareVo> categorySaleAmountCompare(ReportCategoryCompareDto dto);

    List<ReportCategoryCompareVo> categoryGuestAvgPriceCompare(ReportCategoryCompareDto dto);

    List<ReportMultiDataVo> saleConvertRate(ReportMultiDataDto multiDataDto);
    /**
     * 分组获取店铺月销售总额
     * @param saleMonth
     * @return
     */
    List<SaleDataMonthTotalAmountVo> selectMonthTotalAmount(String saleMonth);

    List<ReportMultiDataDetailVo> saleConvertStoreContrast(ReportMultiDataDto multiDataDto);

    List<ReportMultiDataVo> saleSquare(ReportMultiDataDto multiDataDto);

    /**
     * 获取StoreId与SaleTime连接字符串(用于判断店铺是否存在某销售日期数据)
     * <AUTHOR>
     * @date 2024/6/5 15:25
     * @param tenantId
     * @param objCodes
     * @return java.util.Set<java.lang.String>
     */
    Set<String> selectStoreIdSaleTime(long tenantId, List<String> objCodes);

    ReportMultiDataProjectVo getExportData(ReportMultiDataDto curMultiDataDto);

    List<ReportMultiDataStoreVo> getStoreSaleData(ReportMultiDataDto curMultiDataDto);

    BigDecimal getSaleTotalById(Long tenantId, String storeId, LocalDate periodStart, LocalDate periodEnd);

    List<SaleDataCategoryAnalyseVo> categoryAnalyseStatistic(CategoryAnalyseDto dto);

    List<String> saleRank(CategoryAnalyseDto dto);

    List<SaleDataCategoryAnalyseVo> categoryAnalyseStatisticDetail(CategoryAnalyseDto dto);

    BigDecimal getSaleTotalAmount(ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleDistributeList(ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleAmountByDate(ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleAnalyseList(ReportMultiAnalyseListDto dto);

    List<ReportMultiAnalyseVo> getSaleDetailByGatherType(ReportMultiAnalyseListDto dto);

    List<ReportSaleAndFlowListVo> getOperationReport(OperationReportListDto dto);

    List<SaleDataAnalyseVo> saleAmount(MultiDataAnalyseDto dto);

    List<SaleDataAnalyseVo> salePredictAmount(MultiDataAnalyseDto dto);

    List<SaleDataAnalyseVo> salePredictStoreAmount(MultiDataAnalyseDto dto);

    PredictDataVo getPredictData(PredictDataDto dto);

    PredictDataVo getPredictStoreData(PredictDataDto dto);

    List<ReportSaleRecommendVo> getRecommendSaleTotal(ReportMasterSaleDataListDto saleListDto);

    /**
     * 查询客流、销售额、租管费 汇总查询
     */
    List<ManageAnalyseVo> getFlowAmountFeeByProject(ReportManageMultiAnalyseListDto dto);
}
