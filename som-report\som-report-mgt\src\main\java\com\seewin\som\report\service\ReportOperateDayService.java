package com.seewin.som.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.seewin.som.report.entity.ReportOperateDay;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目面积数据统计到天 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
public interface ReportOperateDayService extends IService<ReportOperateDay> {

    List<ReportMultiDataVo> rentRate(ReportMultiDataDto dto);

    List<ReportMultiDataVo> openRate(ReportMultiDataDto dto);

    List<ReportMultiDataVo> openArea(ReportMultiDataDto dto);

    List<ReportMultiDataDetailVo> saleSquareStoreContrast(ReportMultiDataDto dto);

    Double saleSquareArea(ReportMultiDataDto dto);

    List<ReportMultiDataDetailVo> saleSquareTenantDetail(ReportMultiDataDto dto);

    List<ReportMultiDataVo> getRoomAreaByData(MobileSaleDataAnalyseDto dto);

    List<ReportOperateDayStatisticVo> getOperatingRooms(CategoryAnalyseDto dto);

    List<ReportOperateDayStatisticVo> getOperatingRoomsDetail(CategoryAnalyseDto dto);

    List<ReportMultiAnalyseVo> multiAnalyseList(ReportMultiAnalyseListDto dto);

    List<ReportOperateDayListVo> getOperateDayAndFlowList( ReportOperateDayListDto dto);

    List<ReportOperateDayAnalyseListVo> getAllData(ReportOperateDayAnalyseDto dto);

    List<ReportOperateDayAnalyseListVo> getOpenAllData(ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityAllData(ReportOperateDayAnalyseDto dto);

    List<Map<String, Double>> getCityOpenAllData(ReportOperateDayAnalyseDto dto);

}
