package com.seewin.som.report.service.impl;

import com.seewin.som.report.entity.ReportExitStatis;
import com.seewin.som.report.mapper.ReportExitStatisMapper;
import com.seewin.som.report.req.ReportMultiDataDto;
import com.seewin.som.report.req.ReportOperateDayAnalyseDto;
import com.seewin.som.report.resp.ReportExitStatisListVo;
import com.seewin.som.report.resp.ReportExitVo;
import com.seewin.som.report.resp.ReportMultiDataVo;
import com.seewin.som.report.service.ReportExitStatisService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 项目门店撤场数据分析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29
 */
@Service
public class ReportExitStatisServiceImpl extends ServiceImpl<ReportExitStatisMapper, ReportExitStatis> implements ReportExitStatisService {
    @Autowired
    private ReportExitStatisMapper reportExitStatisMapper;
    @Override
    public List<ReportExitVo> exitAreaRateByDay(ReportMultiDataDto dto) {
        return reportExitStatisMapper.exitAreaRateByDay(dto);
    }

    @Override
    public List<ReportExitVo> exitAreaRateByMonth(ReportMultiDataDto dto) {
        return reportExitStatisMapper.exitAreaRateByMonth(dto);
    }

    @Override
    public List<ReportExitStatisListVo> getAllData(ReportOperateDayAnalyseDto dto) {
        return reportExitStatisMapper.getAllData(dto);
    }
}
