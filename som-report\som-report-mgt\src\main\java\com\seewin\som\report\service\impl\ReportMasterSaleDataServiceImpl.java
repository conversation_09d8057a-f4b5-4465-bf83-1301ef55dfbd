package com.seewin.som.report.service.impl;

import com.seewin.som.report.entity.ReportMasterSaleData;
import com.seewin.som.report.mapper.ReportMasterSaleDataMapper;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import com.seewin.som.report.service.ReportMasterSaleDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.seewin.util.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 主数据库销售数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
@Service
public class ReportMasterSaleDataServiceImpl extends ServiceImpl<ReportMasterSaleDataMapper, ReportMasterSaleData> implements ReportMasterSaleDataService {
    @Autowired
    private ReportMasterSaleDataMapper reportMasterSaleDataMapper;

    @Override
    public BigDecimal getTotalAmountSum(ReportMasterSaleDataListDto dto) {
        return reportMasterSaleDataMapper.getTotalAmountSum(dto);
    }

    @Override
    public void accSaleData(ReportMasterSaleDataAddDto saleDataListDto) {
        ReportMasterSaleData iotMasterSaleData = BeanUtils.copyProperties(saleDataListDto, ReportMasterSaleData.class);
        this.reportMasterSaleDataMapper.accReportSaleData(iotMasterSaleData);


    }

    @Override
    public List<ReportMasterSaleData> statisticData(MasterDataStatReq req) {
        return this.reportMasterSaleDataMapper.statisticData(req);
    }

    @Override
    public List<ReportCategoryCompareVo> categorySaleAmountCompare(ReportCategoryCompareDto dto) {
        return reportMasterSaleDataMapper.categorySaleAmountCompare(dto);
    }

    @Override
    public List<ReportCategoryCompareVo> categoryGuestAvgPriceCompare(ReportCategoryCompareDto dto) {
        return reportMasterSaleDataMapper.categoryGuestAvgPriceCompare(dto);
    }

    @Override
    public List<ReportMultiDataVo> saleConvertRate(ReportMultiDataDto multiDataDto) {
        return reportMasterSaleDataMapper.saleConvertRate(multiDataDto);
    }

    @Override
    public List<SaleDataMonthTotalAmountVo> selectMonthTotalAmount(String saleMonth) {
        return baseMapper.selectMonthTotalAmount(saleMonth);
    }

    @Override
    public List<ReportMultiDataDetailVo> saleConvertStoreContrast(ReportMultiDataDto multiDataDto) {
        return reportMasterSaleDataMapper.saleConvertStoreContrast(multiDataDto);
    }

    @Override
    public List<ReportMultiDataVo> saleSquare(ReportMultiDataDto multiDataDto) {
        return reportMasterSaleDataMapper.saleSquare(multiDataDto);
    }

    @Override
    public Set<String> selectStoreIdSaleTime(long tenantId, List<String> objCodes) {
        return reportMasterSaleDataMapper.selectStoreIdSaleTime(tenantId,objCodes);
    }

    @Override
    public ReportMultiDataProjectVo getExportData(ReportMultiDataDto curMultiDataDto) {
        return reportMasterSaleDataMapper.getExportData(curMultiDataDto);
    }

    @Override
    public List<ReportMultiDataStoreVo> getStoreSaleData(ReportMultiDataDto curMultiDataDto) {
        return reportMasterSaleDataMapper.getStoreSaleData(curMultiDataDto);
    }

    @Override
    public BigDecimal getSaleTotalById(Long tenantId, String storeId, LocalDate periodStart, LocalDate periodEnd) {
        return reportMasterSaleDataMapper.getSaleTotalById(tenantId, storeId, periodStart, periodEnd);
    }

    @Override
    public List<SaleDataCategoryAnalyseVo> categoryAnalyseStatistic(CategoryAnalyseDto dto) {
        return reportMasterSaleDataMapper.categoryAnalyseStatistic(dto);
    }

    @Override
    public List<String> saleRank(CategoryAnalyseDto dto) {
        return reportMasterSaleDataMapper.saleRank(dto);
    }

    @Override
    public List<SaleDataCategoryAnalyseVo> categoryAnalyseStatisticDetail(CategoryAnalyseDto dto) {
        return reportMasterSaleDataMapper.categoryAnalyseStatisticDetail(dto);
    }

    @Override
    public BigDecimal getSaleTotalAmount(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataMapper.getSaleTotalAmount(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> getSaleDistributeList(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataMapper.getSaleDistributeList(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> getSaleAmountByDate(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataMapper.getSaleAmountByDate(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> getSaleAnalyseList(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataMapper.getSaleAnalyseList(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> getSaleDetailByGatherType(ReportMultiAnalyseListDto dto) {
        return reportMasterSaleDataMapper.getSaleDetailByGatherType(dto);
    }

    @Override
    public List<ReportSaleAndFlowListVo> getOperationReport(OperationReportListDto dto) {
        return  reportMasterSaleDataMapper.getOperationReport(dto);
    }

    @Override
    public List<SaleDataAnalyseVo> saleAmount(MultiDataAnalyseDto dto) {
        return reportMasterSaleDataMapper.saleAmount(dto);
    }

    @Override
    public List<SaleDataAnalyseVo> salePredictAmount(MultiDataAnalyseDto dto) {
        return reportMasterSaleDataMapper.salePredictAmount(dto);
    }

    @Override
    public List<SaleDataAnalyseVo> salePredictStoreAmount(MultiDataAnalyseDto dto) {
        return reportMasterSaleDataMapper.salePredictStoreAmount(dto);
    }

    @Override
    public PredictDataVo getPredictData(PredictDataDto dto) {
        return reportMasterSaleDataMapper.getPredictData(dto);
    }

    @Override
    public PredictDataVo getPredictStoreData(PredictDataDto dto) {
        return reportMasterSaleDataMapper.getPredictStoreData(dto);
    }

    @Override
    public List<ReportSaleRecommendVo> getRecommendSaleTotal(ReportMasterSaleDataListDto saleListDto) {
        return reportMasterSaleDataMapper.getRecommendSaleTotal(saleListDto);
    }

    @Override
    public List<ManageAnalyseVo> getFlowAmountFeeByProject(ReportManageMultiAnalyseListDto dto) {
        return reportMasterSaleDataMapper.getFlowAmountFeeByProject(dto);
    }

}
