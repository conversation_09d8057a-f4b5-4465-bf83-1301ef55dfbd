<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.report.mapper.ReportExitStatisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.report.entity.ReportExitStatis">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="fid" property="fid" />
        <result column="fcode" property="fcode" />
        <result column="fname" property="fname" />
        <result column="room_id" property="roomId" />
        <result column="room_name" property="roomName" />
        <result column="room_shop_id" property="roomShopId" />
        <result column="room_status" property="roomStatus" />
        <result column="actual_area" property="actualArea" />
        <result column="exit_date" property="exitDate" />
        <result column="adaptive_format_id" property="adaptiveFormatId" />
        <result column="adaptive_format" property="adaptiveFormat" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="commercial_two_id" property="commercialTwoId" />
        <result column="commercial_two" property="commercialTwo" />
        <result column="brand_id" property="brandId" />
        <result column="brand_name" property="brandName" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
    </resultMap>
    <select id="exitAreaRateByDay" resultType="com.seewin.som.report.resp.ReportExitVo">
        SELECT
            DATE_FORMAT( exit_date, '%Y-%m-%d' ) as dateStr,
            sum(actual_area) as value,
            count(1) exitNumber
        FROM
            som_report_exit_statis
        WHERE
            DATE_FORMAT( exit_date, '%Y-%m-%d' ) BETWEEN #{dto.startLocalDate}
          AND #{dto.endLocalDate}
          and tenant_id = #{dto.tenantId}
          AND del_status = 0 and exit_date is not null
        GROUP BY
            DATE_FORMAT( exit_date, '%Y-%m-%d' )
        ORDER BY
            DATE_FORMAT( exit_date, '%Y-%m-%d' )
    </select>
    <select id="exitAreaRateByMonth" resultType="com.seewin.som.report.resp.ReportExitVo">
        SELECT
            DATE_FORMAT( exit_date, '%Y-%m' ) as dateStr,
            sum(actual_area) as value,
            count(1) exitNumber
        FROM
            som_report_exit_statis
        WHERE
            DATE_FORMAT( exit_date, '%Y-%m-%d' ) BETWEEN #{dto.startLocalDate}
          AND #{dto.endLocalDate}
          and tenant_id = #{dto.tenantId}
          AND del_status = 0 and exit_date is not null
        GROUP BY
            DATE_FORMAT( exit_date, '%Y-%m' )
        ORDER BY
            DATE_FORMAT( exit_date, '%Y-%m' )
    </select>


    <select id="getAllData" resultType="com.seewin.som.report.resp.ReportExitStatisListVo">
        SELECT
            tenant_id ,
            tenant_name,
            exit_date,
            actual_area,
            adaptive_format,
            category_name,
            commercial_two
        FROM
            som_report_exit_statis
        WHERE
            DATE_FORMAT( exit_date, '%Y-%m-%d' ) BETWEEN #{dto.startDate} AND #{dto.endDate} AND del_status = 0 and exit_date is not null
        ORDER BY
            DATE_FORMAT( exit_date, '%Y-%m-%d' )
    </select>


    <select id="getCityAllData" resultType="map">
        SELECT
            tenant_name name,
            SUM(actual_area) AS area
        FROM
            som_report_exit_statis
        WHERE
            DATE_FORMAT(exit_date, '%Y-%m-%d') BETWEEN  #{dto.startDate} AND #{dto.endDate}
          AND del_status = 0
          AND exit_date IS NOT NULL
            <if test="dto.tenantNames != null and dto.tenantNames.size() > 0">
                AND tenant_name IN
                <foreach collection="dto.tenantNames" item="tenant" open="(" separator="," close=")">
                    #{tenant}
                </foreach>
            </if>
        GROUP BY
            tenant_name
        ORDER BY
            DATE_FORMAT(exit_date, '%Y-%m-%d');
    </select>


    <select id="getCityDateAllData" resultType="map">
        SELECT
            DATE_FORMAT(exit_date, '%Y-%m-%d') AS date,
            SUM(actual_area) AS value
        FROM
            som_report_exit_statis
        WHERE
            DATE_FORMAT(exit_date, '%Y-%m-%d') BETWEEN  #{dto.startDate} AND #{dto.endDate}
          AND del_status = 0
          AND exit_date IS NOT NULL
        <if test="dto.tenantNames != null and dto.tenantNames.size() > 0">
            AND tenant_name IN
            <foreach collection="dto.tenantNames" item="tenant" open="(" separator="," close=")">
                #{tenant}
            </foreach>
        </if>
        GROUP BY
            exit_date
        ORDER BY
            exit_date;
    </select>
</mapper>
