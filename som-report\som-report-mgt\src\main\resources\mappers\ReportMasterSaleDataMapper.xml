<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.report.mapper.ReportMasterSaleDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.report.entity.ReportMasterSaleData">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="shop_no" property="shopNo" />
        <result column="use_obj" property="useObj" />
        <result column="obj_code" property="objCode" />
        <result column="store_id" property="storeId" />
        <result column="brand_name" property="brandName" />
        <result column="commercial_type_code" property="commercialTypeCode" />
        <result column="commercial_type_name" property="commercialTypeName" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="total_amount" property="totalAmount" />
        <result column="before_total_amount" property="beforeTotalAmount" />
        <result column="total_order" property="totalOrder" />
        <result column="before_total_order" property="beforeTotalOrder" />
        <result column="store_amount" property="storeAmount" />
        <result column="before_store_amount" property="beforeStoreAmount" />
        <result column="store_order" property="storeOrder" />
        <result column="before_store_order" property="beforeStoreOrder" />
        <result column="takeaway_amount" property="takeawayAmount" />
        <result column="before_takeaway_amount" property="beforeTakeawayAmount" />
        <result column="takeaway_order" property="takeawayOrder" />
        <result column="before_takeaway_order" property="beforeTakeawayOrder" />
        <result column="data_sources" property="dataSources" />
        <result column="data_status" property="dataStatus" />
        <result column="sale_time" property="saleTime" />
        <result column="before_sale_data" property="beforeSaleData" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
    </resultMap>


    <insert id="accReportSaleData">
        INSERT INTO `som_report`.som_report_master_sale_data (id, tenant_id, tenant_name, ent_id, org_fid, org_fname,
                                                              shop_no, use_obj,
                                                              obj_code, brand_name,
                                                              commercial_type_code, commercial_type_name, category_id, category_name,
                                                              total_amount, before_total_amount,
                                                              total_order,
                                                              before_total_order, store_amount, before_store_amount,
                                                              store_order,
                                                              before_store_order, takeaway_amount,
                                                              before_takeaway_amount,
                                                              takeaway_order, before_takeaway_order, data_sources,
                                                              data_status,
                                                              sale_time, create_time,store_id,city,commercial_two_id,commercial_two)
        VALUES (#{item.id},
                #{item.tenantId},
                #{item.tenantName},
                #{item.entId},
                #{item.orgFid},
                #{item.orgFname},
                #{item.shopNo},
                #{item.useObj},
                #{item.objCode},
                #{item.brandName},
                #{item.commercialTypeCode},
                #{item.commercialTypeName},
                #{item.categoryId},
                #{item.categoryName},
                #{item.totalAmount},
                #{item.beforeTotalAmount},
                #{item.totalOrder},
                #{item.beforeTotalOrder},
                #{item.storeAmount},
                #{item.beforeStoreAmount},
                #{item.storeOrder},
                #{item.beforeStoreOrder},
                #{item.takeawayAmount},
                #{item.beforeTakeawayAmount},
                #{item.takeawayOrder},
                #{item.beforeTakeawayOrder},
                #{item.dataSources},
                #{item.dataStatus},
                #{item.saleTime},
                #{item.createTime},
                #{item.storeId},
                #{item.city},
                #{item.commercialTwoId},
                #{item.commercialTwo}) ON DUPLICATE KEY
        UPDATE
            total_amount = total_amount + VALUES (total_amount),
            before_total_amount = before_total_amount + VALUES (before_total_amount),
            total_order = total_order + VALUES (total_order),
            before_total_order = before_total_order + VALUES (before_total_order),
            store_amount = store_amount + VALUES (store_amount),
            before_store_amount = before_store_amount + VALUES (before_store_amount),
            store_order = store_order + VALUES (store_order),
            before_store_order = before_store_order + VALUES (before_store_order),
            takeaway_amount = takeaway_amount + VALUES (takeaway_amount),
            before_takeaway_amount = before_takeaway_amount + VALUES (before_takeaway_amount),
            takeaway_order = takeaway_order + VALUES (takeaway_order),
            before_takeaway_order = before_takeaway_order + VALUES (before_takeaway_order)

    </insert>

    <select id="getTotalAmountSum" parameterType="com.seewin.som.report.req.ReportMasterSaleDataListDto" resultType="java.math.BigDecimal">
        SELECT
        SUM(total_amount)
        from som_report_master_sale_data
        <where>
            del_status=0
            <if test="dto.tenantId != null">
                and `tenant_id` = #{dto.tenantId}
            </if>
            <if test="dto.saleTime != null">
                and `sale_time` = #{dto.saleTime}
            </if>
            <if test="dto.objCode != null">
                and `obj_code` = #{dto.objCode}
            </if>
            <if test="dto.startDate != null and dto.endDate != null ">
                AND sale_time BETWEEN #{dto.startDate} and #{dto.endDate}
            </if>
        </where>
    </select>
    <select id="statisticData" resultMap="BaseResultMap">
        select avg(ifnull(total_amount ,0))*7 as total_amount,store_id
        from som_report_master_sale_data
        where tenant_id = #{req.tenantId}
        and store_id in
        <foreach collection="req.storeIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        and sale_time >= #{req.startDate}
        <if test="req.endDate != null">
            and sale_time &lt;= #{req.endDate}
        </if>
        group by store_id
    </select>

    <select id="categorySaleAmountCompare" resultType="com.seewin.som.report.resp.ReportCategoryCompareVo">
        SELECT shop_no, brand_name, SUM(total_amount) as value
        FROM som_report_master_sale_data
        WHERE del_status = 0 AND tenant_id = #{dto.tenantId}
        AND shop_no in
        <foreach collection="dto.shopNoList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND brand_name in
        <foreach collection="dto.brandNameList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND sale_time BETWEEN #{dto.startLocalDate} AND #{dto.endLocalDate}
        GROUP BY shop_no, brand_name
        ORDER BY value DESC
    </select>

    <select id="categoryGuestAvgPriceCompare" resultType="com.seewin.som.report.resp.ReportCategoryCompareVo">
        SELECT shop_no, brand_name, ROUND( SUM(total_amount) / SUM(total_order), 2) as value
        FROM som_report_master_sale_data
        WHERE del_status = 0 AND tenant_id = #{dto.tenantId}
        AND shop_no in
        <foreach collection="dto.shopNoList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND brand_name in
        <foreach collection="dto.brandNameList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND sale_time BETWEEN #{dto.startLocalDate} AND #{dto.endLocalDate}
        GROUP BY shop_no, brand_name
        ORDER BY value DESC
    </select>

    <select id="saleConvertRate" resultType="com.seewin.som.report.resp.ReportMultiDataVo">
        SELECT aa.saleTime as dateStr,
               IF(bb.flowSum IS NULL OR bb.flowSum = 0, 0, ROUND( aa.storeOrderSum  * 100 / bb.flowSum , 2) ) as value
        FROM
        (SELECT
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                sale_time as saleTime,
            </when>
            <otherwise>
                DATE_FORMAT(sale_time,'%Y-%m') as saleTime,
            </otherwise>
        </choose>
        sum(IFNULL(store_order, 0)) as storeOrderSum
        FROM som_report_master_sale_data
        WHERE del_status = 0
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        <if test="dto.storeId != null and dto.storeId !=''">
            and `store_id` = #{dto.storeId}
        </if>
        and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                GROUP BY sale_time
            </when>
            <otherwise>
                GROUP BY DATE_FORMAT(sale_time,'%Y-%m')
            </otherwise>
        </choose>
        ORDER BY saleTime ASC) aa

        LEFT JOIN

        (SELECT
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                statis_time as statisTime,
            </when>
            <otherwise>
                DATE_FORMAT(statis_time,'%Y-%m') as statisTime,
            </otherwise>
        </choose>
        <choose>
            <when test="dto.storeId != null and dto.storeId !=''">
                sum(IFNULL(front_count, 0)) as flowSum
            </when>
            <otherwise>
                sum(IFNULL(enter_count, 0)) as flowSum
            </otherwise>
        </choose>
        FROM som_report_statis_flow_data
        WHERE del_status = 0
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        <choose>
            <when test="dto.storeId != null and dto.storeId !=''">
                and  statis_type = 2
                and `store_id` = #{dto.storeId}
            </when>
            <otherwise>
                and  statis_type = 0
            </otherwise>
        </choose>
        and statis_time between #{dto.startLocalDate} and #{dto.endLocalDate}
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                GROUP BY statis_time
            </when>
            <otherwise>
                GROUP BY DATE_FORMAT(statis_time,'%Y-%m')
            </otherwise>
        </choose>
        ORDER BY statisTime ASC) bb

        ON aa.saleTime = bb.statisTime
    </select>

    <select id="selectMonthTotalAmount" resultType="com.seewin.som.report.resp.SaleDataMonthTotalAmountVo" parameterType="string">
        SELECT
            tenant_id,
            store_id,
            SUM( total_amount ) month_total_amount
        FROM
            `som_report_master_sale_data`
        WHERE
            store_id IS NOT NULL
          AND DATE_FORMAT(sale_time, '%Y-%m') = #{saleMonth}
        GROUP BY
            tenant_id,store_id
    </select>

    <select id="saleConvertStoreContrast" resultType="com.seewin.som.report.resp.ReportMultiDataDetailVo">
        SELECT aa.store_id, aa.brand_name, aa.storeLocation, aa.storeOrder, IFNULL(bb.doorFlow, 0) as doorFlow,
        IF(bb.doorFlow IS NULL OR bb.doorFlow = 0, 0, ROUND( aa.storeOrder  * 100 / bb.doorFlow , 2)) as value
        FROM
        (SELECT store_id, max(brand_name) as brand_name, max(use_obj) as storeLocation, sum(IFNULL(store_order, 0)) as storeOrder FROM som_report_master_sale_data
        WHERE del_status = 0
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        <if test='dto.storeIdList != null and dto.storeIdList.size > 0'>
            <foreach collection="dto.storeIdList" open=" AND store_id in ( " close=" ) " item="itemId" separator=",">
                #{itemId}
            </foreach>
        </if>
        and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}
        GROUP BY store_id
        ORDER BY store_id ASC) aa

        LEFT JOIN

        (SELECT store_id, sum(IFNULL(inshop_count, 0)) as doorFlow FROM som_report_statis_flow_data
        WHERE del_status = 0 and statis_type = 2
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        <if test='dto.storeIdList != null and dto.storeIdList.size > 0'>
            <foreach collection="dto.storeIdList" open=" AND store_id in ( " close=" ) " item="itemId" separator=",">
                #{itemId}
            </foreach>
        </if>
        and statis_time between #{dto.startLocalDate} and #{dto.endLocalDate}
        GROUP BY store_id
        ORDER BY store_id ASC) bb

        ON aa.store_id = bb.store_id
    </select>

    <select id="saleSquare" resultType="com.seewin.som.report.resp.ReportMultiDataVo">
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                SELECT aa.dateStr, ROUND(IFNULL(aa.totalSum, 0),2) as moleValue, ROUND(IFNULL(bb.totalRoomArea, 0),2) as denValue,
                       IF(bb.totalRoomArea IS NULL OR bb.totalRoomArea = 0, 0, ROUND( aa.totalSum / bb.totalRoomArea , 2) ) as value
                FROM
                (SELECT sale_time as dateStr, sum(IFNULL(total_amount, 0)) as totalSum FROM som_report_master_sale_data
                WHERE del_status = 0 and tenant_id = #{dto.tenantId}
                <if test="dto.storeId != null and dto.storeId !=''">
                    and `store_id` = #{dto.storeId}
                </if>
                <if test="dto.formatId != null and dto.formatId !=''">
                    and `commercial_type_code` = #{dto.formatId}
                </if>
                and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}
                GROUP BY sale_time
                ORDER BY dateStr ASC) aa

                LEFT JOIN

                (SELECT STR_TO_DATE( CONCAT( year, '-', month, '-', day ), '%Y-%m-%d' ) as dateStr, sum(IFNULL(room_area, 0)) as totalRoomArea
                FROM som_report_operate_day
                WHERE del_status = 0 and tenant_id = #{dto.tenantId}
                <if test="dto.storeId != null and dto.storeId !=''">
                    and `store_id` = #{dto.storeId}
                </if>
                <if test="dto.formatId != null and dto.formatId !=''">
                    and `commercial_type_code` = #{dto.formatId}
                </if>
                and STR_TO_DATE( CONCAT( year, '-', month, '-', day ), '%Y-%m-%d' ) between #{dto.startLocalDate} and #{dto.endLocalDate}
                GROUP BY STR_TO_DATE( CONCAT( year, '-', month, '-', day ), '%Y-%m-%d' )
                ORDER BY dateStr ASC) bb

                ON aa.dateStr = bb.dateStr
                ORDER BY aa.dateStr ASC
            </when>
            <otherwise>
                SELECT aa.dateStr, ROUND(IFNULL(aa.totalSum, 0),2) as moleValue, ROUND(IFNULL(bb.totalRoomArea, 0),2) as denValue,
                       IF(bb.totalRoomArea IS NULL OR bb.totalRoomArea = 0, 0, ROUND( aa.totalSum / bb.totalRoomArea , 2) ) as value
                FROM
                (SELECT DATE_FORMAT(sale_time,'%Y-%m') as dateStr, sum(IFNULL(total_amount, 0)) as totalSum FROM som_report_master_sale_data
                WHERE del_status = 0 and tenant_id = #{dto.tenantId}
                <if test="dto.storeId != null and dto.storeId !=''">
                    and `store_id` = #{dto.storeId}
                </if>
                <if test="dto.formatId != null and dto.formatId !=''">
                    and `commercial_type_code` = #{dto.formatId}
                </if>
                and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}
                GROUP BY DATE_FORMAT(sale_time,'%Y-%m')
                ORDER BY dateStr ASC) aa

                LEFT JOIN

                (SELECT CONCAT_WS("-", year, LPAD(`month`, 2, '0')) as dateStr, sum(IFNULL(room_area, 0)) as totalRoomArea
                FROM som_report_operate
                WHERE del_status = 0 and tenant_id = #{dto.tenantId}
                <if test="dto.storeId != null and dto.storeId !=''">
                    and `store_id` = #{dto.storeId}
                </if>
                <if test="dto.formatId != null and dto.formatId !=''">
                    and `commercial_type_code` = #{dto.formatId}
                </if>
                and STR_TO_DATE( CONCAT( year, '-', month, '-01'), '%Y-%m-%d' ) between #{dto.startLocalDate} and #{dto.endLocalDate}
                GROUP BY CONCAT_WS("-", year, LPAD(`month`, 2, '0'))
                ORDER BY dateStr ASC) bb

                ON aa.dateStr = bb.dateStr
                ORDER BY aa.dateStr ASC
            </otherwise>
        </choose>
    </select>

    <select id="selectStoreIdSaleTime" resultType="string">
        SELECT CONCAT(store_id, sale_time)
        FROM `som_report_master_sale_data`
        WHERE tenant_id = #{tenantId}
        AND obj_code IN
        <foreach collection="objCodes" item="objCode" open="(" separator="," close=")">
            #{objCode}
        </foreach>
    </select>

    <select id="getExportData" resultType="com.seewin.som.report.resp.ReportMultiDataProjectVo">
        SELECT IFNULL(sum(total_amount), 0.00) as totalAmount, IFNULL(sum(store_order), 0) as totalOrder, IFNULL(ROUND((sum(total_amount)/sum(total_order)),2), 0.00) as unitValue,
               IFNULL(sum(takeaway_amount), 0.00) as takeawayAmount, IFNULL(ROUND((sum(takeaway_amount)/sum(total_amount))*100,2), 0.00) as takeawayPercent
        FROM som_report_master_sale_data WHERE del_status = 0 and tenant_id = #{dto.tenantId}
        <if test="dto.storeId != null and dto.storeId !=''">
            and `store_id` = #{dto.storeId}
        </if>
        <if test="dto.commercialTypeName != null and dto.commercialTypeName !=''">
            and `commercial_type_name` = #{dto.commercialTypeName}
        </if>
        and sale_time BETWEEN #{dto.startLocalDate} AND #{dto.endLocalDate}
    </select>

    <select id="getStoreSaleData" resultType="com.seewin.som.report.resp.ReportMultiDataStoreVo">
        SELECT store_id, max(shop_no) as shopNo, max(brand_name) as brandName, max(commercial_type_name) as busType, max(category_name) as categoryName, IFNULL(sum(total_amount), 0.00) as totalAmount, IFNULL(sum(store_order), 0) as totalOrder,
               IFNULL(ROUND((sum(total_amount)/sum(total_order)),2), 0.00) as unitValue,IFNULL(sum(takeaway_amount), 0.00) as takeawayAmount, IFNULL(ROUND((sum(takeaway_amount)/sum(total_amount))*100,2), 0.00) as takeawayPercent
        FROM som_report_master_sale_data
        WHERE del_status = 0 and tenant_id = #{dto.tenantId}
          and sale_time BETWEEN #{dto.startLocalDate} AND #{dto.endLocalDate} and store_id is not null
        GROUP BY store_id ORDER BY store_id asc
    </select>

    <select id="getSaleTotalById" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(total_amount), 0) as saleTotal FROM som_report_master_sale_data
        WHERE tenant_id = #{tenantId} and store_id = #{storeId}
          and sale_time BETWEEN #{periodStart} AND #{periodEnd}
          and del_status = 0
    </select>
    <select id="categoryAnalyseStatistic" resultType="com.seewin.som.report.resp.SaleDataCategoryAnalyseVo">
        select sale_time as date,sum(total_order) as total_order,sum(total_amount) as total_amount,
        sum(store_order) as store_order,sum(store_amount) as store_amount,
        sum(takeaway_order) as takeaway_order,sum(takeaway_amount) as takeaway_amount
        from som_report_master_sale_data
        <where>
            <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
                and tenant_id in
                <foreach collection="dto.tenantIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.categoryIdList != null and dto.categoryIdList.size > 0">
                and category_id in
                <foreach collection="dto.categoryIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.objCode != null and dto.objCode != ''">
                and obj_code = #{dto.objCode}
            </if>
            and sale_time BETWEEN #{dto.startDate} and #{dto.endDate}
            group by sale_time
        </where>
    </select>
    <select id="saleRank" resultType="java.lang.String">
        select brand_name
        from som_report_master_sale_data
        <where>
            <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
                and tenant_id in
                <foreach collection="dto.tenantIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.categoryIdList != null and dto.categoryIdList.size > 0">
                and category_id in
                <foreach collection="dto.categoryIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and sale_time BETWEEN #{dto.startDate} and #{dto.endDate}
        </where>
        group by brand_name
        order by sum(IFNULL(total_amount,0)) desc
        limit 3
    </select>
    <select id="categoryAnalyseStatisticDetail" resultType="com.seewin.som.report.resp.SaleDataCategoryAnalyseVo">
        select sale_time as date,category_id,category_name,sum(total_order) as total_order,sum(total_amount) as total_amount,
        sum(store_order) as store_order,sum(store_amount) as store_amount,
        sum(takeaway_order) as takeaway_order,sum(takeaway_amount) as takeaway_amount
        from som_report_master_sale_data
        <where>
            <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
                and tenant_id in
                <foreach collection="dto.tenantIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.categoryIdList != null and dto.categoryIdList.size > 0">
                and category_id in
                <foreach collection="dto.categoryIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and sale_time BETWEEN #{dto.startDate} and #{dto.endDate}
            group by sale_time,category_id,category_name
        </where>
    </select>

    <select id="getSaleTotalAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(total_amount), 0)
        FROM som_report_master_sale_data
        WHERE sale_time between #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and del_status = 0
    </select>

    <select id="getSaleDistributeList" resultType="com.seewin.som.report.resp.ReportMultiAnalyseVo">
        SELECT tenant_name, commercial_type_name, IFNULL(sum(total_amount), 0) as totalAmount, IFNULL(sum(store_amount), 0) as storeAmount, IFNULL(sum(takeaway_amount), 0) as takeawayAmount
        FROM som_report_master_sale_data
        WHERE sale_time between #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and del_status = 0 and tenant_name is not null and commercial_type_name is not null
        GROUP BY tenant_name, commercial_type_name
    </select>

    <select id="getSaleAmountByDate" resultType="com.seewin.som.report.resp.ReportMultiAnalyseVo">
        SELECT sale_time as currentDate, sale_time as yearMonthDay, IFNULL(sum(total_amount), 0) as totalAmount
        FROM som_report_master_sale_data
        WHERE sale_time between #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and del_status = 0
        GROUP BY sale_time
    </select>
    <select id="getSaleDetailByGatherType" resultType="com.seewin.som.report.resp.ReportMultiAnalyseVo">
        SELECT
        <if test="dto.searchType==0 || dto.searchType==1">
            <if test="dto.searchType==0">
                city city,
            </if>
            <if test="dto.searchType==1">
                tenant_name,
            </if>
            <if test="dto.gatherType==0">
                commercial_type_code ,
                commercial_type_name ,
            </if>
            <if test="dto.gatherType==1">
                commercial_type_code ,
                commercial_type_name ,
                category_id ,
                category_name ,
            </if>
            <if test="dto.gatherType==2">
                commercial_type_code ,
                commercial_type_name ,
                category_id ,
                category_name ,
                commercial_two_id ,
                commercial_two ,
            </if>
        </if>
        <if test="dto.searchType!=0 and dto.searchType!=1">
            store_id,
            commercial_type_code ,
            commercial_type_name ,
            category_id ,
            category_name ,
            commercial_two_id ,
            commercial_two,
            tenant_id,
            tenant_name,
        </if>
               IFNULL(sum(total_amount), 0)    AS totalAmount,
               IFNULL(sum(store_amount), 0)    AS storeAmount,
               IFNULL(sum(takeaway_amount), 0) AS takeawayAmount
        from som_report_master_sale_data
        where sale_time between #{dto.startDate} and #{dto.endDate}
          and del_status = 0
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.searchType==0 || dto.searchType==1">
            <if test="dto.gatherType==0">
                and commercial_type_name is not null
                GROUP BY commercial_type_code,commercial_type_name
                <if test="dto.searchType==0">
                    ,city
                </if>
                <if test="dto.searchType==1">
                    ,tenant_name
                </if>
            </if>

            <if test="dto.gatherType==1">
                and commercial_type_name is not null
                and category_name is not null
                GROUP BY commercial_type_code,commercial_type_name,category_id,category_name
                <if test="dto.searchType==0">
                    ,city
                </if>
                <if test="dto.searchType==1">
                    ,tenant_name
                </if>
            </if>

            <if test="dto.gatherType==2">
                and commercial_type_name is not null
                and category_name is not null
                and commercial_two is not null
                GROUP BY commercial_type_code,commercial_type_name,category_id,category_name,commercial_two_id,commercial_two
                <if test="dto.searchType==0">
                    ,city
                </if>
                <if test="dto.searchType==1">
                    ,tenant_name
                </if>
            </if>
        </if>
        <if test="dto.searchType!=0 and dto.searchType!=1">
            and commercial_type_name is not null
            and category_name is not null
            and commercial_two is not null
            GROUP BY commercial_type_code,commercial_type_name,category_id,category_name,commercial_two_id,commercial_two,store_id,tenant_id,tenant_name
        </if>
    </select>


    <select id="getSaleAnalyseList" resultType="com.seewin.som.report.resp.ReportMultiAnalyseVo">
        SELECT sale_time as currentDate, sale_time as yearMonthDay, DATE_FORMAT(sale_time,'%Y-%m') as yearMonth, commercial_type_name, IFNULL(sum(total_amount), 0) as totalAmount
        FROM som_report_master_sale_data
        WHERE sale_time between #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and del_status = 0 and commercial_type_name is not null
        GROUP BY sale_time, commercial_type_name
    </select>
    <select id="getOperationReport" resultType="com.seewin.som.report.resp.ReportSaleAndFlowListVo">
        SELECT
            t1.tenant_id,
            t1.tenant_name,
            t1.shop_no,
            t1.store_id,
            t1.brand_name,
            sum(t1.total_amount)            total_amount,
            sum(t1.total_order)             total_order,
            sum(t2.front_count)             front_count,
            sum(t2.inshop_count)            inshop_count
        FROM som_report_master_sale_data t1
                 LEFT JOIN (select tenant_id,
                                   obj_code,
                                   statis_time,
                                   sum(front_count)  front_count,
                                   sum(inshop_count) inshop_count
                            from som_report_statis_flow_data
                            where statis_type = 2
                              AND del_status = 0
                            GROUP BY tenant_id, obj_code, statis_time) t2 ON t1.tenant_id = t2.tenant_id
            AND t1.obj_code = t2.obj_code
            AND t1.sale_time = t2.statis_time
        WHERE t1.del_status = 0
          AND IFNULL(t1.store_id, '') <![CDATA[ <> ]]> ''
          and sale_time BETWEEN  #{dto.startDate} and  #{dto.endDate}
        group by t1.tenant_id,
                 t1.tenant_name,
                 t1.shop_no,
                 t1.store_id,
                 t1.brand_name
    </select>

    <select id="saleAmount" resultType="com.seewin.som.report.resp.SaleDataAnalyseVo">
        SELECT
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                sale_time as dateStr,
            </when>
            <otherwise>
                DATE_FORMAT(sale_time,'%Y-%m') as dateStr,
            </otherwise>
        </choose>
        <choose>
            <when test="dto.storeId != null and dto.storeId !=''">
                sum(total_amount) as value, max(use_obj) as useObj, max(brand_name) as brandName
            </when>
            <otherwise>
                sum(total_amount) as value
            </otherwise>
        </choose>
        FROM som_report_master_sale_data
        WHERE del_status = 0
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        <if test="dto.storeId != null and dto.storeId !=''">
            and `store_id` = #{dto.storeId}
        </if>
        and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                GROUP BY sale_time
            </when>
            <otherwise>
                GROUP BY DATE_FORMAT(sale_time,'%Y-%m')
            </otherwise>
        </choose>
        ORDER BY dateStr ASC
    </select>

    <select id="salePredictAmount" resultType="com.seewin.som.report.resp.SaleDataAnalyseVo">
        SELECT
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                predict_date as dateStr,
            </when>
            <otherwise>
                DATE_FORMAT(predict_date,'%Y-%m') as dateStr,
            </otherwise>
        </choose>
        sum(IFNULL(total_amount, 0)) as predictValue
        FROM som_report_predict_project_data
        WHERE del_status = 0
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        and predict_date between #{dto.startLocalDate} and #{dto.endLocalDate}
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                GROUP BY predict_date
            </when>
            <otherwise>
                GROUP BY DATE_FORMAT(predict_date,'%Y-%m')
            </otherwise>
        </choose>
        ORDER BY dateStr ASC
    </select>

    <select id="salePredictStoreAmount" resultType="com.seewin.som.report.resp.SaleDataAnalyseVo">
        SELECT
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                predict_date as dateStr,
            </when>
            <otherwise>
                DATE_FORMAT(predict_date,'%Y-%m') as dateStr,
            </otherwise>
        </choose>
        sum(IFNULL(total_amount, 0)) as predictValue
        FROM som_report_predict_store_data
        WHERE del_status = 0
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        <if test="dto.storeId != null and dto.storeId !=''">
            and `store_id` = #{dto.storeId}
        </if>
        and predict_date between #{dto.startLocalDate} and #{dto.endLocalDate}
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                GROUP BY predict_date
            </when>
            <otherwise>
                GROUP BY DATE_FORMAT(predict_date,'%Y-%m')
            </otherwise>
        </choose>
        ORDER BY dateStr ASC
    </select>

    <select id="getPredictData" resultType="com.seewin.som.report.resp.PredictDataVo">
        SELECT predict_date as dateStr, IFNULL(enter_count, 0) as doorFlow, IFNULL(total_amount, 0) as saleAmount
        FROM som_report_predict_project_data
        WHERE del_status = 0
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        and predict_date = #{dto.predictDate}
        limit 1
    </select>

    <select id="getPredictStoreData" resultType="com.seewin.som.report.resp.PredictDataVo">
        SELECT predict_date as dateStr, IFNULL(front_count, 0) as doorFlow, IFNULL(total_amount, 0) as saleAmount
        FROM som_report_predict_store_data
        WHERE del_status = 0
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        <if test="dto.storeId != null and dto.storeId !=''">
            and `store_id` = #{dto.storeId}
        </if>
        and predict_date = #{dto.predictDate}
        limit 1
    </select>

    <select id="getRecommendSaleTotal" resultType="com.seewin.som.report.resp.ReportSaleRecommendVo">
        SELECT bb.brand_id, aa.incomePercent, aa.sort FROM

        (SELECT t1.brand_name, IF(t2.lastTotalAmount IS NULL OR t2.lastTotalAmount = 0, 0, ROUND( (t1.curTotalAmount - t2.lastTotalAmount)*100 / t2.lastTotalAmount, 2) ) as incomePercent,
        ROW_NUMBER() OVER (ORDER BY IF(t2.lastTotalAmount IS NULL OR t2.lastTotalAmount = 0, 0, ROUND( (t1.curTotalAmount - t2.lastTotalAmount)*100 / t2.lastTotalAmount, 2)) DESC) AS sort
         FROM
             (SELECT brand_name, sum(total_amount) as curTotalAmount FROM som_report_master_sale_data WHERE del_status = 0
             and brand_name is not null and brand_name != '' and store_id is not null and store_id != ''
             and sale_time BETWEEN #{dto.createTimeStart} AND #{dto.createTimeEnd}
             GROUP BY brand_name) t1

               LEFT JOIN

             (SELECT brand_name, sum(total_amount) as lastTotalAmount FROM som_report_master_sale_data WHERE del_status = 0
             and brand_name is not null and brand_name != '' and store_id is not null and store_id != ''
             and sale_time BETWEEN #{dto.createTimeStart} - INTERVAL 7 DAY AND #{dto.createTimeEnd} - INTERVAL 7 DAY
             GROUP BY brand_name) t2

             ON t1.brand_name = t2.brand_name
         ORDER BY incomePercent desc) aa

         LEFT JOIN
            (SELECT name, max(id) as brand_id FROM som_commerce.som_commerce_brand WHERE del_status = 0 GROUP BY name) bb

         ON aa.brand_name = bb.name
    </select>
    <select id="getFlowAmountFeeByProject" resultType="com.seewin.som.report.resp.ManageAnalyseVo">
        WITH sale_data AS (
        SELECT
        <choose>
            <when test="dto.timeType == 0"> DATE(t1.sale_time) </when>
            <when test="dto.timeType == 1"> DATE_FORMAT(t1.sale_time, '%Y-%m') </when>
        </choose> AS sale_time,
        t1.tenant_id,
        t1.store_id,
        t1.obj_code,
        t1.tenant_name,
        t1.city,
        <if test="dto.gatherType >= 0"> t1.commercial_type_name, </if>
        <if test="dto.gatherType >= 1"> t1.category_name, </if>
        <if test="dto.gatherType == 2"> t1.commercial_two, </if>
        <if test="dto.indicatorType == 0 or dto.indicatorType == 2 or dto.indicatorType == 3">
            SUM(t1.total_amount) AS total_amount,
        </if>
        <if test="dto.indicatorType == 1 or dto.indicatorType == 3">
            SUM(t1.total_order) AS total_order,
        </if>
        1 AS dummy
        FROM som_report_master_sale_data t1
        WHERE t1.del_status = 0
        AND t1.sale_time BETWEEN #{dto.startDate} AND #{dto.endDate}
        AND t1.tenant_id IN
        <foreach collection="dto.tenantIdList" item="id" open="(" separator="," close=")">#{id}</foreach>
        <if test="dto.gatherType >= 0"> AND commercial_type_name IS NOT NULL AND commercial_type_name != '' </if>
        <if test="dto.gatherType >= 1"> AND category_name IS NOT NULL AND category_name != '' </if>
        <if test="dto.gatherType == 2"> AND commercial_two IS NOT NULL AND commercial_two != '' </if>
        GROUP BY
        <choose>
            <when test="dto.timeType == 0"> DATE(t1.sale_time) </when>
            <when test="dto.timeType == 1"> DATE_FORMAT(t1.sale_time, '%Y-%m') </when>
        </choose>,
        t1.tenant_id, t1.store_id, t1.obj_code, t1.tenant_name, t1.city
        <if test="dto.gatherType >= 0">, t1.commercial_type_name </if>
        <if test="dto.gatherType >= 1">, t1.category_name </if>
        <if test="dto.gatherType == 2">, t1.commercial_two </if>
        ),

        pre_flow AS (
        SELECT
        tenant_id,
        obj_code,
        <choose>
            <when test="dto.timeType == 0"> DATE(statis_time) </when>
            <when test="dto.timeType == 1"> DATE_FORMAT(statis_time, '%Y-%m') </when>
        </choose> AS sale_time,
        SUM(IFNULL(inshop_count, 0)) AS inshop_count
        FROM som_report_statis_flow_data
        WHERE statis_type = 2
        AND del_status = 0
        AND statis_time BETWEEN #{dto.startDate} AND #{dto.endDate}
        AND tenant_id IN
        <foreach collection="dto.tenantIdList" item="id" open="(" separator="," close=")">#{id}</foreach>
        GROUP BY tenant_id, obj_code, sale_time
        ),

        project_fee AS (
        SELECT
        tenant_id,
        store_id,
        CONCAT_WS('-', year, LPAD(month, 2, '0')) AS year_and_month,
        SUM(IFNULL(theory_rent, 0) + IFNULL(theory_manage, 0)) AS monthly_fee
        FROM som_report_fee
        WHERE del_status = 0
        AND tenant_id IN
        <foreach collection="dto.tenantIdList" item="id" open="(" separator="," close=")">#{id}</foreach>
        GROUP BY tenant_id, store_id, year, month
        ),

        area_data AS (
        SELECT
        <choose>
            <when test="dto.timeType == 0">
                STR_TO_DATE(CONCAT(year, '-', LPAD(month, 2, '0'), '-', LPAD(day, 2, '0')), '%Y-%m-%d') AS sale_time
            </when>
            <when test="dto.timeType == 1">
                DATE_FORMAT(STR_TO_DATE(CONCAT(year, '-', LPAD(month, 2, '0'), '-', LPAD(day, 2, '0')), '%Y-%m-%d'), '%Y-%m') AS sale_time
            </when>
        </choose>,
        tenant_id,
        store_id,
        <if test="dto.gatherType >= 0"> commercial_type_name, </if>
        <if test="dto.gatherType >= 1"> category_name, </if>
        <if test="dto.gatherType == 2"> commercial_two, </if>
        SUM(IFNULL(room_area, 0)) AS total_room_area
        FROM som_report_operate_day
        WHERE del_status = 0
        AND `status` = 2
        AND STR_TO_DATE(CONCAT(year, '-', LPAD(month, 2, '0'), '-', LPAD(day, 2, '0')), '%Y-%m-%d') BETWEEN #{dto.startDate} AND #{dto.endDate}
        AND tenant_id IN
        <foreach collection="dto.tenantIdList" item="id" open="(" separator="," close=")">#{id}</foreach>
        <if test="dto.gatherType >= 0"> AND commercial_type_name IS NOT NULL AND commercial_type_name != '' </if>
        <if test="dto.gatherType >= 1"> AND category_name IS NOT NULL AND category_name != '' </if>
        <if test="dto.gatherType == 2"> AND commercial_two IS NOT NULL AND commercial_two != '' </if>
        GROUP BY sale_time, tenant_id, store_id
        <if test="dto.gatherType >= 0">, commercial_type_name </if>
        <if test="dto.gatherType >= 1">, category_name </if>
        <if test="dto.gatherType == 2">, commercial_two </if>
        ),

        store_level_data AS (
        SELECT
        s.sale_time,
        s.tenant_id,
        s.tenant_name,
        s.city,
        s.store_id,
        <if test="dto.gatherType >= 0"> s.commercial_type_name, </if>
        <if test="dto.gatherType >= 1"> s.category_name, </if>
        <if test="dto.gatherType == 2"> s.commercial_two, </if>
        <if test="dto.indicatorType == 0 or dto.indicatorType == 2 or dto.indicatorType == 3"> s.total_amount, </if>
        <if test="dto.indicatorType == 1 or dto.indicatorType == 3"> s.total_order, IFNULL(f.inshop_count, 0) AS inshop_count, </if>
        <if test="dto.indicatorType == 0 or dto.indicatorType == 3">
            <choose>
                <when test="dto.timeType == 0"> IFNULL(pf.monthly_fee / DAY(LAST_DAY(s.sale_time)), 0) AS theory_rent, </when>
                <when test="dto.timeType == 1"> IFNULL(pf.monthly_fee, 0) AS theory_rent, </when>
            </choose>
        </if>
        <if test="dto.indicatorType == 2 or dto.indicatorType == 3"> ROUND(IFNULL(a.total_room_area, 0), 2) AS total_room_area, </if>
        1 AS dummy
        FROM sale_data s
        <if test="dto.indicatorType == 1 or dto.indicatorType == 3">
            LEFT JOIN pre_flow f ON f.tenant_id = s.tenant_id AND f.obj_code = s.obj_code AND f.sale_time = s.sale_time
        </if>
        <if test="dto.indicatorType == 0 or dto.indicatorType == 3">
            LEFT JOIN project_fee pf ON pf.tenant_id = s.tenant_id AND pf.store_id = s.store_id
            AND pf.year_and_month =
            <choose>
                <when test="dto.timeType == 0"> DATE_FORMAT(s.sale_time, '%Y-%m') </when>
                <when test="dto.timeType == 1"> s.sale_time </when>
            </choose>
        </if>
        <if test="dto.indicatorType == 2 or dto.indicatorType == 3">
            LEFT JOIN area_data a ON a.tenant_id = s.tenant_id AND a.store_id = s.store_id AND a.sale_time = s.sale_time
            <if test="dto.gatherType >= 0"> AND a.commercial_type_name = s.commercial_type_name </if>
            <if test="dto.gatherType >= 1"> AND a.category_name = s.category_name </if>
            <if test="dto.gatherType == 2"> AND a.commercial_two = s.commercial_two </if>
        </if>
        )

        SELECT
        d.sale_time,
        d.tenant_id,
        d.tenant_name,
        d.city,
        <if test="dto.gatherType >= 0"> d.commercial_type_name, </if>
        <if test="dto.gatherType >= 1"> d.category_name, </if>
        <if test="dto.gatherType == 2"> d.commercial_two, </if>
        <if test="dto.indicatorType == 0 or dto.indicatorType == 2 or dto.indicatorType == 3"> SUM(d.total_amount) AS total_amount, </if>
        <if test="dto.indicatorType == 1 or dto.indicatorType == 3"> SUM(d.total_order) AS total_order, SUM(d.inshop_count) AS inshop_count, </if>
        <if test="dto.indicatorType == 0 or dto.indicatorType == 3">
            SUM(d.theory_rent) AS theory_rent,
        </if>
        <if test="dto.indicatorType == 2 or dto.indicatorType == 3">
            SUM(d.total_room_area) AS total_room_area,
        </if>
        1 AS dummy
        FROM store_level_data d
        GROUP BY
        d.sale_time,
        d.tenant_id,
        d.tenant_name,
        d.city
        <if test="dto.gatherType >= 0">, d.commercial_type_name </if>
        <if test="dto.gatherType >= 1">, d.category_name </if>
        <if test="dto.gatherType == 2">, d.commercial_two </if>
        <if test="dto.timeType != 2"> ORDER BY d.sale_time </if>
    </select>

</mapper>
