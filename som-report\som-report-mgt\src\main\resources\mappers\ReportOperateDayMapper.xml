<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.report.mapper.ReportOperateDayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.report.entity.ReportOperateDay">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="room_id" property="roomId" />
        <result column="room_area" property="roomArea" />
        <result column="actual_area" property="actualArea" />
        <result column="day" property="day" />
        <result column="month" property="month" />
        <result column="year" property="year" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
    </resultMap>

    <select id="rentRate" resultType="com.seewin.som.report.resp.ReportMultiDataVo">
        SELECT CONCAT_WS("-", year, LPAD(`month`, 2, '0'), LPAD(`day`, 2, '0')) as dateStr,
        IF( MAX(actual_area) IS NULL OR MAX(actual_area) = 0, 0, ROUND( SUM( IFNULL(room_area, 0) ) * 100 / MAX(actual_area) , 2) ) as value
        FROM som_report_operate_day
        WHERE del_status = 0 and status != 1
        <if test="dto.tenantId != null">
            and `tenant_id` = #{dto.tenantId}
        </if>
        <if test="dto.startYear == dto.endYear">
            and year = #{dto.startYear}
            <if test="dto.startMonth == dto.endMonth">
                and month = #{dto.startMonth}
                and day between #{dto.startDay} and #{dto.endDay}
            </if>
            <if test="dto.startMonth != dto.endMonth">
                and ((month = #{dto.startMonth} and day >= #{dto.startDay}) OR (month = #{dto.endMonth} and day &lt;= #{dto.endDay}))
            </if>
        </if>
        <if test="dto.startYear != dto.endYear">
            and ((year = #{dto.startYear} and month = #{dto.startMonth} and day >= #{dto.startDay}) OR (year = #{dto.endYear} and month = #{dto.endMonth}) and day &lt;= #{dto.endDay})
        </if>
        group by year, month, day
        order by year asc, month asc, day asc
    </select>
    <select id="openRate" resultType="com.seewin.som.report.resp.ReportMultiDataVo">
        SELECT
            STR_TO_DATE( CONCAT( YEAR, '-', MONTH, '-', DAY ), '%Y-%m-%d' ) as dateStr,
            IF( MAX(actual_area) IS NULL OR MAX(actual_area) = 0, 0, ROUND( (SUM( IFNULL(room_area, 0) )* 100) / MAX(actual_area) , 2)  ) as value
        FROM
            som_report_operate_day
        WHERE
            STR_TO_DATE( CONCAT( YEAR, '-', MONTH, '-', DAY ), '%Y-%m-%d' ) BETWEEN #{dto.startLocalDate}
          AND #{dto.endLocalDate}
          and tenant_id = #{dto.tenantId}
          AND STATUS = 2
          AND del_status = 0
        GROUP BY
            STR_TO_DATE( CONCAT( YEAR, '-', MONTH, '-', DAY ), '%Y-%m-%d' )
        ORDER BY
            STR_TO_DATE( CONCAT( YEAR, '-', MONTH, '-', DAY ), '%Y-%m-%d' )
    </select>
    <select id="openArea" resultType="com.seewin.som.report.resp.ReportMultiDataVo">
        SELECT
            STR_TO_DATE( CONCAT( YEAR, '-', MONTH, '-', DAY ), '%Y-%m-%d' ) as dateStr,
            SUM( IFNULL(room_area, 0) ) as value
        FROM
            som_report_operate_day
        WHERE
            STR_TO_DATE( CONCAT( YEAR, '-', MONTH, '-', DAY ), '%Y-%m-%d' ) BETWEEN #{dto.startLocalDate}
          AND #{dto.endLocalDate}
          and tenant_id = #{dto.tenantId}
          AND STATUS = 2
          AND del_status = 0
        GROUP BY
            STR_TO_DATE( CONCAT( YEAR, '-', MONTH, '-', DAY ), '%Y-%m-%d' )
        ORDER BY
            STR_TO_DATE( CONCAT( YEAR, '-', MONTH, '-', DAY ), '%Y-%m-%d' )
    </select>

    <select id="saleSquareStoreContrast" resultType="com.seewin.som.report.resp.ReportMultiDataDetailVo">
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                SELECT aa.store_id, aa.brand_name, aa.storeLocation, aa.formatName, aa.categoryName, IFNULL(aa.moleValue, 0) as moleValue, IFNULL(bb.denValue, 0) as denValue,
                IF(bb.denValue IS NULL OR bb.denValue = 0, 0, ROUND( aa.moleValue / bb.denValue , 2)) as value
                FROM
                (SELECT store_id, max(brand_name) as brand_name, max(use_obj) as storeLocation, max(commercial_type_name) as formatName, max(category_name) as categoryName, sum(IFNULL(total_amount, 0)) as moleValue FROM som_report_master_sale_data
                WHERE del_status = 0
                and `tenant_id` = #{dto.tenantId}
                <if test='dto.storeIdList != null and dto.storeIdList.size > 0'>
                    <foreach collection="dto.storeIdList" open=" AND store_id in ( " close=" ) " item="itemId" separator=",">
                        #{itemId}
                    </foreach>
                </if>
                <if test="dto.fcode != null and dto.fcode !=''">
                    and `obj_code` LIKE CONCAT(#{dto.fcode},'%') and store_id is not null
                </if>
                and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}
                GROUP BY store_id
                ORDER BY store_id ASC) aa

                LEFT JOIN

                (SELECT store_id, sum(IFNULL(room_area, 0)) as denValue
                FROM som_report_operate
                WHERE del_status = 0 and tenant_id = #{dto.tenantId}
                <if test='dto.storeIdList != null and dto.storeIdList.size > 0'>
                    <foreach collection="dto.storeIdList" open=" AND store_id in ( " close=" ) " item="itemId" separator=",">
                        #{itemId}
                    </foreach>
                </if>
                <if test="dto.fcode != null and dto.fcode !=''">
                    and `fcode` LIKE CONCAT(#{dto.fcode},'%') and store_id is not null
                </if>
                and STR_TO_DATE( CONCAT( year, '-', month, '-01'), '%Y-%m-%d' ) between #{dto.startLocalDate} and #{dto.endLocalDate}
                GROUP BY store_id
                ORDER BY store_id ASC) bb

                ON aa.store_id = bb.store_id
            </when>
            <otherwise>
                SELECT aa.store_id, aa.brand_name, aa.storeLocation, aa.formatName, aa.categoryName, IFNULL(aa.moleValue, 0) as moleValue, IFNULL(bb.denValue, 0) as denValue,
                IF(bb.denValue IS NULL OR bb.denValue = 0, 0, ROUND( aa.moleValue / bb.denValue , 2)) as value
                FROM
                (SELECT store_id, max(brand_name) as brand_name, max(use_obj) as storeLocation, max(commercial_type_name) as formatName, max(category_name) as categoryName, sum(IFNULL(total_amount, 0)) as moleValue FROM som_report_master_sale_data
                WHERE del_status = 0
                and `tenant_id` = #{dto.tenantId}
                <if test='dto.storeIdList != null and dto.storeIdList.size > 0'>
                    <foreach collection="dto.storeIdList" open=" AND store_id in ( " close=" ) " item="itemId" separator=",">
                        #{itemId}
                    </foreach>
                </if>
                <if test="dto.fcode != null and dto.fcode !=''">
                    and `obj_code` LIKE CONCAT(#{dto.fcode},'%') and store_id is not null
                </if>
                and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}
                GROUP BY store_id
                ORDER BY store_id ASC) aa

                LEFT JOIN

                (SELECT store_id, sum(IFNULL(room_area, 0)) as denValue
                FROM som_report_operate
                WHERE del_status = 0 and tenant_id = #{dto.tenantId}
                <if test='dto.storeIdList != null and dto.storeIdList.size > 0'>
                    <foreach collection="dto.storeIdList" open=" AND store_id in ( " close=" ) " item="itemId" separator=",">
                        #{itemId}
                    </foreach>
                </if>
                <if test="dto.fcode != null and dto.fcode !=''">
                    and `fcode` LIKE CONCAT(#{dto.fcode},'%') and store_id is not null
                </if>
                and STR_TO_DATE( CONCAT( year, '-', month, '-01'), '%Y-%m-%d' ) between #{dto.startLocalDate} and #{dto.endLocalDate}
                GROUP BY store_id
                ORDER BY store_id ASC) bb

                ON aa.store_id = bb.store_id
            </otherwise>
        </choose>
    </select>

    <select id="saleSquareArea" resultType="java.lang.Double">
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                SELECT ifnull(sum(IFNULL(room_area, 0)), 0) as totalRoomArea
                FROM som_report_operate
                WHERE del_status = 0 and tenant_id = #{dto.tenantId}
                <if test="dto.fcode != null and dto.fcode !=''">
                    and `fcode` LIKE CONCAT(#{dto.fcode},'%')
                </if>
                and STR_TO_DATE( CONCAT( year, '-', month, '-01'), '%Y-%m-%d' ) between #{dto.startLocalDate} and #{dto.endLocalDate}
            </when>
            <otherwise>
                SELECT ifnull(sum(IFNULL(room_area, 0)), 0) as totalRoomArea
                FROM som_report_operate
                WHERE del_status = 0 and tenant_id = #{dto.tenantId}
                <if test="dto.fcode != null and dto.fcode !=''">
                    and `fcode` LIKE CONCAT(#{dto.fcode},'%')
                </if>
                and STR_TO_DATE( CONCAT( year, '-', month, '-01'), '%Y-%m-%d' ) between #{dto.startLocalDate} and #{dto.endLocalDate}
            </otherwise>
        </choose>
    </select>

    <select id="saleSquareTenantDetail" resultType="com.seewin.som.report.resp.ReportMultiDataDetailVo">
        <choose>
            <when test="dto.timeType != null and dto.timeType == 0">
                SELECT aa.fcode, aa.storeLocation, ROUND(IFNULL(aa.totalSum, 0),2) as moleValue, ROUND(IFNULL(bb.totalRoomArea, 0),2) as denValue,
                IF(bb.totalRoomArea IS NULL OR bb.totalRoomArea = 0, 0, ROUND( aa.totalSum / bb.totalRoomArea , 2) ) as value
                FROM

                (SELECT floorCode as fcode, max(floorName) as storeLocation, SUM(IFNULL(total_amount, 0)) as totalSum
                FROM
                (SELECT *, SUBSTRING_INDEX(use_obj, '/',3) as floorName, SUBSTRING_INDEX(obj_code, '.',2) as floorCode
                FROM som_report_master_sale_data
                WHERE del_status = 0 and obj_code IS NOT NULL and tenant_id = #{dto.tenantId}
                and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}) as msd
                GROUP BY msd.floorCode) aa

                LEFT JOIN

                (SELECT floorCode as fcode, sum(IFNULL(room_area, 0)) as totalRoomArea FROM
                (SELECT *, SUBSTRING_INDEX(fcode, '.',2) as floorCode
                FROM som_report_operate_day
                WHERE del_status = 0 and fcode IS NOT NULL and tenant_id = #{dto.tenantId}
                and STR_TO_DATE( CONCAT( year, '-', month, '-', day ), '%Y-%m-%d' ) between #{dto.startLocalDate} and #{dto.endLocalDate}) as sod
                GROUP BY sod.floorCode) bb

                ON aa.fcode = bb.fcode
                ORDER BY aa.fcode ASC
            </when>
            <otherwise>
                SELECT aa.fcode, aa.storeLocation, ROUND(IFNULL(aa.totalSum, 0),2) as moleValue, ROUND(IFNULL(bb.totalRoomArea, 0),2) as denValue,
                IF(bb.totalRoomArea IS NULL OR bb.totalRoomArea = 0, 0, ROUND( aa.totalSum / bb.totalRoomArea , 2) ) as value
                FROM

                (SELECT floorCode as fcode, max(floorName) as storeLocation, SUM(IFNULL(total_amount, 0)) as totalSum
                FROM
                (SELECT *, SUBSTRING_INDEX(use_obj, '/',3) as floorName, SUBSTRING_INDEX(obj_code, '.',2) as floorCode
                FROM som_report_master_sale_data
                WHERE del_status = 0 and obj_code IS NOT NULL and tenant_id = #{dto.tenantId}
                and sale_time between #{dto.startLocalDate} and #{dto.endLocalDate}) as msd
                GROUP BY msd.floorCode) aa

                LEFT JOIN

                (SELECT floorCode as fcode, sum(IFNULL(room_area, 0)) as totalRoomArea FROM
                (SELECT *, SUBSTRING_INDEX(fcode, '.',2) as floorCode
                FROM som_report_operate
                WHERE del_status = 0 and fcode IS NOT NULL and tenant_id = #{dto.tenantId}
                and STR_TO_DATE( CONCAT( year, '-', month, '-01'), '%Y-%m-%d' ) between #{dto.startLocalDate} and #{dto.endLocalDate}) as sod
                GROUP BY sod.floorCode) bb

                ON aa.fcode = bb.fcode
                ORDER BY aa.fcode ASC
            </otherwise>
        </choose>
    </select>

    <select id="getRoomAreaByData" resultType="com.seewin.som.report.resp.ReportMultiDataVo">
        SELECT STR_TO_DATE( CONCAT( year, '-', month, '-', day ), '%Y-%m-%d' ) as dateStr, IFNULL(room_area, 0) as value
        FROM som_report_operate_day
        WHERE del_status = 0 and tenant_id = #{dto.tenantId}
        and `fcode` = #{dto.objCode}
        and STR_TO_DATE( CONCAT( year, '-', month, '-', day ), '%Y-%m-%d' ) between #{dto.startSaleDate} and #{dto.endSaleDate}
        ORDER BY dateStr ASC
    </select>

    <select id="getOperatingRooms" resultType="com.seewin.som.report.resp.ReportOperateDayStatisticVo">
        select STR_TO_DATE(CONCAT(year, '-', month, '-', day), '%Y-%m-%d') as date,count(*)as operatingRoomCount,sum(room_area) as operatingRoomArea
        from som_report_operate_day
        where STR_TO_DATE(CONCAT(year, '-', month, '-', day), '%Y-%m-%d') between #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.categoryIdList != null and dto.categoryIdList.size > 0">
            and category_id in
            <foreach collection="dto.categoryIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and status = 2
        group by date
        having operatingRoomCount > 0
    </select>
    <select id="getOperatingRoomsDetail" resultType="com.seewin.som.report.resp.ReportOperateDayStatisticVo">
        select STR_TO_DATE(CONCAT(year, '-', month, '-', day), '%Y-%m-%d') as date,category_id,category_name,count(*)as operatingRoomCount,sum(room_area) as operatingRoomArea
        from som_report_operate_day
        where STR_TO_DATE(CONCAT(year, '-', month, '-', day), '%Y-%m-%d') between #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="dto.categoryIdList != null and dto.categoryIdList.size > 0">
            and category_id in
            <foreach collection="dto.categoryIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and status = 2
        group by date,category_id,category_name
        having operatingRoomCount > 0
    </select>

    <select id="multiAnalyseList" resultType="com.seewin.som.report.resp.ReportMultiAnalyseVo">
        select t1.*, IFNULL(t2.total_amount,0) as total_amount, IFNULL(t2.total_order,0) as total_order,
               IFNULL(t2.store_amount,0) as store_amount, IFNULL(t2.store_order,0) as store_order,
               IFNULL(t2.takeaway_amount,0) as takeaway_amount, IFNULL(t2.takeaway_order,0) as takeaway_order
        FROM
        (select tenant_id, fcode as fCode, STR_TO_DATE(CONCAT(year, '-', month, '-', day), '%Y-%m-%d') as currentDate,
               fname as fName, tenant_name, room_id,store_id, room_area, commercial_type_code, commercial_type_name,
               category_id, category_name, commercial_two_id, commercial_two
        from som_report_operate_day
        where STR_TO_DATE(CONCAT(year, '-', month, '-', day), '%Y-%m-%d') between #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and del_status = 0
        and status = 2
        order by currentDate asc) as t1

        LEFT JOIN

        (SELECT tenant_id, obj_code, sale_time,
        total_amount, total_order, store_amount, store_order, takeaway_amount, takeaway_order
        FROM som_report_master_sale_data
        WHERE sale_time between #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantIdList != null and dto.tenantIdList.size > 0">
            and tenant_id in
            <foreach collection="dto.tenantIdList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and del_status = 0) as t2

        ON t1.tenant_id = t2.tenant_id AND t1.fcode = t2.obj_code AND t1.currentDate = t2.sale_time

    </select>
    <select id="getOperateDayAndFlowList" resultType="com.seewin.som.report.resp.ReportOperateDayListVo">
        SELECT
            t1.id,
            t1.tenant_id,
            t1.tenant_name,
            t1.room_id,
            t1.STATUS,
            t1.room_area,
            t1.store_id,
            t1.fcode,
            t1.commercial_type_code,
            t1.commercial_type_name,
            t1.category_id,
            t1.category_name,
            t1.YEAR,
            t1.MONTH,
            t1.DAY,
            STR_TO_DATE( CONCAT( t1.YEAR, '-', t1.MONTH, '-', t1.DAY ), '%Y-%m-%d' ) date,
	t2.front_count,
	t2.inshop_count,
	t3.total_amount,
	t3.total_order,
	t4.theory_rent
        FROM
            som_report_operate_day t1
            LEFT JOIN (
            SELECT
            tenant_id,
            obj_code,
            statis_time,
            sum( front_count ) front_count,
            sum( inshop_count ) inshop_count
            FROM
            som_report_statis_flow_data
            WHERE
            statis_type = 2
            AND del_status = 0
            GROUP BY
            tenant_id,
            obj_code,
            statis_time
            ) t2 ON t1.tenant_id = t2.tenant_id
            AND t1.fcode = t2.obj_code
            AND STR_TO_DATE( CONCAT( t1.YEAR, '-', t1.MONTH, '-', t1.DAY ), '%Y-%m-%d' ) = t2.statis_time
            LEFT JOIN som_report_master_sale_data t3 ON t1.tenant_id = t3.tenant_id and  t3.del_status = 0
            AND t1.fcode = t3.obj_code
            AND STR_TO_DATE( CONCAT( t1.YEAR, '-', t1.MONTH, '-', t1.DAY ), '%Y-%m-%d' )= t3.sale_time
            left join som_report_fee t4 on t1.tenant_id=t4.tenant_id and t1.fcode = t4.fcode  and  t1.`year`=t4.`year` and t1.`month`=t4.`month` and t4.del_status=0   and t1.store_id=t4.store_id
        WHERE
            t1.del_status = 0
          AND STR_TO_DATE( CONCAT( t1.YEAR, '-', t1.MONTH, '-', t1.DAY ), '%Y-%m-%d' )  BETWEEN  #{dto.startDate} and #{dto.endDate}
    </select>

    <select id="getAllData" resultType="com.seewin.som.report.resp.ReportOperateDayAnalyseListVo">
        select  * from som_report_operate_day t1
        where org_fname  like '%深圳市瑞维控股有限公司%' and  t1.del_status = 0  and status != 1
        AND STR_TO_DATE( CONCAT( t1.YEAR, '-', t1.MONTH, '-', t1.DAY ), '%Y-%m-%d' )  BETWEEN  #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantNames != null and dto.tenantNames.size() > 0">
            AND tenant_name IN
            <foreach collection="dto.tenantNames" item="tenant" open="(" separator="," close=")">
                #{tenant}
            </foreach>
        </if>
        and tenant_name in ('深圳北站','深圳湾公园')
    </select>

    <select id="getOpenAllData" resultType="com.seewin.som.report.resp.ReportOperateDayAnalyseListVo">
        select  * from som_report_operate_day t1
        where org_fname  like '%深圳市瑞维控股有限公司%' and  t1.del_status = 0  and status = 2
        AND STR_TO_DATE( CONCAT( t1.YEAR, '-', t1.MONTH, '-', t1.DAY ), '%Y-%m-%d' )  BETWEEN  #{dto.startDate} and #{dto.endDate}
        <if test="dto.tenantNames != null and dto.tenantNames.size() > 0">
            AND tenant_name IN
            <foreach collection="dto.tenantNames" item="tenant" open="(" separator="," close=")">
                #{tenant}
            </foreach>
        </if>
        and tenant_name in ('深圳北站','深圳湾公园')
    </select>


    <select id="getCityAllData" resultType="map">
        SELECT
            DATE_FORMAT(date, '%Y-%m-%d') AS date,
            ROUND(AVG(rental_rate), 2) AS value
        FROM (
            SELECT
                tenant_name,
                STR_TO_DATE(CONCAT(year, '-', month, '-', day), '%Y-%m-%d') AS date,
                ROUND(SUM(room_area) / MAX(actual_area) * 100, 2) AS rental_rate
            FROM
            som_report_operate_day t1
            WHERE
                t1.org_fname LIKE '%深圳市瑞维控股有限公司%'
                AND t1.del_status = 0
                AND  t1.status != 1
                AND STR_TO_DATE( CONCAT( t1.YEAR, '-', t1.MONTH, '-', t1.DAY ), '%Y-%m-%d' )  BETWEEN  #{dto.startDate} and #{dto.endDate}
                <if test="dto.tenantNames != null and dto.tenantNames.size() > 0">
                    AND tenant_name IN
                    <foreach collection="dto.tenantNames" item="tenant" open="(" separator="," close=")">
                        #{tenant}
                    </foreach>
                </if>
            GROUP BY t1.tenant_name, t1.year, t1.month, t1.day
        ) AS subquery
        GROUP BY date
        ORDER BY date;
    </select>

    <select id="getCityOpenAllData" resultType="map">
        SELECT
            DATE_FORMAT(date, '%Y-%m-%d') AS date,
            ROUND(AVG(rental_rate), 2) AS value
        FROM (
            SELECT
                tenant_name,
                STR_TO_DATE(CONCAT(year, '-', month, '-', day), '%Y-%m-%d') AS date,
                ROUND(SUM(room_area) / MAX(actual_area) * 100, 2) AS rental_rate
            FROM
            som_report_operate_day t1
            WHERE
                t1.org_fname LIKE '%深圳市瑞维控股有限公司%'
                AND t1.del_status = 0
                AND  t1.status = 2
                AND STR_TO_DATE( CONCAT( t1.YEAR, '-', t1.MONTH, '-', t1.DAY ), '%Y-%m-%d' )  BETWEEN  #{dto.startDate} and #{dto.endDate}
                <if test="dto.tenantNames != null and dto.tenantNames.size() > 0">
                    AND tenant_name IN
                    <foreach collection="dto.tenantNames" item="tenant" open="(" separator="," close=")">
                        #{tenant}
                    </foreach>
                </if>
            GROUP BY t1.tenant_name, t1.year, t1.month, t1.day
        ) AS subquery
        GROUP BY date
        ORDER BY date;
    </select>


</mapper>
